<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}打印文档{% endblock %}</title>
    <style nonce="{{ csp_nonce }}">
        /* 打印样式 */
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: SimSun, "宋体", serif;
            font-size: 12pt;
            line-height: 1.5;
            color: #000;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        .title {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .subtitle {
            font-size: 14pt;
            margin-bottom: 10px;
        }
        .info {
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            width: 100px;
            font-weight: bold;
        }
        .info-value {
            flex: 1;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .section-title {
            font-size: 14pt;
            font-weight: bold;
            margin: 20px 0 10px 0;
        }
        .footer {
            margin-top: 30px;
            border-top: 1px solid #000;
            padding-top: 10px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-item {
            text-align: center;
        }
        .signature-line {
            display: inline-block;
            width: 150px;
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
        }
        .print-info {
            font-size: 10pt;
            color: #666;
            text-align: right;
            margin-top: 20px;
        }
        .normal {
            color: #28a745;
        }
        .abnormal {
            color: #dc3545;
        }
        .photo-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .photo-item {
            width: 150px;
            text-align: center;
        }
        .photo-item img {
            max-width: 100%;
            border: 1px solid #ddd;
        }
        .photo-caption {
            font-size: 10pt;
            margin-top: 5px;
        }
        
        /* 打印时隐藏打印按钮 */
        @d-flex print {
            .no-print {
                display: none;
            }
            body {
                padding: 0;
                margin: 0;
            }
            .container {
                width: 100%;
                max-width: none;
                padding: 0;
            }
        }
        
        /* 打印按钮样式 */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #4e73df;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .print-button:hover {
            background-color: #2e59d9;
        }
    
        /* 打印样式 */
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: SimSun, "宋体", serif;
            font-size: 12pt;
            line-height: 1.5;
            color: #000;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        .title {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .subtitle {
            font-size: 14pt;
            margin-bottom: 10px;
        }
        .info {
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            width: 100px;
            font-weight: bold;
        }
        .info-value {
            flex: 1;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .section-title {
            font-size: 14pt;
            font-weight: bold;
            margin: 20px 0 10px 0;
        }
        .footer {
            margin-top: 30px;
            border-top: 1px solid #000;
            padding-top: 10px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-item {
            text-align: center;
        }
        .signature-line {
            display: inline-block;
            width: 150px;
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
        }
        .print-info {
            font-size: 10pt;
            color: #666;
            text-align: right;
            margin-top: 20px;
        }
        .normal {
            color: #28a745;
        }
        .abnormal {
            color: #dc3545;
        }
        .photo-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .photo-item {
            width: 150px;
            text-align: center;
        }
        .photo-item img {
            max-width: 100%;
            border: 1px solid #ddd;
        }
        .photo-caption {
            font-size: 10pt;
            margin-top: 5px;
        }
        
        /* 打印时隐藏打印按钮 */
        @d-flex print {
            .no-print {
                display: none;
            }
            body {
                padding: 0;
                margin: 0;
            }
            .container {
                width: 100%;
                max-width: none;
                padding: 0;
            }
        }
        
        /* 打印按钮样式 */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #4e73df;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            /* box-shadow: 0 2px 5px rgba(0,0,0,0.2); */ /* 移除阴影效果 */
        }
        .print-button:hover {
            background-color: #2e59d9;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" class="print-button">打印文档</button>
    
    <div class="container">
        <div class="header">
            <div class="title">{% block document_title %}文档标题{% endblock %}</div>
            <div class="subtitle">{% block document_subtitle %}文档副标题{% endblock %}</div>
        </div>
        
        <div class="info">
            {% block document_info %}{% endblock %}
        </div>
        
        <div class="content">
            {% block content %}{% endblock %}
        </div>
        
        <div class="signature">
            {% block signature %}
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>记录人</div>
            </div>
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>审核人</div>
            </div>
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>负责人</div>
            </div>
            {% endblock %}
        </div>
        
        <div class="footer">
            <div>学校食堂日常管理系统</div>
            <div>{{ print_date.strftime('%Y-%m-%d %H:%M') }}</div>
        </div>
        
        <div class="print-info">
            本文档由系统自动生成，打印时间：{{ print_date.strftime('%Y-%m-%d %H:%M:%S') }}
        </div>
    </div>
</body>
</html>
