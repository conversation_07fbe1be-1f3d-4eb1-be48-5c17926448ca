{% extends 'base.html' %}

{% block title %}食堂日常管理仪表盘{% endblock %}

{% from 'daily_management/components/enhanced_image_uploader.html' import enhanced_image_uploader %}
{% from 'daily_management/components/data_visualization.html' import data_visualization_cards %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 仪表盘样式 */
    .dashboard-header {
        background: linear-gradient(to right, #f8f9fc, #ffffff);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .dashboard-header h1 {
        margin: 0;
        color: #4e73df;
        font-weight: 700;
    }

    .dashboard-header .date {
        font-size: 1.1rem;
        color: #858796;
    }

    .feature-card {
        height: 100%;
        transition: all 0.3s;
        border: none;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15);
    }

    .feature-card .card-header {
        background: linear-gradient(to right, rgba(78, 115, 223, 0.1), rgba(255, 255, 255, 0));
        border-bottom: 1px solid #e3e6f0;
        padding: 1rem 1.25rem;
    }

    .feature-card .card-body {
        padding: 1.25rem;
    }

    .feature-card .icon-box {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .feature-card .btn-group {
        margin-top: 1rem;
    }

    .feature-card .btn-group .btn {
        border-radius: 0.25rem;
    }

    .feature-card .btn-group .btn:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .feature-card .btn-group .btn:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 10rem;
        font-size: 0.75rem;
        font-weight: 700;
    }

    .status-bg-success {
        background-color: rgba(28, 200, 138, 0.1);
        color: #1cc88a;
    }

    .status-bg-warning {
        background-color: rgba(246, 194, 62, 0.1);
        color: #f6c23e;
    }

    .status-bg-danger {
        background-color: rgba(231, 74, 59, 0.1);
        color: #e74a3b;
    }

    .status-bg-info {
        background-color: rgba(54, 185, 204, 0.1);
        color: #36b9cc;
    }

    .overview-card {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        margin-bottom: 1.5rem;
    }

    .overview-card .card-body {
        padding: 1.25rem;
    }

    .overview-card .icon {
        font-size: 2rem;
        opacity: 0.3;
    }

    .overview-card .count {
        font-size: 1.5rem;
        font-weight: 700;
    }

    .overview-card .title {
        font-size: 0.8rem;
        font-weight: 700;
        text-transform: uppercase;
        margin-bottom: 0.5rem;
    }

    .issue-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .issue-item {
        padding: 0.75rem 1rem;
        border-start: 4px solid transparent;
        margin-bottom: 0.5rem;
        background-color: #f8f9fc;
        border-radius: 0.25rem;
        transition: all 0.2s;
    }

    .issue-item:hover {
        background-color: #eaecf4;
    }

    .issue-item.priority-high {
        border-start-color: #e74a3b;
    }

    .issue-item.priority-medium {
        border-start-color: #f6c23e;
    }

    .issue-item.priority-low {
        border-start-color: #1cc88a;
    }

    .issue-item .issue-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .issue-item .issue-meta {
        font-size: 0.8rem;
        color: #858796;
    }

    .print-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.25rem;
        font-weight: 500;
        transition: all 0.2s;
    }

    .print-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    /* 仪表盘样式 */
    .dashboard-header {
        background: linear-gradient(to right, #f8f9fc, #ffffff);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        /* box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15); */ /* 移除阴影效果 */
    }

    .dashboard-header h1 {
        margin: 0;
        color: #4e73df;
        font-weight: 700;
    }

    .dashboard-header .date {
        font-size: 1.1rem;
        color: #858796;
    }

    .feature-card {
        height: 100%;
        transition: all 0.3s;
        border: none;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        /* box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15); */ /* 移除阴影效果 */
    }

    .feature-card .card-header {
        background: linear-gradient(to right, rgba(78, 115, 223, 0.1), rgba(255, 255, 255, 0));
        border-bottom: 1px solid #e3e6f0;
        padding: 1rem 1.25rem;
    }

    .feature-card .card-body {
        padding: 1.25rem;
    }

    .feature-card .icon-box {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .feature-card .btn-group {
        margin-top: 1rem;
    }

    .feature-card .btn-group .btn {
        border-radius: 0.25rem;
    }

    .feature-card .btn-group .btn:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .feature-card .btn-group .btn:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 10rem;
        font-size: 0.75rem;
        font-weight: 700;
    }

    .status-bg-success {
        background-color: rgba(28, 200, 138, 0.1);
        color: #1cc88a;
    }

    .status-bg-warning {
        background-color: rgba(246, 194, 62, 0.1);
        color: #f6c23e;
    }

    .status-bg-danger {
        background-color: rgba(231, 74, 59, 0.1);
        color: #e74a3b;
    }

    .status-bg-info {
        background-color: rgba(54, 185, 204, 0.1);
        color: #36b9cc;
    }

    .overview-card {
        border-radius: 0.5rem;
        overflow: hidden;
        /* box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15); */ /* 移除阴影效果 */
        margin-bottom: 1.5rem;
    }

    .overview-card .card-body {
        padding: 1.25rem;
    }

    .overview-card .icon {
        font-size: 2rem;
        opacity: 0.3;
    }

    .overview-card .count {
        font-size: 1.5rem;
        font-weight: 700;
    }

    .overview-card .title {
        font-size: 0.8rem;
        font-weight: 700;
        text-transform: uppercase;
        margin-bottom: 0.5rem;
    }

    .issue-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .issue-item {
        padding: 0.75rem 1rem;
        border-start: 4px solid transparent;
        margin-bottom: 0.5rem;
        background-color: #f8f9fc;
        border-radius: 0.25rem;
        transition: all 0.2s;
    }

    .issue-item:hover {
        background-color: #eaecf4;
    }

    .issue-item.priority-high {
        border-start-color: #e74a3b;
    }

    .issue-item.priority-medium {
        border-start-color: #f6c23e;
    }

    .issue-item.priority-low {
        border-start-color: #1cc88a;
    }

    .issue-item .issue-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .issue-item .issue-meta {
        font-size: 0.8rem;
        color: #858796;
    }

    .print-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.25rem;
        font-weight: 500;
        transition: all 0.2s;
    }

    .print-btn:hover {
        transform: translateY(-2px);
        /* box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); */ /* 移除阴影效果 */
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 仪表盘头部 -->
    <div class="dashboard-header d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-1">{{ school.name }} - 食堂日常管理仪表盘</h1>
            <p class="date mb-0">{{ today.strftime('%Y年%m月%d日') }} {{ ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'][today.weekday()] }}</p>
        </div>
        <div>
            <a href="{{ url_for('daily_management.print_daily_summary', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
               class="btn btn-primary print-btn {{ 'disabled' if not today_log else '' }}"
               {{ 'disabled' if not today_log else '' }}>
                <i class="fas fa-print"></i> 打印今日汇总
            </a>
        </div>
    </div>

    <!-- 日常管理中心卡片 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">
                <i class="fas fa-calendar-alt me-1"></i> 日常管理中心
            </h6>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <a href="{{ url_for('daily_management.logs') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-calendar-day me-1"></i> 进入日志管理
                </a>
                <p class="text-muted mt-2">管理日志、检查记录、陪餐记录等所有日常工作</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-star text-warning me-1"></i> 推荐使用</h5>
                            <p class="card-text">日常管理中心提供了更直观的日期导航体验，所有内容集中在一个页面，减少了页面跳转，让您可以看到整体情况。</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-magic text-info me-1"></i> 自动创建日志</h5>
                            <p class="card-text">无需手动创建日志，系统会自动为您处理，让您专注于记录重要内容。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心功能卡片 -->
    <div class="row mb-4">
        <!-- 检查记录卡片 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card shadow feature-card border-start-primary">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 fw-bold text-primary">检查记录</h6>
                    <span class="status-badge {{ 'status-bg-success' if today_log and inspection_count > 0 else 'status-bg-warning' }}">
                        {{ '已完成' if today_log and inspection_count > 0 else '待完成' }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="icon-box bg-primary text-white mx-auto">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <p class="mb-2">记录晨检、午检和晚检情况，确保食品安全和卫生标准</p>
                    </div>
                    <div class="btn-group d-flex">
                        <a href="{{ url_for('daily_management.auto_inspections') }}" class="btn btn-primary flex-fill">
                            <i class="fas fa-edit me-1"></i> 填写
                        </a>
                        <a href="{{ url_for('daily_management.print_inspection', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
                           class="btn btn-outline-primary flex-fill {{ 'disabled' if not today_log else '' }}">
                            <i class="fas fa-print me-1"></i> 打印
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 陪餐记录卡片 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card shadow feature-card border-start-success">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 fw-bold text-success">陪餐记录</h6>
                    <span class="status-badge {{ 'status-bg-success' if today_log and companion_count > 0 else 'status-bg-warning' }}">
                        {{ '已完成' if today_log and companion_count > 0 else '待完成' }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="icon-box bg-success text-white mx-auto">
                            <i class="fas fa-users"></i>
                        </div>
                        <p class="mb-2">记录领导、教师陪餐情况，收集反馈意见</p>
                    </div>
                    <div class="btn-group d-flex mb-2">
                        <a href="{{ url_for('daily_management.auto_companions') }}" class="btn btn-success flex-fill">
                            <i class="fas fa-edit me-1"></i> 填写
                        </a>
                        <a href="{{ url_for('daily_management.print_companion', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
                           class="btn btn-outline-success flex-fill {{ 'disabled' if not today_log else '' }}">
                            <i class="fas fa-print me-1"></i> 打印
                        </a>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('daily_management.school_qrcode') }}" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-qrcode me-1"></i> 生成学校陪餐二维码
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 培训记录卡片 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card shadow feature-card border-start-info">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 fw-bold text-info">培训记录</h6>
                    <span class="status-badge status-bg-info">必要时填写</span>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="icon-box bg-info text-white mx-auto">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <p class="mb-2">记录食堂员工培训情况，提高服务质量</p>
                    </div>
                    <div class="btn-group d-flex">
                        <a href="{{ url_for('daily_management.auto_trainings') }}" class="btn btn-info flex-fill">
                            <i class="fas fa-edit me-1"></i> 填写
                        </a>
                        <a href="{{ url_for('daily_management.print_training', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
                           class="btn btn-outline-info flex-fill {{ 'disabled' if not today_log else '' }}">
                            <i class="fas fa-print me-1"></i> 打印
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 特殊事件卡片 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card shadow feature-card border-start-secondary">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 fw-bold text-secondary">特殊事件</h6>
                    <span class="status-badge status-bg-info">必要时填写</span>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="icon-box bg-secondary text-white mx-auto">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <p class="mb-2">记录食堂特殊事件，如重要接待、节日餐等</p>
                    </div>
                    <div class="btn-group d-flex">
                        <a href="{{ url_for('daily_management.auto_events') }}" class="btn btn-secondary flex-fill">
                            <i class="fas fa-edit me-1"></i> 填写
                        </a>
                        <a href="{{ url_for('daily_management.print_event', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
                           class="btn btn-outline-secondary flex-fill {{ 'disabled' if not today_log else '' }}">
                            <i class="fas fa-print me-1"></i> 打印
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 问题记录卡片 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card shadow feature-card border-start-danger">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 fw-bold text-danger">问题记录</h6>
                    <span class="status-badge {{ 'status-bg-danger' if pending_issues and pending_issues|length > 0 else 'status-bg-info' }}">
                        {{ pending_issues|length if pending_issues else '0' }} 个待处理
                    </span>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="icon-box bg-danger text-white mx-auto">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <p class="mb-2">记录和跟踪问题处理，确保及时解决</p>
                    </div>
                    <div class="btn-group d-flex">
                        <a href="{{ url_for('daily_management.auto_issues') }}" class="btn btn-danger flex-fill">
                            <i class="fas fa-edit me-1"></i> 填写
                        </a>
                        <a href="{{ url_for('daily_management.print_issue', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
                           class="btn btn-outline-danger flex-fill {{ 'disabled' if not today_log else '' }}">
                            <i class="fas fa-print me-1"></i> 打印
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志汇总卡片 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card shadow feature-card border-start-dark">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 fw-bold text-dark">日志汇总</h6>
                    <span class="status-badge {{ 'status-bg-success' if today_log else 'status-bg-warning' }}">
                        {{ '已创建' if today_log else '未创建' }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="icon-box bg-dark text-white mx-auto">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <p class="mb-2">生成包含所有记录的综合日志报告</p>
                    </div>
                    <div class="btn-group d-flex">
                        <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="btn btn-dark flex-fill">
                            <i class="fas fa-edit me-1"></i> {{ '编辑' if today_log else '创建' }}
                        </a>
                        <a href="{{ url_for('daily_management.print_daily_summary', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
                           class="btn btn-outline-dark flex-fill {{ 'disabled' if not today_log else '' }}">
                            <i class="fas fa-print me-1"></i> 打印
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据可视化和问题记录区域 -->
    <div class="row">
        <!-- 数据可视化区域 -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold text-primary">数据统计</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">图表操作:</div>
                            <a class="dropdown-item" href="#" id="refreshCharts">
                                <i class="fas fa-sync-alt fa-sm fa-fw me-2 text-gray-400"></i>刷新数据
                            </a>
                            <a class="dropdown-item" href="#" id="exportChartData">
                                <i class="fas fa-download fa-sm fa-fw me-2 text-gray-400"></i>导出数据
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="#" id="printCharts">
                                <i class="fas fa-print fa-sm fa-fw me-2 text-gray-400"></i>打印图表
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="dataVisualizationTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="diners-tab" data-bs-toggle="tab" href="#diners" role="tab" aria-controls="diners" aria-selected="true">
                                <i class="fas fa-users me-1"></i> 就餐人数
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="inspections-tab" data-bs-toggle="tab" href="#inspections" role="tab" aria-controls="inspections" aria-selected="false">
                                <i class="fas fa-clipboard-check me-1"></i> 检查记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="issues-tab" data-bs-toggle="tab" href="#issues" role="tab" aria-controls="issues" aria-selected="false">
                                <i class="fas fa-exclamation-circle me-1"></i> 问题统计
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="dataVisualizationTabContent">
                        <div class="tab-pane fade show active" id="diners" role="tabpanel" aria-labelledby="diners-tab">
                            <div class="chart-filters mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="dinersDateRange">日期范围</label>
                                            <select class="form-control" id="dinersDateRange">
                                                <option value="7">最近7天</option>
                                                <option value="30" selected>最近30天</option>
                                                <option value="90">最近3个月</option>
                                                <option value="180">最近6个月</option>
                                                <option value="custom">自定义...</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="dinersChartType">图表类型</label>
                                            <select class="form-control" id="dinersChartType">
                                                <option value="bar" selected>柱状图</option>
                                                <option value="line">折线图</option>
                                                <option value="stacked">堆叠图</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-container" style="position: relative; height:300px;">
                                <canvas id="dinersChart"></canvas>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="inspections" role="tabpanel" aria-labelledby="inspections-tab">
                            <div class="chart-filters mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="inspectionDateRange">日期范围</label>
                                            <select class="form-control" id="inspectionDateRange">
                                                <option value="7">最近7天</option>
                                                <option value="30" selected>最近30天</option>
                                                <option value="90">最近3个月</option>
                                                <option value="180">最近6个月</option>
                                                <option value="custom">自定义...</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="inspectionChartType">图表类型</label>
                                            <select class="form-control" id="inspectionChartType">
                                                <option value="bar" selected>柱状图</option>
                                                <option value="pie">饼图</option>
                                                <option value="doughnut">环形图</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-container" style="position: relative; height:300px;">
                                <canvas id="inspectionChart"></canvas>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="issues" role="tabpanel" aria-labelledby="issues-tab">
                            <div class="chart-filters mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="issuesDateRange">日期范围</label>
                                            <select class="form-control" id="issuesDateRange">
                                                <option value="7">最近7天</option>
                                                <option value="30" selected>最近30天</option>
                                                <option value="90">最近3个月</option>
                                                <option value="180">最近6个月</option>
                                                <option value="custom">自定义...</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="issuesChartType">图表类型</label>
                                            <select class="form-control" id="issuesChartType">
                                                <option value="bar" selected>柱状图</option>
                                                <option value="pie">饼图</option>
                                                <option value="line">折线图</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-container" style="position: relative; height:300px;">
                                <canvas id="issuesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 问题记录区域 -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold text-danger">待处理问题</h6>
                    <a href="{{ url_for('daily_management.auto_issues') }}" class="btn btn-sm btn-danger">
                        <i class="fas fa-plus me-1"></i> 新增问题
                    </a>
                </div>
                <div class="card-body">
                    {% if pending_issues and pending_issues|length > 0 %}
                    <div class="issue-list">
                        {% for issue in pending_issues %}
                        <div class="issue-item priority-{{ issue.priority|default('medium') }}">
                            <div class="issue-title">{{ issue.title }}</div>
                            <div class="issue-meta">
                                <span><i class="fas fa-calendar-alt me-1"></i>
                                    {% if issue.found_time is string %}
                                        {{ issue.found_time }}
                                    {% else %}
                                        {{ issue.found_time.strftime('%Y-%m-%d') }}
                                    {% endif %}
                                </span>
                                <span class="ms-2"><i class="fas fa-user me-1"></i> {{ issue.reporter_name|default('未知') }}</span>
                                <span class="ms-2">
                                    <i class="fas fa-flag me-1"></i>
                                    {% if issue.priority == 'high' %}
                                    <span class="text-danger">高优先级</span>
                                    {% elif issue.priority == 'medium' %}
                                    <span class="text-warning">中优先级</span>
                                    {% else %}
                                    <span class="text-success">低优先级</span>
                                    {% endif %}
                                </span>
                            </div>
                            <div class="mt-2">
                                <a href="{{ url_for('daily_management.view_issue', issue_id=issue.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i> 查看
                                </a>
                                <a href="{{ url_for('daily_management.edit_issue', issue_id=issue.id) }}" class="btn btn-sm btn-outline-success ms-1">
                                    <i class="fas fa-edit me-1"></i> 处理
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">暂无待处理问题</p>
                    </div>
                    {% endif %}

                    <div class="text-center mt-3">
                        <a href="{{ url_for('daily_management.auto_issues') }}" class="btn btn-link">查看所有问题</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近工作日志 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold text-primary">最近工作日志</h6>
                    <a href="{{ url_for('daily_management.logs') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-list me-1"></i> 查看全部
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_logs %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>管理员</th>
                                    <th>就餐人数</th>
                                    <th>检查记录</th>
                                    <th>陪餐记录</th>
                                    <th>问题记录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>
                                        {% if log.log_date is string %}
                                            {{ log.log_date }}
                                        {% else %}
                                            {{ log.log_date.strftime('%Y-%m-%d') }}
                                        {% endif %}
                                    </td>
                                    <td>{{ log.manager or '未设置' }}</td>
                                    <td>{{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}</td>
                                    <td>{{ log.inspection_count|default(0) }}</td>
                                    <td>{{ log.companion_count|default(0) }}</td>
                                    <td>{{ log.issue_count|default(0) }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date.strftime('%Y-%m-%d') if log.log_date is not string else log.log_date) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('daily_management.print_daily_summary', date=log.log_date.strftime('%Y-%m-%d') if log.log_date is not string else log.log_date) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <p class="text-muted">暂无工作日志记录</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 自定义日期范围模态框 -->
<div class="modal fade" id="customDateRangeModal" tabindex="-1" role="dialog" aria-labelledby="customDateRangeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customDateRangeModalLabel">自定义日期范围</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="startDate">开始日期</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="mb-3">
                    <label for="endDate">结束日期</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="applyCustomDateRange">应用</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否已加载Chart.js
        if (typeof Chart === 'undefined') {
            // 加载Chart.js
            const chartScript = document.createElement('script');
            chartScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";
            chartScript.onload = loadCustomCharts;
            document.head.appendChild(chartScript);
        } else {
            loadCustomCharts();
        }

        function loadCustomCharts() {
            // 加载自定义图表脚本
            const customChartScript = document.createElement('script');
            customChartScript.src = "{{ url_for('static', filename='js/daily-management-charts.js') }}";
            customChartScript.onload = initCharts;
            document.head.appendChild(customChartScript);
        }

        function initCharts() {
            // 初始化图表
            const charts = new DailyManagementCharts();
            charts.initAllCharts();

            // 绑定事件
            bindChartEvents(charts);
        }

        function bindChartEvents(charts) {
            // 刷新图表
            document.getElementById('refreshCharts')?.addEventListener('click', function() {
                charts.initAllCharts();
            });

            // 日期范围变化
            document.getElementById('dinersDateRange')?.addEventListener('change', function() {
                if (this.value === 'custom') {
                    $('#customDateRangeModal').modal.show();
                    document.getElementById('applyCustomDateRange').dataset.target = 'diners';
                } else {
                    const days = parseInt(this.value);
                    charts.initDinersChart('dinersChart', {
                        start_date: charts.getDateString(-days),
                        end_date: charts.getDateString(0)
                    });
                }
            });

            // 图表类型变化
            document.getElementById('dinersChartType')?.addEventListener('change', function() {
                const chartType = this.value;
                const dateRange = document.getElementById('dinersDateRange').value;
                const days = dateRange !== 'custom' ? parseInt(dateRange) : 30;

                charts.initDinersChart('dinersChart', {
                    start_date: charts.getDateString(-days),
                    end_date: charts.getDateString(0),
                    chart_type: chartType
                });
            });

            // 类似地绑定其他图表的事件
            // ...

            // 应用自定义日期范围
            document.getElementById('applyCustomDateRange')?.addEventListener('click', function() {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                if (!startDate || !endDate) {
                    alert('请选择开始和结束日期');
                    return;
                }

                const target = this.dataset.target;
                if (target === 'diners') {
                    charts.initDinersChart('dinersChart', {
                        start_date: startDate,
                        end_date: endDate
                    });
                } else if (target === 'inspection') {
                    charts.initInspectionChart('inspectionChart', {
                        start_date: startDate,
                        end_date: endDate
                    });
                } else if (target === 'issues') {
                    charts.initIssueChart('issuesChart', {
                        start_date: startDate,
                        end_date: endDate
                    });
                }

                $('#customDateRangeModal').modal.hide();
            });
        }
    });
</script>
{% endblock %}
