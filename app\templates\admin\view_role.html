{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system.edit_role', id=role.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> 编辑角色
        </a>
        <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-info">
            <i class="fas fa-key"></i> 编辑权限
        </a>
        {% if role.name %}{% if variable not in ['系统管理员', '管理员', '超级管理员'] and role.users.count() == 0   %}
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteRoleModal">
            <i class="fas fa-trash"></i> 删除角色
        </button>
        {% endif %}{% if {% if role.name  endif %}
        <a href="{{ url_for('system.roles') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">角色信息</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="w-30">角色ID</th>
                        <td>{{ role.id }}</td>
                    </tr>
                    <tr>
                        <th>角色名称</th>
                        <td>{{ role.name }}</td>
                    </tr>
                    <tr>
                        <th>角色描述</th>
                        <td>{{ role.description or '无' }}</td>
                    </tr>
                    <tr>
                        <th>创建时间</th>
                        <td>{{  role.created_at|format_datetime  }}</td>
                    </tr>
                    <tr>
                        <th>关联用户数</th>
                        <td>{{ role.users.count() }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">权限信息</h5>
            </div>
            <div class="card-body">
                {% if permissions %}
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead>
                            <tr>
                                <th>模块</th>
                                <th>权限</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for module, actions in permissions.items() %}
                            <tr>
                                <td>
                                    {% if module == '*' %}
                                    <span class="badge bg-danger">全局权限</span>
                                    {% elif module == 'user' %}
                                    <span class="badge bg-primary">用户管理</span>
                                    {% elif module == 'role' %}
                                    <span class="badge bg-success">角色管理</span>
                                    {% elif module == 'area' %}
                                    <span class="badge bg-info">区域管理</span>
                                    {% elif module == 'supplier' %}
                                    <span class="badge bg-warning">供应商管理</span>
                                    {% elif module == 'ingredient' %}
                                    <span class="badge bg-secondary">食材管理</span>
                                    {% elif module == 'menu' %}
                                    <span class="badge bg-dark">食谱管理</span>
                                    {% elif module == 'sample' %}
                                    <span class="badge bg-light">留样管理</span>
                                    {% elif module == 'setting' %}
                                    <span class="badge bg-primary">系统设置</span>
                                    {% elif module == 'log' %}
                                    <span class="badge bg-secondary">日志管理</span>
                                    {% elif module == 'report' %}
                                    <span class="badge bg-info">报表管理</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ module }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% for action in actions %}
                                        {% if action == '*' %}
                                        <span class="badge bg-danger">所有操作</span>
                                        {% elif action == 'view' %}
                                        <span class="badge bg-info">查看</span>
                                        {% elif action == 'create' %}
                                        <span class="badge bg-success">创建</span>
                                        {% elif action == 'edit' %}
                                        <span class="badge bg-primary">编辑</span>
                                        {% elif action == 'delete' %}
                                        <span class="badge bg-danger">删除</span>
                                        {% elif action == 'approve' %}
                                        <span class="badge bg-warning">审核</span>
                                        {% elif action == 'export' %}
                                        <span class="badge bg-secondary">导出</span>
                                        {% elif action == 'print' %}
                                        <span class="badge bg-dark">打印</span>
                                        {% elif action == 'change_status' %}
                                        <span class="badge bg-warning">修改状态</span>
                                        {% elif action == 'reset_password' %}
                                        <span class="badge bg-danger">重置密码</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ action }}</span>
                                        {% endif %}
                                    {% endfor %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-2">
                    <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-sm btn-info">
                        <i class="fas fa-key"></i> 编辑权限
                    </a>
                    <a href="{{ url_for('role_permissions_fix.fix_role_permissions', role_id=role.id) }}" class="btn btn-sm btn-warning">
                        <i class="fas fa-tools"></i> 修复权限
                    </a>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <p>该角色未配置权限</p>
                    <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-sm btn-info">
                        <i class="fas fa-key"></i> 添加权限
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">关联用户 ({{  users|length  }})</h5>
    </div>
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>真实姓名</th>
                        <th>电子邮箱</th>
                        <th>所属区域</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.real_name }}</td>
                        <td>{{ user.email }}</td>
                        <td>{{ user.area.name if user.area else '无' }}</td>
                        <td>
                            {% if user.status == 1 %}
                            <span class="badge bg-success">启用</span>
                            {% else %}
                            <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('system.view_user', id=user.id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i> 查看
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <p>暂无关联用户</p>
        </div>
        {% endif %}
    </div>
</div>

{% if role.name not in ['系统管理员', '管理员', '超级管理员'] and role.users.count() == 0 %}
<!-- 删除角色确认模态框 -->
<div class="modal fade" id="deleteRoleModal" tabindex="-1" role="dialog" aria-labelledby="deleteRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteRoleModalLabel">确认删除角色</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除角色 <strong>{{ role.name }}</strong> 吗？</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 警告：
                    <ul>
                        <li>此操作不可逆，删除后无法恢复</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('system.delete_role', id=role.id) }}" method="POST"><button type="submit" class="btn btn-danger">确认删除</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
