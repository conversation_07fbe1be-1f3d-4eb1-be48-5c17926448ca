{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-qrcode me-2"></i>{{ title }}
        </h1>
        <div class="btn-group">
            <a href="{{ url_for('daily_management.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>返回
            </a>
        </div>
    </div>

    <!-- 说明信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>使用说明</h5>
                <p class="mb-2">这是 <strong>{{ school.name }}</strong> 的固定检查记录二维码，员工和管理员可以扫码进行以下操作：</p>
                <ul class="mb-0">
                    <li><strong>员工扫码</strong>：选择日期和检查时间，上传检查照片</li>
                    <li><strong>管理员扫码</strong>：选择日期和检查时间，对照片进行评分</li>
                    <li><strong>无需登录</strong>：所有操作都支持匿名访问</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 二维码展示 -->
    <div class="row">
        <!-- 员工上传二维码 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="m-0">
                        <i class="fas fa-camera me-2"></i>员工上传二维码
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <p class="text-muted">员工扫描此二维码上传检查照片</p>
                    </div>
                    
                    <!-- 二维码图片 -->
                    <div class="qr-code-container mb-4">
                        <img src="data:image/png;base64,{{ upload_qrcode_base64 }}" 
                             alt="员工上传二维码" 
                             class="img-fluid border rounded"
                             style="max-width: 300px;">
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="btn-group-vertical w-100">
                        <button class="btn btn-outline-primary" data-onclick="copyToClipboard("{{ upload_url }}')">
                            <i class="fas fa-copy me-2"></i>复制链接
                        </button>
                        <a href="{{ upload_url }}" target="_blank" class="btn btn-outline-secondary">
                            <i class="fas fa-external-link-alt me-2"></i>预览页面
                        </a>
                        <button class="btn btn-outline-success" data-onclick="downloadQRCode("upload', '{{ school.name }}-员工上传二维码')">
                            <i class="fas fa-download me-2"></i>下载二维码
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理员评分二维码 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="m-0">
                        <i class="fas fa-star me-2"></i>管理员评分二维码
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <p class="text-muted">管理员扫描此二维码对照片进行评分</p>
                    </div>
                    
                    <!-- 二维码图片 -->
                    <div class="qr-code-container mb-4">
                        <img src="data:image/png;base64,{{ rate_qrcode_base64 }}" 
                             alt="管理员评分二维码" 
                             class="img-fluid border rounded"
                             style="max-width: 300px;">
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="btn-group-vertical w-100">
                        <button class="btn btn-outline-primary" data-onclick="copyToClipboard('{{ rate_url }}')">
                            <i class="fas fa-copy me-2"></i>复制链接
                        </button>
                        <a href="{{ rate_url }}" target="_blank" class="btn btn-outline-secondary">
                            <i class="fas fa-external-link-alt me-2"></i>预览页面
                        </a>
                        <button class="btn btn-outline-success" data-onclick="downloadQRCode('rate', '{{ school.name }}-管理员评分二维码')">
                            <i class="fas fa-download me-2"></i>下载二维码
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 打印按钮 -->
    <div class="row">
        <div class="col-12 text-center">
            <button class="btn btn-lg btn-primary" data-onclick="printQRCodes()">
                <i class="fas fa-print me-2"></i>打印二维码
            </button>
        </div>
    </div>
</div>

<!-- 成功提示模态框 -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>操作成功
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="successMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showSuccess('链接已复制到剪贴板');
    }).catch(function(err) {
        console.error('复制失败:', err);
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showSuccess('链接已复制到剪贴板');
        } catch (err) {
            alert('复制失败，请手动复制链接');
        }
        document.body.removeChild(textArea);
    });
}

// 下载二维码
function downloadQRCode(type, filename) {
    const imgSelector = type === 'upload' ? 
        'img[alt="员工上传二维码"]' : 
        'img[alt="管理员评分二维码"]';
    
    const img = document.querySelector(imgSelector);
    if (!img) {
        alert('找不到二维码图片');
        return;
    }
    
    // 创建canvas来转换图片
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;
    
    ctx.drawImage(img, 0, 0);
    
    // 下载
    canvas.toBlob(function(blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename + '.png';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showSuccess('二维码已下载');
    });
}

// 打印二维码
function printQRCodes() {
    window.print();
}

// 显示成功消息
function showSuccess(message) {
    document.getElementById('successMessage').textContent = message;
    $('#successModal').modal.show();
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加打印样式
    const style = document.createElement('style');
    style.textContent = `
        @d-flex print {
            .btn, .alert, .card-header, .modal { display: none !important; }
            .card { border: 2px solid #000 !important; page-break-inside: avoid; }
            .qr-code-container img { max-width: 200px !important; }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>