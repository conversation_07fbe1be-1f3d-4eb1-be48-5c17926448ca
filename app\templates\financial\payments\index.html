{% extends "financial/base.html" %}

{% block page_title %}付款记录管理{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item active">付款记录管理</span>
{% endblock %}

{% block page_actions %}
<a href="{{ url_for('financial.create_payment') }}" class="uf-btn uf-btn-primary">
    <i class="fas fa-plus uf-icon"></i> 新建付款记录
</a>
{% endblock %}

{% block financial_content %}
<!-- 用友财务软件风格搜索表单 -->
<div style="background: #f8f9fa; border: 1px solid #c0c0c0; padding: 8px; margin-bottom: 8px; border-radius: 1px;">
    <form method="GET" style="margin: 0;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 8px; align-items: end;">
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">关键词：</label>
                <input type="text" id="keyword" name="keyword" value="{{ keyword }}"
                       placeholder="付款号或摘要"
                       style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">供应商：</label>
                <select id="supplier_id" name="supplier_id" style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
                    <option value="">-- 所有供应商 --</option>
                    {% for supplier in suppliers %}
                    <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                        {{ supplier.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">付款方式：</label>
                <select id="payment_method" name="payment_method" style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
                    <option value="">-- 所有方式 --</option>
                    <option value="现金" {% if payment_method == '现金' %}selected{% endif %}>现金</option>
                    <option value="银行转账" {% if payment_method == '银行转账' %}selected{% endif %}>银行转账</option>
                    <option value="支票" {% if payment_method == '支票' %}selected{% endif %}>支票</option>
                    <option value="其他" {% if payment_method == '其他' %}selected{% endif %}>其他</option>
                </select>
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">开始日期：</label>
                <input type="date" id="start_date" name="start_date" value="{{ start_date }}"
                       style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">结束日期：</label>
                <input type="date" id="end_date" name="end_date" value="{{ end_date }}"
                       style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div style="display: flex; gap: 4px;">
                <button type="submit" class="uf-btn uf-btn-primary uf-btn-sm">
                    <i class="fas fa-search" style="font-size: 10px;"></i> 查询
                </button>
                <a href="{{ url_for('financial.payments_index') }}" class="uf-btn uf-btn-sm">
                    <i class="fas fa-undo" style="font-size: 10px;"></i> 重置
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 用友财务软件专业付款记录列表 -->
<div class="uf-card">
    <div class="uf-card-header">
        <span>
            <i class="fas fa-hand-holding-usd uf-icon"></i> 付款记录列表
            <span style="margin-left: 6px; padding: 1px 4px; background: var(--uf-info); color: white; border-radius: 1px; font-size: 10px;">
                共 {{ payments.total }} 条记录
            </span>
        </span>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        {% if payments.items %}
        <table class="uf-table" style="margin: 0;">
            <thead>
                <tr>
                    <th style="width: 110px;">付款编号</th>
                    <th style="width: 80px;">付款日期</th>
                    <th style="width: 120px;">供应商</th>
                    <th style="width: 110px;">应付账款号</th>
                    <th style="width: 90px;">付款金额</th>
                    <th style="width: 70px;">付款方式</th>
                    <th style="width: 120px;">摘要</th>
                    <th style="width: 60px;">创建人</th>
                    <th style="width: 80px;">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for payment in payments.items %}
                <tr>
                    <td class="text-center">
                        <code class="uf-code">{{ payment.payment_number }}</code>
                    </td>
                    <td class="text-center" style="font-size: 11px;">{{ payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '未知' }}</td>
                    <td class="text-start" style="font-size: 11px;">{{ payment.supplier.name if payment.supplier else '未知供应商' }}</td>
                    <td class="text-center">
                        {% if payment.payable %}
                        <code class="uf-code uf-code-warning">{{ payment.payable.payable_number }}</code>
                        {% else %}
                        <span style="color: #999;">-</span>
                        {% endif %}
                    </td>
                    <td class="uf-amount-col">{{ "%.2f"|format(payment.amount) }}</td>
                    <td class="text-center">
                        <span style="background: #e6f2ff; color: var(--uf-primary); border: 1px solid #b3d9ff; padding: 1px 3px; border-radius: 1px; font-size: 10px; white-space: nowrap;">{{ payment.payment_method }}</span>
                    </td>
                    <td class="text-start" style="font-size: 11px; max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{ payment.summary }}">{{ payment.summary }}</td>
                    <td class="text-center" style="font-size: 11px;">{{ payment.creator.username if payment.creator else '未知' }}</td>
                    <td class="text-center">
                        <div class="uf-btn-group">
                            <button class="uf-btn uf-btn-sm uf-btn-info" title="查看详情"
                                    onclick="viewPayment({{ payment.id }})" style="padding: 1px 4px;">
                                <i class="fas fa-eye" style="font-size: 10px;"></i>
                            </button>
                            {% if payment.voucher_id %}
                            <a href="{{ url_for('financial.view_voucher', id=payment.voucher_id) }}"
                               class="uf-btn uf-btn-sm uf-btn-success" title="查看凭证" style="padding: 1px 4px;">
                                <i class="fas fa-file-invoice" style="font-size: 10px;"></i>
                            </a>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr style="background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%); font-weight: 600;">
                    <td colspan="4" class="text-end" style="font-size: 11px;">合计金额：</td>
                    <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(payments.items|sum(attribute='amount')) }}</td>
                    <td colspan="4"></td>
                </tr>
            </tfoot>
        </table>

        <!-- 用友风格分页 -->
        {% if payments.pages > 1 %}
        <div class="uf-pagination">
            {% if payments.has_prev %}
            <span class="uf-page-item">
                <a class="uf-page-link" href="{{ url_for('financial.payments_index', page=payments.prev_num, keyword=keyword, supplier_id=supplier_id, payment_method=payment_method, start_date=start_date, end_date=end_date) }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </span>
            {% endif %}

            {% for page_num in payments.iter_pages() %}
                {% if page_num %}
                    {% if page_num != payments.page %}
                    <span class="uf-page-item">
                        <a class="uf-page-link" href="{{ url_for('financial.payments_index', page=page_num, keyword=keyword, supplier_id=supplier_id, payment_method=payment_method, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                    </span>
                    {% else %}
                    <span class="uf-page-item active">
                        <span class="uf-page-link">{{ page_num }}</span>
                    </span>
                    {% endif %}
                {% else %}
                <span class="uf-page-item disabled">
                    <span class="uf-page-link">…</span>
                </span>
                {% endif %}
            {% endfor %}

            {% if payments.has_next %}
            <span class="uf-page-item">
                <a class="uf-page-link" href="{{ url_for('financial.payments_index', page=payments.next_num, keyword=keyword, supplier_id=supplier_id, payment_method=payment_method, start_date=start_date, end_date=end_date) }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </span>
            {% endif %}
        </div>
        {% endif %}
        {% else %}
        <div class="uf-empty-state">
            <i class="fas fa-hand-holding-usd"></i>
            <p>暂无付款记录数据</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
function viewPayment(paymentId) {
    ufShowMessage('查看付款记录详情功能待实现，ID: ' + paymentId, 'info');
}
</script>
{% endblock %}
