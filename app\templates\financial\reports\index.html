{% extends "financial/base.html" %}

{% block page_title %}财务管理{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item active">财务管理</span>
{% endblock %}

{% block financial_content %}
<!-- 用友财务软件风格概览 -->
<div style="background: linear-gradient(135deg, #0066cc 0%, #004499 100%); color: white; padding: 12px; margin-bottom: 8px; border-radius: 1px; font-size: 11px;">
    <div style="display: flex; align-items: center; margin-bottom: 8px;">
        <i class="fas fa-chart-line" style="margin-right: 6px; font-size: 12px;"></i>
        <span style="font-weight: 600; font-size: 12px;">财务概览</span>
    </div>
    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 12px;">
        <div style="text-align: center;">
            <div style="font-size: 10px; opacity: 0.9; margin-bottom: 2px;">本月直接成本</div>
            <div style="font-size: 14px; font-weight: 700; font-family: 'Courier New', monospace;" id="monthly-direct-cost">¥0.00</div>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 10px; opacity: 0.9; margin-bottom: 2px;">本月间接成本</div>
            <div style="font-size: 14px; font-weight: 700; font-family: 'Courier New', monospace;" id="monthly-indirect-cost">¥0.00</div>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 10px; opacity: 0.9; margin-bottom: 2px;">应付账款</div>
            <div style="font-size: 14px; font-weight: 700; font-family: 'Courier New', monospace;" id="total-payables">¥0.00</div>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 10px; opacity: 0.9; margin-bottom: 2px;">单位用餐成本</div>
            <div style="font-size: 14px; font-weight: 700; font-family: 'Courier New', monospace;" id="cost-per-meal">¥0.00</div>
        </div>
    </div>
</div>

<!-- 用友财务软件风格功能模块 -->
<div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px; margin-bottom: 16px;">
    <!-- 会计科目管理 -->
    <div style="background: white; border: 1px solid #c0c0c0; border-radius: 1px; padding: 12px; text-align: center; transition: all 0.2s; cursor: pointer;"
         onmouseover="this.style.boxShadow='0 2px 4px rgba(0,102,204,0.2)'; this.style.borderColor='var(--uf-primary)'"
         onmouseout="this.style.boxShadow='none'; this.style.borderColor='#c0c0c0'">
        <div style="font-size: 24px; color: var(--uf-primary); margin-bottom: 8px;">
            <i class="fas fa-list"></i>
        </div>
        <div style="font-size: 11px; font-weight: 600; color: #333; margin-bottom: 4px;">会计科目管理</div>
        <div style="font-size: 10px; color: #666; margin-bottom: 8px; line-height: 1.3;">设置会计科目结构，配置科目属性和分类</div>
        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn uf-btn-primary uf-btn-sm">
            查看科目
        </a>
    </div>

    <!-- 财务凭证管理 -->
    <div style="background: white; border: 1px solid #c0c0c0; border-radius: 1px; padding: 12px; text-align: center; transition: all 0.2s; cursor: pointer;"
         onmouseover="this.style.boxShadow='0 2px 4px rgba(40,167,69,0.2)'; this.style.borderColor='var(--uf-success)'"
         onmouseout="this.style.boxShadow='none'; this.style.borderColor='#c0c0c0'">
        <div style="font-size: 24px; color: var(--uf-success); margin-bottom: 8px;">
            <i class="fas fa-file-invoice"></i>
        </div>
        <div style="font-size: 11px; font-weight: 600; color: #333; margin-bottom: 4px;">财务凭证管理</div>
        <div style="font-size: 10px; color: #666; margin-bottom: 8px; line-height: 1.3;">创建和管理财务凭证，记录会计分录</div>
        <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn uf-btn-success uf-btn-sm">
            查看凭证
        </a>
    </div>

    <!-- 应付账款管理 -->
    <div style="background: white; border: 1px solid #c0c0c0; border-radius: 1px; padding: 12px; text-align: center; transition: all 0.2s; cursor: pointer;"
         onmouseover="this.style.boxShadow='0 2px 4px rgba(224,168,0,0.2)'; this.style.borderColor='var(--uf-warning)'"
         onmouseout="this.style.boxShadow='none'; this.style.borderColor='#c0c0c0'">
        <div style="font-size: 24px; color: var(--uf-warning); margin-bottom: 8px;">
            <i class="fas fa-credit-card"></i>
        </div>
        <div style="font-size: 11px; font-weight: 600; color: #333; margin-bottom: 4px;">应付账款管理</div>
        <div style="font-size: 10px; color: #666; margin-bottom: 8px; line-height: 1.3;">管理供应商应付账款，跟踪付款状态</div>
        <a href="{{ url_for('financial.payables_index') }}" class="uf-btn uf-btn-warning uf-btn-sm">
            查看账款
        </a>
    </div>

    <!-- 付款记录管理 -->
    <div style="background: white; border: 1px solid #c0c0c0; border-radius: 1px; padding: 12px; text-align: center; transition: all 0.2s; cursor: pointer;"
         onmouseover="this.style.boxShadow='0 2px 4px rgba(19,162,184,0.2)'; this.style.borderColor='var(--uf-info)'"
         onmouseout="this.style.boxShadow='none'; this.style.borderColor='#c0c0c0'">
        <div style="font-size: 24px; color: var(--uf-info); margin-bottom: 8px;">
            <i class="fas fa-hand-holding-usd"></i>
        </div>
        <div style="font-size: 11px; font-weight: 600; color: #333; margin-bottom: 4px;">付款记录管理</div>
        <div style="font-size: 10px; color: #666; margin-bottom: 8px; line-height: 1.3;">记录和管理付款信息，生成付款凭证</div>
        <a href="{{ url_for('financial.payments_index') }}" class="uf-btn uf-btn-info uf-btn-sm">
            查看付款
        </a>
    </div>
</div>

<!-- 用友风格财务报表 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-chart-bar uf-icon"></i> 财务报表
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 10px;">
            <!-- 资产负债表 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-primary); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-primary); margin-bottom: 4px;">
                            资产负债表
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            反映特定日期的财务状况
                        </div>
                        <a href="{{ url_for('financial.balance_sheet') }}" class="uf-btn uf-btn-sm uf-btn-primary">
                            <i class="fas fa-eye uf-icon"></i> 查看报表
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-balance-scale" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>

            <!-- 成本分析表 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-success); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-success); margin-bottom: 4px;">
                            成本分析表
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            分析食堂运营成本构成和变化
                        </div>
                        <a href="{{ url_for('financial.cost_analysis') }}" class="uf-btn uf-btn-sm uf-btn-success">
                            <i class="fas fa-eye uf-icon"></i> 查看报表
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-chart-pie" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>

            <!-- 应付账款账龄分析 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-warning); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-warning); margin-bottom: 4px;">
                            账龄分析
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            分析应付账款的账龄结构
                        </div>
                        <a href="{{ url_for('financial.payables_aging') }}" class="uf-btn uf-btn-sm uf-btn-warning">
                            <i class="fas fa-eye uf-icon"></i> 查看分析
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-clock" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 10px; margin-top: 10px;">
            <!-- 凭证汇总表 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-info); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-info); margin-bottom: 4px;">
                            凭证汇总
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            汇总分析财务凭证数据
                        </div>
                        <a href="{{ url_for('financial.voucher_summary') }}" class="uf-btn uf-btn-sm uf-btn-info">
                            <i class="fas fa-eye uf-icon"></i> 查看汇总
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-file-alt" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>

            <!-- 待处理事项 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-danger); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-danger); margin-bottom: 4px;">
                            待处理事项
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            需要处理的财务事项
                        </div>
                        <a href="{{ url_for('financial.payables_pending_stock_ins') }}" class="uf-btn uf-btn-sm uf-btn-danger">
                            <i class="fas fa-tasks uf-icon"></i> 查看待办
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>

            <!-- 数据导出 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-secondary); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-secondary); margin-bottom: 4px;">
                            数据导出
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            导出财务数据和报表
                        </div>
                        <div class="uf-btn-group">
                            <a href="{{ url_for('financial.export_report', report_type='payables') }}"
                               class="uf-btn uf-btn-sm">应付账款</a>
                            <a href="{{ url_for('financial.export_report', report_type='payments') }}"
                               class="uf-btn uf-btn-sm">付款记录</a>
                        </div>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-download" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 账簿查询功能 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 10px; margin-top: 10px;">
            <!-- 明细账查询 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-primary); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-primary); margin-bottom: 4px;">
                            明细账查询
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            查看科目明细发生记录
                        </div>
                        <a href="{{ url_for('financial.detail_ledger') }}" class="uf-btn uf-btn-sm uf-btn-primary">
                            <i class="fas fa-search uf-icon"></i> 查询明细
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-list-ul" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>

            <!-- 总账查询 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-secondary); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-secondary); margin-bottom: 4px;">
                            总账查询
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            查看科目汇总余额
                        </div>
                        <a href="{{ url_for('financial.general_ledger') }}" class="uf-btn uf-btn-sm">
                            <i class="fas fa-calculator uf-icon"></i> 查看总账
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-calculator" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>

            <!-- 科目余额表 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid var(--uf-dark); border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: var(--uf-dark); margin-bottom: 4px;">
                            科目余额表
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            查看所有科目余额情况
                        </div>
                        <a href="{{ url_for('financial.balance_sheet_detail') }}" class="uf-btn uf-btn-sm" style="background: var(--uf-dark); color: white;">
                            <i class="fas fa-balance-scale uf-icon"></i> 查看余额
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-balance-scale" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>

            <!-- 试算平衡表 -->
            <div style="border: 1px solid var(--uf-border); border-start: 4px solid #6f42c1; border-radius: 2px; background: white; height: 100px;">
                <div style="padding: 12px; display: flex; align-items: center; height: 100%;">
                    <div style="flex: 1;">
                        <div style="font-size: 12px; font-weight: 600; color: #6f42c1; margin-bottom: 4px;">
                            试算平衡表
                        </div>
                        <div style="font-size: 11px; color: #666; margin-bottom: 8px;">
                            检查账务平衡情况
                        </div>
                        <a href="{{ url_for('financial.general_ledger') }}" class="uf-btn uf-btn-sm" style="background: #6f42c1; color: white;">
                            <i class="fas fa-equals uf-icon"></i> 试算平衡
                        </a>
                    </div>
                    <div style="margin-left: 12px;">
                        <i class="fas fa-equals" style="font-size: 24px; color: #ddd;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用友风格快速操作 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-bolt uf-icon"></i> 快速操作
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px;">
            <a href="{{ url_for('financial.create_voucher') }}" class="uf-btn uf-btn-primary" style="justify-content: center; padding: 8px 16px;">
                <i class="fas fa-plus uf-icon"></i> 新建凭证
            </a>
            <a href="{{ url_for('financial.create_payment') }}" class="uf-btn uf-btn-success" style="justify-content: center; padding: 8px 16px;">
                <i class="fas fa-money-bill uf-icon"></i> 记录付款
            </a>
            <a href="{{ url_for('financial.create_accounting_subject') }}" class="uf-btn uf-btn-info" style="justify-content: center; padding: 8px 16px;">
                <i class="fas fa-plus uf-icon"></i> 新增科目
            </a>
            <a href="{{ url_for('financial.payables_pending_stock_ins') }}" class="uf-btn uf-btn-warning" style="justify-content: center; padding: 8px 16px;">
                <i class="fas fa-tasks uf-icon"></i> 处理入库
            </a>
            {% if current_user.is_admin %}
            <a href="{{ url_for('financial.admin_style_demo') }}" class="uf-btn uf-btn-secondary" style="justify-content: center; padding: 8px 16px;">
                <i class="fas fa-palette uf-icon"></i> 样式演示
            </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 财务首页特定JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 加载财务概览数据
    loadFinancialSummary();

    // 每5分钟刷新一次数据
    setInterval(loadFinancialSummary, 5 * 60 * 1000);
});

function loadFinancialSummary() {
    // 这里可以通过AJAX加载实际的财务数据
    // 暂时使用模拟数据
    const summaryData = {
        monthlyDirectCost: 85680.50,
        monthlyIndirectCost: 28750.30,
        totalPayables: 45230.80,
        costPerMeal: 12.50
    };

    // 更新显示
    document.getElementById('monthly-direct-cost').textContent = ufFormatAmountChinese(summaryData.monthlyDirectCost);
    document.getElementById('monthly-indirect-cost').textContent = ufFormatAmountChinese(summaryData.monthlyIndirectCost);
    document.getElementById('total-payables').textContent = ufFormatAmountChinese(summaryData.totalPayables);
    document.getElementById('cost-per-meal').textContent = ufFormatAmountChinese(summaryData.costPerMeal);

    // 设置成本颜色（成本越低越好）
    const costElement = document.getElementById('cost-per-meal');
    if (summaryData.costPerMeal <= 10) {
        costElement.style.color = '#28a745'; // 绿色：成本较低
    } else if (summaryData.costPerMeal <= 15) {
        costElement.style.color = '#ffc107'; // 黄色：成本适中
    } else {
        costElement.style.color = '#dc3545'; // 红色：成本较高
    }
}
</script>
{% endblock %}
