{% extends "base.html" %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 按钮组优化 */
    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
    }

    .action-buttons .btn {
        min-width: 100px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .action-buttons .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .action-buttons .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }

    .action-buttons .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border: none;
    }

    .action-buttons .btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
        border: none;
        color: #212529;
    }

    .action-buttons .btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        border: none;
    }

    .action-buttons .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
        border: none;
    }

    /* 响应式按钮布局 */
    @d-flex (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .action-buttons .btn {
            width: 100%;
            margin-bottom: 5px;
        }
    }

    /* 模态框按钮优化 */
    .modal-footer .btn {
        min-width: 80px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    /* 来源筛选按钮优化 */
    .btn-group .btn {
        border-radius: 6px !important;
        font-weight: 500;
        transition: all 0.2s ease;
        margin-right: 5px;
    }

    .btn-group .btn.active {
        transform: scale(1.02);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .btn-outline-success.active {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border-color: #28a745;
    }

    .btn-outline-info.active {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        border-color: #17a2b8;
    }

    .menu-input {
        min-height: 100px;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 10px;
        background-color: #f9f9f9;
    }

    .menu-input.readonly {
        background-color: #f0f0f0;
        cursor: not-allowed;
    }

    .menu-table {
        width: 100%;
        border-collapse: collapse;
    }

    .menu-table th, .menu-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }

    .menu-table th {
        background-color: #f2f2f2;
    }

    .week-selector {
        display: flex;
        margin-bottom: 20px;
        overflow-x: auto;
    }

    .week-item {
        padding: 10px 15px;
        margin-right: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        white-space: nowrap;
        position: relative;
    }

    .week-item.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .week-item.not-editable {
        background-color: #f0f0f0;
    }

    /* 已发布状态的周菜单使用红色显示 */
    .week-item[data-status="已发布"] {
        background-color: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .selected-recipe-tag {
        display: inline-block;
        background-color: #e9ecef;
        padding: 5px 10px;
        margin: 5px;
        border-radius: 20px;
        transition: all 0.3s;
    }

    .selected-recipe-tag .remove-btn {
        margin-left: 5px;
        cursor: pointer;
        font-weight: bold;
    }

    /* 高亮效果 */
    .selected-recipe-tag.highlight {
        background-color: #f8d7da;
        transform: scale(1.1);
        box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
    }

    .recipe-card {
        margin-bottom: 15px;
        cursor: pointer;
    }

    .recipe-card:hover {
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .recipe-card .card-body {
        padding: 10px;
        text-align: center;
    }

    /* 加载遮罩样式 - 统一定义 */
    .loading-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(0,0,0,0.5) !important;
        display: none !important;
        justify-content: center !important;
        align-items: center !important;
        z-index: 99999 !important;
        pointer-events: none !important;
    }

    .loading-overlay.show {
        display: flex !important;
        pointer-events: all !important;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 2s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .save-status {
        display: none;
        padding: 10px;
        margin-top: 10px;
        border-radius: 4px;
    }

    .save-status.success {
        background-color: #d4edda;
        color: #155724;
    }

    .save-status.error {
        background-color: #f8d7da;
        color: #721c24;
    }

    .save-status.info {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    /* 菜品显示项样式 */
    .recipe-item-display {
        display: inline-block;
        background-color: #e9ecef;
        padding: 4px 8px;
        margin: 2px;
        border-radius: 4px;
        font-size: 0.9rem;
        line-height: 1.2;
        border: 1px solid #dee2e6;
    }

    /* 打印样式 */
    @d-flex print {
        /* 隐藏不需要打印的元素 */
        .no-print,
        .btn,
        .dropdown,
        .week-selector,
        .alert,
        .modal,
        .loading-overlay,
        .card-header .dropdown,
        .d-sm-flex,
        .save-status {
            display: none !important;
        }

        /* A4横向布局 */
        @page {
            size: A4 landscape;
            margin: 1cm;
        }

        body {
            font-size: 12px;
            line-height: 1.3;
            color: #000;
            background: white;
        }

        .container-fluid {
            max-width: none;
            padding: 0;
        }

        /* 打印标题 */
        .print-header {
            display: block !important;
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .print-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .print-subtitle {
            font-size: 14px;
            margin-bottom: 5px;
        }

        /* 菜单表格打印样式 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            page-break-inside: avoid;
        }

        .menu-table th,
        .menu-table td {
            border: 2px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
            font-size: 11px;
            line-height: 1.4;
        }

        .menu-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }

        /* 日期列样式 */
        .menu-table td:first-child {
            width: 12%;
            text-align: center;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        /* 餐次列样式 */
        .menu-table td:not(:first-child) {
            width: 29.33%;
            min-height: 80px;
            position: relative;
        }

        /* 菜品显示样式 */
        .menu-input {
            border: none;
            background: transparent;
            padding: 0;
            margin: 0;
            min-height: auto;
            font-size: 11px;
            line-height: 1.4;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        /* 打印时菜品项样式 */
        .recipe-item-display {
            display: inline-block;
            background-color: transparent;
            border: 1px solid #666;
            padding: 3px 6px;
            margin: 1px;
            border-radius: 3px;
            font-size: 10px;
            line-height: 1.3;
            page-break-inside: avoid;
        }

        /* 确保表格在一页内 */
        .card {
            border: none;
            box-shadow: none;
        }

        .card-body {
            padding: 0;
        }

        .table-responsive {
            overflow: visible;
        }
    }

    /* 按钮组优化 */
    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
    }

    .action-buttons .btn {
        min-width: 100px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
        /* box-shadow: 0 1px 3px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
    }

    .action-buttons .btn:hover {
        transform: translateY(-1px);
        /* box-shadow: 0 2px 6px rgba(0,0,0,0.15); */ /* 移除阴影效果 */
    }

    .action-buttons .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }

    .action-buttons .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border: none;
    }

    .action-buttons .btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
        border: none;
        color: #212529;
    }

    .action-buttons .btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        border: none;
    }

    .action-buttons .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
        border: none;
    }

    /* 响应式按钮布局 */
    @d-flex (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .action-buttons .btn {
            width: 100%;
            margin-bottom: 5px;
        }
    }

    /* 模态框按钮优化 */
    .modal-footer .btn {
        min-width: 80px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    /* 来源筛选按钮优化 */
    .btn-group .btn {
        border-radius: 6px !important;
        font-weight: 500;
        transition: all 0.2s ease;
        margin-right: 5px;
    }

    .btn-group .btn.active {
        transform: scale(1.02);
        /* box-shadow: 0 2px 8px rgba(0,0,0,0.15); */ /* 移除阴影效果 */
    }

    .btn-outline-success.active {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border-color: #28a745;
    }

    .btn-outline-info.active {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        border-color: #17a2b8;
    }

    .menu-input {
        min-height: 100px;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 10px;
        background-color: #f9f9f9;
        transition: all 0.2s ease;
        position: relative;
        z-index: 1;
    }

    .menu-input:hover {
        border-color: #007bff;
        background-color: #e3f2fd;
        transform: translateY(-1px);
    }

    .menu-input.readonly {
        background-color: #f0f0f0;
        cursor: not-allowed;
        opacity: 0.7;
    }

    /* 确保页面元素可以获得焦点 */
    body {
        position: relative;
        z-index: 0;
    }

    /* 修复可能的焦点问题 */
    .container-fluid {
        position: relative;
        z-index: 1;
    }

    /* 强制启用页面交互 - 紧急修复 */
    body, html {
        pointer-events: auto !important;
        user-select: auto !important;
        touch-action: auto !important;
    }

    /* 模态框位置和大小优化 */
    .modal-dialog {
        margin-top: 8vh !important;
        margin-bottom: 2vh !important;
        max-width: 95vw !important;
        width: 95vw !important;
    }

    /* 模态框内容紧凑化 */
    .modal-header {
        padding: 0.75rem 1rem !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .modal-body {
        padding: 1rem !important;
        max-height: 70vh !important;
        overflow-y: auto !important;
    }

    .modal-footer {
        padding: 0.75rem 1rem !important;
        border-top: 1px solid #dee2e6 !important;
    }

    /* 卡片间距优化 */
    .modal-body .card {
        margin-bottom: 1rem !important;
    }

    .modal-body .card-header {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.9rem !important;
    }

    .modal-body .card-body {
        padding: 0.75rem !important;
    }

    .main-content, .content-area {
        pointer-events: auto !important;
        position: relative !important;
        z-index: auto !important;
    }

    .menu-input, .btn, .card, .table {
        pointer-events: auto !important;
        cursor: pointer !important;
    }

    .menu-input.readonly {
        cursor: not-allowed !important;
    }

    /* 确保没有隐藏的覆盖层 */
    *:not(.modal):not(.dropdown-menu):not(.loading-overlay) {
        pointer-events: auto !important;
    }

    /* 特别处理可能的问题元素 */
    .sidebar, .top-toolbar {
        pointer-events: auto !important;
    }

    .menu-table {
        width: 100%;
        border-collapse: collapse;
    }

    .menu-table th, .menu-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }

    .menu-table th {
        background-color: #f2f2f2;
    }

    .week-selector {
        display: flex;
        margin-bottom: 20px;
        overflow-x: auto;
    }

    .week-item {
        padding: 10px 15px;
        margin-right: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        white-space: nowrap;
        position: relative;
    }

    .week-item.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .week-item.not-editable {
        background-color: #f0f0f0;
    }

    /* 已发布状态的周菜单使用红色显示 */
    .week-item[data-status="已发布"] {
        background-color: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .selected-recipe-tag {
        display: inline-block;
        background-color: #e9ecef;
        padding: 5px 10px;
        margin: 5px;
        border-radius: 20px;
        transition: all 0.3s;
    }

    .selected-recipe-tag .remove-btn {
        margin-left: 5px;
        cursor: pointer;
        font-weight: bold;
    }

    /* 高亮效果 */
    .selected-recipe-tag.highlight {
        background-color: #f8d7da;
        transform: scale(1.1);
        /* box-shadow: 0 0 10px rgba(220, 53, 69, 0.5); */ /* 移除阴影效果 */
    }

    .recipe-card {
        margin-bottom: 15px;
        cursor: pointer;
    }

    .recipe-card:hover {
        /* box-shadow: 0 0 10px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
    }

    .recipe-card .card-body {
        padding: 10px;
        text-align: center;
    }



    .save-status {
        display: none;
        padding: 10px;
        margin-top: 10px;
        border-radius: 4px;
    }

    .save-status.success {
        background-color: #d4edda;
        color: #155724;
    }

    .save-status.error {
        background-color: #f8d7da;
        color: #721c24;
    }

    .save-status.info {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    /* 菜品显示项样式 */
    .recipe-item-display {
        display: inline-block;
        background-color: #e9ecef;
        padding: 4px 8px;
        margin: 2px;
        border-radius: 4px;
        font-size: 0.9rem;
        line-height: 1.2;
        border: 1px solid #dee2e6;
    }

    /* 打印样式 */
    @d-flex print {
        /* 隐藏不需要打印的元素 */
        .no-print,
        .btn,
        .dropdown,
        .week-selector,
        .alert,
        .modal,
        .loading-overlay,
        .card-header .dropdown,
        .d-sm-flex,
        .save-status {
            display: none !important;
        }

        /* A4横向布局 */
        @page {
            size: A4 landscape;
            margin: 1cm;
        }

        body {
            font-size: 12px;
            line-height: 1.3;
            color: #000;
            background: white;
        }

        .container-fluid {
            max-width: none;
            padding: 0;
        }

        /* 打印标题 */
        .print-header {
            display: block !important;
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .print-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .print-subtitle {
            font-size: 14px;
            margin-bottom: 5px;
        }

        /* 菜单表格打印样式 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            page-break-inside: avoid;
        }

        .menu-table th,
        .menu-table td {
            border: 2px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
            font-size: 11px;
            line-height: 1.4;
        }

        .menu-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }

        /* 日期列样式 */
        .menu-table td:first-child {
            width: 12%;
            text-align: center;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        /* 餐次列样式 */
        .menu-table td:not(:first-child) {
            width: 29.33%;
            min-height: 80px;
            position: relative;
        }

        /* 菜品显示样式 */
        .menu-input {
            border: none;
            background: transparent;
            padding: 0;
            margin: 0;
            min-height: auto;
            font-size: 11px;
            line-height: 1.4;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        /* 打印时菜品项样式 */
        .recipe-item-display {
            display: inline-block;
            background-color: transparent;
            border: 1px solid #666;
            padding: 3px 6px;
            margin: 1px;
            border-radius: 3px;
            font-size: 10px;
            line-height: 1.3;
            page-break-inside: avoid;
        }

        /* 确保表格在一页内 */
        .card {
            border: none;
            /* box-shadow: none; */ /* 移除阴影效果 */
        }

        .card-body {
            padding: 0;
        }

        .table-responsive {
            overflow: visible;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-end mb-4">
        <table class="action-buttons-table">
            <tr>
                {% if is_editable %}
                    {% if existing_menu %}
                        <td>
                            <button id="saveMenuBtn" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存菜单
                            </button>
                        </td>
                        <td>
                            <button id="testBootstrap5Btn" class="btn btn-warning" onclick="alert('Bootstrap 5.3.6 测试按钮工作正常！')">
                                <i class="fas fa-test"></i> 🔧 测试BS5
                            </button>
                        </td>
                        {% if existing_menu.status == '计划中' %}
                            <td>
                                <button id="publishMenuBtn" class="btn btn-success">
                                    <i class="fas fa-check"></i> 发布菜单
                                </button>
                            </td>
                        {% else %}
                            <td>
                                <button id="unpublishMenuBtn" class="btn btn-warning">
                                    <i class="fas fa-undo"></i> 解除发布
                                </button>
                            </td>
                        {% endif %}
                    {% else %}
                        <!-- 只在本周和下周显示创建菜单按钮 -->
                        {% if week_start >= this_week_monday|string %}
                            <td>
                                <button id="createMenuBtn" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> 创建菜单
                                </button>
                            </td>
                        {% endif %}
                    {% endif %}
                {% endif %}
                {% if existing_menu %}
                    <td>
                        <a href="{{ url_for('weekly_menu_v2.print_menu', id=existing_menu.id) }}" target="_blank" class="btn btn-info">
                            <i class="fas fa-print"></i> 打印菜单
                        </a>
                    </td>
                {% else %}
                    <td>
                        <button class="btn btn-info" disabled title="请先创建菜单才能使用专业打印格式">
                            <i class="fas fa-print"></i> 打印菜单
                        </button>
                    </td>
                {% endif %}
                <td>
                    <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-secondary">
                        <i class="fas fa-list"></i> 返回列表
                    </a>
                </td>
            </tr>
        </table>
    </div>

    <!-- 打印标题（仅在打印时显示） -->
    <div class="print-header" style="display: none;">
        <div class="print-title">{{ area.name }}周菜单安排表</div>
        <div class="print-subtitle">{{ week_start }} 至 {{ week_end }}</div>
        <div class="print-subtitle">制表日期：<span id="print-date"></span></div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold text-danger">🔧 Bootstrap 5.3.6修复进行中 - {{ area.name }} 周菜单计划 ({{ week_start }} 至 {{ week_end }})</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">菜单操作:</div>
                    <a class="dropdown-item" href="{{ url_for('weekly_menu_v2.copy', area_id=area_id) }}">
                        <i class="fas fa-copy fa-sm fa-fw me-2 text-gray-400"></i>
                        复制历史菜单
                    </a>
                    {% if existing_menu %}
                        <a class="dropdown-item" href="{{ url_for('weekly_menu_v2.view', id=existing_menu.id) }}">
                            <i class="fas fa-eye fa-sm fa-fw me-2 text-gray-400"></i>
                            查看菜单详情
                        </a>
                        {% if existing_menu.status == '已发布' %}
                            <a class="dropdown-item" href="{{ url_for('purchase_order.create_from_menu', weekly_menu_id=existing_menu.id) }}">
                                <i class="fas fa-shopping-cart fa-sm fa-fw me-2 text-gray-400"></i>
                                创建采购订单
                            </a>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 周次选择器 -->
            <div class="week-selector mb-4">
                {% for week in available_weeks %}
                    <div class="week-item {% if week.start_date == week_start %}active{% endif %}{% if not week.is_editable %} not-editable{% endif %}"
                         data-week="{{ week.start_date }}"
                         data-editable="{{ week.is_editable }}"
                         data-status="{{ week.status }}">
                        {{ week.display_text }}
                        <span class="badge bg-{{ week.status|status_class }}">{{ week.status }}</span>
                    </div>
                {% endfor %}
            </div>

            {% if not existing_menu and is_editable %}
                <!-- 创建菜单提示 -->
                <div id="createMenuPrompt" class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 当前周次没有菜单计划，请点击"创建菜单"按钮创建新的菜单计划。
                </div>
                <!-- 调试信息 -->
                <div class="alert alert-secondary small">
                    <strong>调试信息:</strong><br>
                    区域ID: {{ area_id }}<br>
                    周开始日期: {{ week_start }}<br>
                    用户ID: {{ current_user.id }}<br>
                    用户名: {{ current_user.name }}<br>
                    用户角色: {{ current_user.roles|map(attribute='name')|join(', ') }}
                </div>

                <!-- 错误信息显示区域 -->
                <div id="errorMessages" class="alert alert-danger" style="display: none;"></div>

                <!-- AJAX请求状态显示区域 -->
                <div id="ajaxStatus" class="alert alert-info" style="display: none;"></div>
            {% endif %}

            <!-- 菜单表单 -->
            <form id="menuForm" method="POST" action="{{ url_for('weekly_menu_v2.plan', area_id=area_id, week_start=week_start) }}" {% if not existing_menu %}style="display: none;"{% endif %}>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" id="area-id" name="area_id" value="{{ area_id }}">
                <input type="hidden" id="week-start" name="week_start" value="{{ week_start }}">
                <input type="hidden" id="menu-id" name="menu_id" value="{{ existing_menu.id if existing_menu else '' }}">
                <input type="hidden" id="menuData" name="menu_data" value="{{ menu_data|tojson }}">

                <div class="table-responsive">
                    <table class="menu-table">
                        <thead>
                            <tr>
                                <th width="15%">日期</th>
                                <th width="28%">早餐</th>
                                <th width="28%">午餐</th>
                                <th width="28%">晚餐</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date, info in week_dates.items() %}
                                <tr>
                                    <td>{{ info.date }}<br>{{ info.weekday }}</td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="早餐">
                                            {% if menu_data and date in menu_data and '早餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['早餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="午餐">
                                            {% if menu_data and date in menu_data and '午餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['午餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="晚餐">
                                            {% if menu_data and date in menu_data and '晚餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['晚餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="save-status"></div>
            </form>
        </div>
    </div>
</div>

<!-- 菜品选择模态框 -->
<div class="modal fade" id="menuModal" tabindex="-1" aria-labelledby="menuModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="margin-top: 10vh; max-width: 90vw;"">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">选择菜品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card mb-2">
                            <div class="card-header py-2">
                                <h6 class="m-0 fw-bold text-primary">已选菜品</h6>
                            </div>
                            <div class="card-body py-2" style="min-height: 120px; max-height: 200px; overflow-y: auto;">
                                <div id="selectedDishes"></div>
                            </div>
                        </div>

                        <div class="card mb-2">
                            <div class="card-header py-2">
                                <h6 class="m-0 fw-bold text-primary">添加自定义菜品</h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="input-group input-group-sm">
                                    <input type="text" id="customDishInput" class="form-control" placeholder="输入菜品名称">
                                    <button id="addCustomDishBtn" class="btn btn-primary" type="button">
                                        <i class="fas fa-plus"></i> 添加
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <div class="card mb-2">
                            <div class="card-header py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <h6 class="m-0 fw-bold text-primary">菜品列表</h6>
                                    </div>
                                    <div class="col-md-8">
                                        <input type="text" id="recipeSearch" class="form-control form-control-sm" placeholder="搜索菜品">
                                    </div>
                                </div>
                            </div>
                            <div class="card-body py-2">
                                <!-- 只显示学校食谱标题 -->
                                <div class="mb-2">
                                    <span class="badge bg-success">
                                        <i class="fas fa-school"></i> 学校食谱
                                    </span>
                                </div>

                                <ul class="nav nav-tabs nav-tabs-sm" id="recipeCategories">
                                    <li class="nav-item">
                                        <a class="nav-link active small" data-category="all" href="#">全部</a>
                                    </li>
                                    {% for category, recipes in recipes_by_category.items() %}
                                        <li class="nav-item">
                                            <a class="nav-link small" data-category="{{ category }}" href="#">{{ category }}</a>
                                        </li>
                                    {% endfor %}
                                </ul>

                                <div class="tab-content mt-2" style="max-height: 300px; overflow-y: auto;">
                                    <div class="tab-pane fade show active">
                                        <div class="row">
                                            <!-- 食谱卡片将由JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" id="saveSelectionBtn" class="btn btn-primary btn-sm">
                    <i class="fas fa-check"></i> 保存选择
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" style="display: none;">
    <div class="loading-spinner"></div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/weekly_menu_v2.js') }}"></script>
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 旧的jQuery代码已被原生JS重写替代

    // 设置打印日期
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const printDate = `${year}年${month}月${day}日`;
    $('#print-date').text(printDate);

    // 优化打印功能
    window.addEventListener('beforeprint', function() {
        // 打印前的处理
        document.title = '{{ area.name }}周菜单安排表_{{ week_start }}至{{ week_end }}';
    });

    window.addEventListener('afterprint', function() {
        // 打印后的处理
        document.title = '{{ title }}';
    });

    // 旧的jQuery模态框事件已被原生JS替代

    // 添加焦点调试功能
    $(document).on('click', function(e) {
        console.log('点击事件:', e.target, '当前焦点:', document.activeElement);
    });

    // 检查页面加载状态
    console.log('=== 周菜单页面状态检查 ===');
    console.log('- jQuery版本:', $.fn.jquery);
    console.log('- Bootstrap可用:', typeof bootstrap !== 'undefined');
    console.log('- 加载遮罩数量:', $('.loading-overlay').length);
    console.log('- 加载遮罩状态:', $('.loading-overlay').css('display'));
    console.log('- 菜单输入框数量:', $('.menu-input').length);
    console.log('- 当前焦点元素:', document.activeElement);

    // 强制隐藏所有遮罩层
    $('.loading-overlay').each(function(index, element) {
        console.log('遮罩层', index, ':', element, '显示状态:', $(element).css('display'));
        // 多种方式确保隐藏
        $(element).hide();
        $(element).removeClass('show');
        element.style.setProperty('display', 'none', 'important');
        element.style.setProperty('visibility', 'hidden', 'important');
        element.style.setProperty('opacity', '0', 'important');
        element.style.setProperty('pointer-events', 'none', 'important');
    });

    // 额外的安全检查
    setTimeout(() => {
        const visibleOverlays = $('.loading-overlay:visible');
        if (visibleOverlays.length > 0) {
            console.warn('发现仍然可见的遮罩层，强制隐藏:', visibleOverlays);
            visibleOverlays.each(function() {
                this.style.setProperty('display', 'none', 'important');
            });
        }
    }, 100);

    console.log('=== 遮罩层处理完成 ===');

    // 页面交互诊断
    console.log('=== 页面交互诊断开始 ===');

    // 检查所有可能阻挡交互的元素
    const allElements = document.querySelectorAll('*');
    const blockingElements = [];

    allElements.forEach(el => {
        const style = window.getComputedStyle(el);
        const rect = el.getBoundingClientRect();

        // 检查是否有元素覆盖整个页面
        if (style.position === 'fixed' || style.position === 'absolute') {
            if (rect.width >= window.innerWidth * 0.8 && rect.height >= window.innerHeight * 0.8) {
                if (style.zIndex > 100 && style.display !== 'none' && style.visibility !== 'hidden') {
                    blockingElements.push({
                        element: el,
                        className: el.className,
                        id: el.id,
                        zIndex: style.zIndex,
                        position: style.position,
                        display: style.display,
                        pointerEvents: style.pointerEvents
                    });
                }
            }
        }
    });

    if (blockingElements.length > 0) {
        console.warn('发现可能阻挡交互的元素:', blockingElements);
        blockingElements.forEach(item => {
            console.log('阻挡元素:', item);
            // 如果不是模态框，尝试隐藏
            if (!item.className.includes('modal') && !item.className.includes('dropdown')) {
                item.element.style.setProperty('display', 'none', 'important');
                console.log('已隐藏阻挡元素:', item.element);
            }
        });
    } else {
        console.log('未发现明显的阻挡元素');
    }

    // 测试点击事件
    console.log('测试页面点击响应...');
    document.addEventListener('click', function(e) {
        console.log('页面点击事件:', {
            target: e.target,
            className: e.target.className,
            id: e.target.id,
            tagName: e.target.tagName,
            coordinates: {x: e.clientX, y: e.clientY}
        });
    }, {once: true});

    // 检查菜单输入框状态
    const menuInputs = document.querySelectorAll('.menu-input');
    console.log('菜单输入框数量:', menuInputs.length);
    menuInputs.forEach((input, index) => {
        const style = window.getComputedStyle(input);
        console.log(`菜单输入框 ${index}:`, {
            element: input,
            pointerEvents: style.pointerEvents,
            zIndex: style.zIndex,
            position: style.position,
            display: style.display,
            visibility: style.visibility
        });
    });

    console.log('=== 页面交互诊断完成 ===');

    // 强制启用页面交互
    setTimeout(() => {
        console.log('强制启用页面交互...');

        // 确保body可以接收事件
        document.body.style.pointerEvents = 'auto';
        document.documentElement.style.pointerEvents = 'auto';

        // 确保主要容器可以接收事件
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.style.pointerEvents = 'auto';
        }

        const contentArea = document.querySelector('.content-area');
        if (contentArea) {
            contentArea.style.pointerEvents = 'auto';
        }

        // 确保所有菜单输入框可以接收事件
        document.querySelectorAll('.menu-input').forEach(input => {
            input.style.pointerEvents = 'auto';
            input.style.cursor = 'pointer';
        });

        // 确保所有按钮可以接收事件
        document.querySelectorAll('.btn').forEach(btn => {
            btn.style.pointerEvents = 'auto';
        });

        console.log('页面交互强制启用完成');

        // 最终测试
        const testElement = document.querySelector('.menu-input:not(.readonly)');
        if (testElement) {
            console.log('测试元素:', testElement);
            console.log('测试元素样式:', window.getComputedStyle(testElement));

            // 添加测试点击监听器
            testElement.addEventListener('click', function(e) {
                console.log('测试点击成功!', e);
                alert('页面交互已恢复正常！');
            }, {once: true});
        }
    }, 2000);

    // Bootstrap 5.3.6 + 原生JS 重写
    console.log('=== Bootstrap 5.3.6 + 原生JS 重写开始 ===');

    // 1. 使用原生JS重新绑定菜单输入框事件
    function bindMenuInputEvents() {
        // 移除所有旧的事件监听器
        document.querySelectorAll('.menu-input').forEach(input => {
            input.replaceWith(input.cloneNode(true));
        });

        // 使用原生JS重新绑定
        document.querySelectorAll('.menu-input').forEach(input => {
            input.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('原生JS菜单格子点击:', this);

                if (this.classList.contains('readonly')) {
                    console.log('只读菜单格子，跳过');
                    return;
                }

                const date = this.dataset.date;
                const meal = this.dataset.meal;

                console.log('菜单格子数据:', {date, meal});

                // 直接显示模态框，不依赖RecipeSelector
                showMenuModal(date, meal);
            });
        });

        console.log('原生JS菜单输入框事件绑定完成');
    }

    // 2. 原生JS模态框显示函数
    function showMenuModal(date, meal) {
        console.log(`显示菜品选择模态框: ${date} ${meal}`);

        const modalElement = document.getElementById('menuModal');
        if (!modalElement) {
            alert('未找到模态框元素，请检查页面结构');
            return;
        }

        // 保存当前选择的日期和餐次到全局变量
        window.currentMenuSelection = {
            date: date,
            meal: meal
        };

        // 更新模态框标题
        const titleElement = document.getElementById('modalTitle');
        if (titleElement) {
            titleElement.textContent = `${date} ${meal} 菜品选择`;
        }

        // 清空已选菜品区域
        const selectedContainer = document.getElementById('selectedDishes');
        if (selectedContainer) {
            selectedContainer.innerHTML = '';
        }

        // 使用Bootstrap 5.3.6原生API显示模态框
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            try {
                const modal = new bootstrap.Modal(modalElement, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
                modal.show();
                console.log('Bootstrap 5.3.6模态框显示成功');
            } catch (error) {
                console.error('Bootstrap 5.3.6模态框显示失败:', error);
                // 备用方案：手动显示
                modalElement.style.display = 'block';
                modalElement.classList.add('show');
                document.body.classList.add('modal-open');

                // 添加背景遮罩
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'manual-backdrop';
                document.body.appendChild(backdrop);

                console.log('使用手动方式显示模态框');
            }
        } else {
            alert('Bootstrap 5未正确加载');
        }
    }

    // 立即执行绑定
    bindMenuInputEvents();

    // 3. 重写菜品加载和显示功能（仅学校食谱）
    window.loadRecipes = function() {
        console.log('开始加载学校菜品数据...');

        // 学校食谱数据（实际应该从API获取）
        const schoolRecipes = [
            {id: 1, name: '红烧肉', category: '肉类'},
            {id: 2, name: '糖醋排骨', category: '肉类'},
            {id: 3, name: '宫保鸡丁', category: '肉类'},
            {id: 4, name: '白切鸡', category: '肉类'},
            {id: 5, name: '红烧鱼', category: '肉类'},
            {id: 6, name: '麻婆豆腐', category: '豆制品'},
            {id: 7, name: '家常豆腐', category: '豆制品'},
            {id: 8, name: '青椒土豆丝', category: '蔬菜'},
            {id: 9, name: '西红柿鸡蛋', category: '蔬菜'},
            {id: 10, name: '蒜蓉西兰花', category: '蔬菜'},
            {id: 11, name: '红烧茄子', category: '蔬菜'},
            {id: 12, name: '酸辣土豆丝', category: '蔬菜'},
            {id: 13, name: '白米饭', category: '主食'},
            {id: 14, name: '蒸蛋羹', category: '汤品'},
            {id: 15, name: '紫菜蛋花汤', category: '汤品'}
        ];

        // 直接显示学校食谱
        displayRecipes(schoolRecipes);

        console.log('学校菜品数据加载完成');
    };

    // 4. 显示菜品卡片
    function displayRecipes(recipes) {
        console.log('显示学校菜品:', recipes.length, '个');

        const container = document.querySelector('.tab-content .row');
        if (!container) {
            console.error('未找到菜品容器');
            return;
        }

        // 清空现有内容
        container.innerHTML = '';

        // 生成菜品卡片
        recipes.forEach(recipe => {
            const cardHtml = `
                <div class="col-md-3 col-sm-4 col-6 mb-2">
                    <div class="card recipe-card h-100" data-id="${recipe.id}" data-category="${recipe.category}"
                         style="cursor: pointer; transition: all 0.2s; border: 1px solid #dee2e6;"
                         onclick="selectRecipe(${recipe.id}, '${recipe.name}', '${recipe.category}')">
                        <div class="card-body p-2 text-center">
                            <h6 class="card-title mb-1 small">${recipe.name}</h6>
                            <small class="text-muted">${recipe.category}</small>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += cardHtml;
        });

        // 添加卡片悬停效果
        container.querySelectorAll('.recipe-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '';
            });
        });
    }

    // 5. 选择菜品功能
    window.selectRecipe = function(recipeId, recipeName, category) {
        console.log('选择学校菜品:', {recipeId, recipeName, category});

        // 检查是否已经选择
        const selectedContainer = document.getElementById('selectedDishes');
        const existingTag = selectedContainer.querySelector(`[data-id="${recipeId}"]`);

        if (existingTag) {
            // 高亮已存在的标签
            existingTag.style.backgroundColor = '#ffc107';
            existingTag.style.color = '#000';
            setTimeout(() => {
                existingTag.style.backgroundColor = '';
                existingTag.style.color = '';
            }, 1000);
            return;
        }

        // 创建已选菜品标签
        const tagHtml = `
            <span class="badge bg-success me-1 mb-1 d-inline-flex align-items-center" data-id="${recipeId}">
                ${recipeName}
                <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;"
                        onclick="removeSelectedRecipe(${recipeId})"></button>
            </span>
        `;

        selectedContainer.innerHTML += tagHtml;

        // 标记卡片为已选择
        const recipeCard = document.querySelector(`.recipe-card[data-id="${recipeId}"]`);
        if (recipeCard) {
            recipeCard.style.backgroundColor = '#d4edda';
            recipeCard.style.borderColor = '#28a745';
            recipeCard.style.borderWidth = '2px';
        }
    };

    // 6. 移除已选菜品
    window.removeSelectedRecipe = function(recipeId) {
        console.log('移除菜品:', recipeId);

        // 移除标签
        const tag = document.querySelector(`#selectedDishes [data-id="${recipeId}"]`);
        if (tag) {
            tag.remove();
        }

        // 恢复卡片样式
        const recipeCard = document.querySelector(`.recipe-card[data-id="${recipeId}"]`);
        if (recipeCard) {
            recipeCard.style.backgroundColor = '';
            recipeCard.style.borderColor = '#dee2e6';
            recipeCard.style.borderWidth = '1px';
        }
    };

    // 7. 保存选择功能
    document.getElementById('saveSelectionBtn').addEventListener('click', function() {
        console.log('保存菜品选择');

        // 检查是否有当前选择的日期和餐次
        if (!window.currentMenuSelection || !window.currentMenuSelection.date || !window.currentMenuSelection.meal) {
            alert('错误：缺少日期或餐次信息');
            return;
        }

        const { date, meal } = window.currentMenuSelection;
        console.log('当前选择:', { date, meal });

        const selectedTags = document.querySelectorAll('#selectedDishes [data-id]');
        const selectedRecipes = Array.from(selectedTags).map(tag => ({
            id: tag.dataset.id,
            name: tag.textContent.trim().replace('×', '').trim(), // 移除关闭按钮的×符号
            recipe_id: tag.dataset.id,
            recipe_name: tag.textContent.trim().replace('×', '').trim(),
            is_custom: tag.dataset.id.startsWith('custom_')
        }));

        console.log('已选菜品:', selectedRecipes);

        if (selectedRecipes.length === 0) {
            alert('请至少选择一个菜品');
            return;
        }

        // 更新对应的菜单格子显示
        const menuInput = document.querySelector(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);
        if (menuInput) {
            // 清空原有内容
            menuInput.innerHTML = '';

            // 添加选择的菜品
            selectedRecipes.forEach(recipe => {
                const recipeDiv = document.createElement('div');
                recipeDiv.className = 'recipe-item-display';
                recipeDiv.textContent = recipe.name;
                menuInput.appendChild(recipeDiv);
            });

            // 添加has-recipes类
            menuInput.classList.add('has-recipes');

            console.log(`已更新菜单格子: ${date} ${meal}`);
        } else {
            console.error(`未找到菜单格子: ${date} ${meal}`);
        }

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('menuModal'));
        if (modal) {
            modal.hide();
        }

        alert(`✅ 已选择 ${selectedRecipes.length} 个菜品：\n${selectedRecipes.map(r => r.name).join('、')}\n\n请点击"保存菜单"按钮保存到数据库`);
    });

    // 8. 自定义菜品添加
    document.getElementById('addCustomDishBtn').addEventListener('click', function() {
        const input = document.getElementById('customDishInput');
        const dishName = input.value.trim();

        if (!dishName) {
            alert('请输入菜品名称');
            return;
        }

        // 生成自定义菜品ID
        const customId = 'custom_' + Date.now();

        // 添加到已选菜品
        selectRecipe(customId, dishName, '自定义');

        // 清空输入框
        input.value = '';
    });

    // 9. 模态框显示时自动加载菜品
    document.getElementById('menuModal').addEventListener('shown.bs.modal', function() {
        console.log('模态框已显示，加载学校菜品...');
        loadRecipes();
    });

    // 2. 确保Bootstrap 5模态框正常工作
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap 5已加载，版本检查...');

        // 检查模态框元素
        const modalElement = document.getElementById('menuModal');
        if (modalElement) {
            console.log('找到模态框元素:', modalElement);

            // 确保模态框可以正常初始化
            try {
                const testModal = new bootstrap.Modal(modalElement, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
                console.log('模态框初始化测试成功');
            } catch (error) {
                console.error('模态框初始化失败:', error);
            }
        } else {
            console.error('未找到模态框元素 #menuModal');
        }
    } else {
        console.error('Bootstrap 5未加载');
    }

    // 3. 最终测试
    setTimeout(() => {
        console.log('=== 最终兼容性测试 ===');

        const testInput = document.querySelector('.menu-input:not(.readonly)');
        if (testInput) {
            console.log('测试菜单输入框:', testInput);

            // 模拟点击测试
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });

            console.log('模拟点击测试...');
            testInput.dispatchEvent(clickEvent);
        }

        console.log('=== Bootstrap 5 兼容性修复完成 ===');
    }, 3000);

    // 按钮点击效果增强
    $('.action-buttons .btn').on('click', function() {
        $(this).addClass('btn-clicked');
        setTimeout(() => {
            $(this).removeClass('btn-clicked');
        }, 200);
    });

    // 添加按钮点击动画CSS
    $('<style>').text(`
        .btn-clicked {
            transform: scale(0.95) !important;
            transition: transform 0.1s ease !important;
        }

        .action-buttons .btn:active {
            transform: scale(0.98);
        }

        .btn-group .btn:active {
            transform: scale(0.98);
        }

        /* 禁用按钮样式优化 */
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }
    `).appendTo('head');
});
</script>
{% endblock %}
