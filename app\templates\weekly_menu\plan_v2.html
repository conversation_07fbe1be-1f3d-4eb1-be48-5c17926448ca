{% extends "base.html" %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 按钮组优化 */
    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
    }

    .action-buttons .btn {
        min-width: 100px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .action-buttons .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .action-buttons .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }

    .action-buttons .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border: none;
    }

    .action-buttons .btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
        border: none;
        color: #212529;
    }

    .action-buttons .btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        border: none;
    }

    .action-buttons .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
        border: none;
    }

    /* 响应式按钮布局 */
    @d-flex (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .action-buttons .btn {
            width: 100%;
            margin-bottom: 5px;
        }
    }

    /* 模态框按钮优化 */
    .modal-footer .btn {
        min-width: 80px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    /* 来源筛选按钮优化 */
    .btn-group .btn {
        border-radius: 6px !important;
        font-weight: 500;
        transition: all 0.2s ease;
        margin-right: 5px;
    }

    .btn-group .btn.active {
        transform: scale(1.02);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .btn-outline-success.active {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border-color: #28a745;
    }

    .btn-outline-info.active {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        border-color: #17a2b8;
    }

    .menu-input {
        min-height: 100px;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 10px;
        background-color: #f9f9f9;
    }

    .menu-input.readonly {
        background-color: #f0f0f0;
        cursor: not-allowed;
    }

    .menu-table {
        width: 100%;
        border-collapse: collapse;
    }

    .menu-table th, .menu-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }

    .menu-table th {
        background-color: #f2f2f2;
    }

    .week-selector {
        display: flex;
        margin-bottom: 20px;
        overflow-x: auto;
    }

    .week-item {
        padding: 10px 15px;
        margin-right: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        white-space: nowrap;
        position: relative;
    }

    .week-item.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .week-item.not-editable {
        background-color: #f0f0f0;
    }

    /* 已发布状态的周菜单使用红色显示 */
    .week-item[data-status="已发布"] {
        background-color: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .selected-recipe-tag {
        display: inline-block;
        background-color: #e9ecef;
        padding: 5px 10px;
        margin: 5px;
        border-radius: 20px;
        transition: all 0.3s;
    }

    .selected-recipe-tag .remove-btn {
        margin-left: 5px;
        cursor: pointer;
        font-weight: bold;
    }

    /* 高亮效果 */
    .selected-recipe-tag.highlight {
        background-color: #f8d7da;
        transform: scale(1.1);
        box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
    }

    .recipe-card {
        margin-bottom: 15px;
        cursor: pointer;
    }

    .recipe-card:hover {
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .recipe-card .card-body {
        padding: 10px;
        text-align: center;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 2s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .save-status {
        display: none;
        padding: 10px;
        margin-top: 10px;
        border-radius: 4px;
    }

    .save-status.success {
        background-color: #d4edda;
        color: #155724;
    }

    .save-status.error {
        background-color: #f8d7da;
        color: #721c24;
    }

    .save-status.info {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    /* 菜品显示项样式 */
    .recipe-item-display {
        display: inline-block;
        background-color: #e9ecef;
        padding: 4px 8px;
        margin: 2px;
        border-radius: 4px;
        font-size: 0.9rem;
        line-height: 1.2;
        border: 1px solid #dee2e6;
    }

    /* 打印样式 */
    @d-flex print {
        /* 隐藏不需要打印的元素 */
        .no-print,
        .btn,
        .dropdown,
        .week-selector,
        .alert,
        .modal,
        .loading-overlay,
        .card-header .dropdown,
        .d-sm-flex,
        .save-status {
            display: none !important;
        }

        /* A4横向布局 */
        @page {
            size: A4 landscape;
            margin: 1cm;
        }

        body {
            font-size: 12px;
            line-height: 1.3;
            color: #000;
            background: white;
        }

        .container-fluid {
            max-width: none;
            padding: 0;
        }

        /* 打印标题 */
        .print-header {
            display: block !important;
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .print-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .print-subtitle {
            font-size: 14px;
            margin-bottom: 5px;
        }

        /* 菜单表格打印样式 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            page-break-inside: avoid;
        }

        .menu-table th,
        .menu-table td {
            border: 2px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
            font-size: 11px;
            line-height: 1.4;
        }

        .menu-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }

        /* 日期列样式 */
        .menu-table td:first-child {
            width: 12%;
            text-align: center;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        /* 餐次列样式 */
        .menu-table td:not(:first-child) {
            width: 29.33%;
            min-height: 80px;
            position: relative;
        }

        /* 菜品显示样式 */
        .menu-input {
            border: none;
            background: transparent;
            padding: 0;
            margin: 0;
            min-height: auto;
            font-size: 11px;
            line-height: 1.4;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        /* 打印时菜品项样式 */
        .recipe-item-display {
            display: inline-block;
            background-color: transparent;
            border: 1px solid #666;
            padding: 3px 6px;
            margin: 1px;
            border-radius: 3px;
            font-size: 10px;
            line-height: 1.3;
            page-break-inside: avoid;
        }

        /* 确保表格在一页内 */
        .card {
            border: none;
            box-shadow: none;
        }

        .card-body {
            padding: 0;
        }

        .table-responsive {
            overflow: visible;
        }
    }

    /* 按钮组优化 */
    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
    }

    .action-buttons .btn {
        min-width: 100px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
        /* box-shadow: 0 1px 3px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
    }

    .action-buttons .btn:hover {
        transform: translateY(-1px);
        /* box-shadow: 0 2px 6px rgba(0,0,0,0.15); */ /* 移除阴影效果 */
    }

    .action-buttons .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }

    .action-buttons .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border: none;
    }

    .action-buttons .btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
        border: none;
        color: #212529;
    }

    .action-buttons .btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        border: none;
    }

    .action-buttons .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
        border: none;
    }

    /* 响应式按钮布局 */
    @d-flex (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .action-buttons .btn {
            width: 100%;
            margin-bottom: 5px;
        }
    }

    /* 模态框按钮优化 */
    .modal-footer .btn {
        min-width: 80px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    /* 来源筛选按钮优化 */
    .btn-group .btn {
        border-radius: 6px !important;
        font-weight: 500;
        transition: all 0.2s ease;
        margin-right: 5px;
    }

    .btn-group .btn.active {
        transform: scale(1.02);
        /* box-shadow: 0 2px 8px rgba(0,0,0,0.15); */ /* 移除阴影效果 */
    }

    .btn-outline-success.active {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border-color: #28a745;
    }

    .btn-outline-info.active {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        border-color: #17a2b8;
    }

    .menu-input {
        min-height: 100px;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 10px;
        background-color: #f9f9f9;
    }

    .menu-input.readonly {
        background-color: #f0f0f0;
        cursor: not-allowed;
    }

    .menu-table {
        width: 100%;
        border-collapse: collapse;
    }

    .menu-table th, .menu-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }

    .menu-table th {
        background-color: #f2f2f2;
    }

    .week-selector {
        display: flex;
        margin-bottom: 20px;
        overflow-x: auto;
    }

    .week-item {
        padding: 10px 15px;
        margin-right: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        white-space: nowrap;
        position: relative;
    }

    .week-item.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .week-item.not-editable {
        background-color: #f0f0f0;
    }

    /* 已发布状态的周菜单使用红色显示 */
    .week-item[data-status="已发布"] {
        background-color: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .selected-recipe-tag {
        display: inline-block;
        background-color: #e9ecef;
        padding: 5px 10px;
        margin: 5px;
        border-radius: 20px;
        transition: all 0.3s;
    }

    .selected-recipe-tag .remove-btn {
        margin-left: 5px;
        cursor: pointer;
        font-weight: bold;
    }

    /* 高亮效果 */
    .selected-recipe-tag.highlight {
        background-color: #f8d7da;
        transform: scale(1.1);
        /* box-shadow: 0 0 10px rgba(220, 53, 69, 0.5); */ /* 移除阴影效果 */
    }

    .recipe-card {
        margin-bottom: 15px;
        cursor: pointer;
    }

    .recipe-card:hover {
        /* box-shadow: 0 0 10px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
    }

    .recipe-card .card-body {
        padding: 10px;
        text-align: center;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 2s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .save-status {
        display: none;
        padding: 10px;
        margin-top: 10px;
        border-radius: 4px;
    }

    .save-status.success {
        background-color: #d4edda;
        color: #155724;
    }

    .save-status.error {
        background-color: #f8d7da;
        color: #721c24;
    }

    .save-status.info {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    /* 菜品显示项样式 */
    .recipe-item-display {
        display: inline-block;
        background-color: #e9ecef;
        padding: 4px 8px;
        margin: 2px;
        border-radius: 4px;
        font-size: 0.9rem;
        line-height: 1.2;
        border: 1px solid #dee2e6;
    }

    /* 纯CSS+JS菜品选择窗口样式 */
    .menu-selector {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        width: 90%;
        max-width: 1200px;
        height: 80%;
        max-height: 800px;
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        display: flex;
        flex-direction: column;
    }

    .menu-window {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .menu-window-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px 8px 0 0;
        border-bottom: 1px solid #dee2e6;
    }

    .menu-window-title {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 16px;
    }

    .menu-window-title i {
        margin-right: 8px;
        font-size: 16px;
    }

    .menu-window-controls {
        display: flex;
        gap: 8px;
    }

    .menu-btn-close {
        width: 28px;
        height: 28px;
        border: none;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;
    }

    .menu-btn-close:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .menu-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #f8f9fa;
    }

    .menu-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        padding: 16px 20px;
        background: #fff;
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 8px 8px;
    }

    .menu-btn {
        padding: 8px 16px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        transition: all 0.2s;
        text-decoration: none;
    }

    .menu-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .menu-btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
    }

    .menu-btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        color: white;
    }

    .menu-btn-secondary {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .menu-btn-secondary:hover {
        background: #5a6268;
        border-color: #545b62;
        color: white;
    }

    /* 打印样式 */
    @d-flex print {
        /* 隐藏不需要打印的元素 */
        .no-print,
        .btn,
        .dropdown,
        .week-selector,
        .alert,
        .modal,
        .loading-overlay,
        .card-header .dropdown,
        .d-sm-flex,
        .save-status {
            display: none !important;
        }

        /* A4横向布局 */
        @page {
            size: A4 landscape;
            margin: 1cm;
        }

        body {
            font-size: 12px;
            line-height: 1.3;
            color: #000;
            background: white;
        }

        .container-fluid {
            max-width: none;
            padding: 0;
        }

        /* 打印标题 */
        .print-header {
            display: block !important;
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .print-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .print-subtitle {
            font-size: 14px;
            margin-bottom: 5px;
        }

        /* 菜单表格打印样式 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            page-break-inside: avoid;
        }

        .menu-table th,
        .menu-table td {
            border: 2px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
            font-size: 11px;
            line-height: 1.4;
        }

        .menu-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }

        /* 日期列样式 */
        .menu-table td:first-child {
            width: 12%;
            text-align: center;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        /* 餐次列样式 */
        .menu-table td:not(:first-child) {
            width: 29.33%;
            min-height: 80px;
            position: relative;
        }

        /* 菜品显示样式 */
        .menu-input {
            border: none;
            background: transparent;
            padding: 0;
            margin: 0;
            min-height: auto;
            font-size: 11px;
            line-height: 1.4;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        /* 打印时菜品项样式 */
        .recipe-item-display {
            display: inline-block;
            background-color: transparent;
            border: 1px solid #666;
            padding: 3px 6px;
            margin: 1px;
            border-radius: 3px;
            font-size: 10px;
            line-height: 1.3;
            page-break-inside: avoid;
        }

        /* 确保表格在一页内 */
        .card {
            border: none;
            /* box-shadow: none; */ /* 移除阴影效果 */
        }

        .card-body {
            padding: 0;
        }

        .table-responsive {
            overflow: visible;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-end mb-4">
        <table class="action-buttons-table">
            <tr>
                {% if is_editable %}
                    {% if existing_menu %}
                        <td>
                            <button id="saveMenuBtn" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存菜单
                            </button>
                        </td>
                        {% if existing_menu.status == '计划中' %}
                            <td>
                                <button id="publishMenuBtn" class="btn btn-success">
                                    <i class="fas fa-check"></i> 发布菜单
                                </button>
                            </td>
                        {% else %}
                            <td>
                                <button id="unpublishMenuBtn" class="btn btn-warning">
                                    <i class="fas fa-undo"></i> 解除发布
                                </button>
                            </td>
                        {% endif %}
                    {% else %}
                        <!-- 只在本周和下周显示创建菜单按钮 -->
                        {% if week_start >= this_week_monday|string %}
                            <td>
                                <button id="createMenuBtn" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> 创建菜单
                                </button>
                            </td>
                        {% endif %}
                    {% endif %}
                {% endif %}
                {% if existing_menu %}
                    <td>
                        <a href="{{ url_for('weekly_menu_v2.print_menu', id=existing_menu.id) }}" target="_blank" class="btn btn-info">
                            <i class="fas fa-print"></i> 打印菜单
                        </a>
                    </td>
                {% else %}
                    <td>
                        <button class="btn btn-info" disabled title="请先创建菜单才能使用专业打印格式">
                            <i class="fas fa-print"></i> 打印菜单
                        </button>
                    </td>
                {% endif %}
                <td>
                    <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-secondary">
                        <i class="fas fa-list"></i> 返回列表
                    </a>
                </td>
            </tr>
        </table>
    </div>

    <!-- 打印标题（仅在打印时显示） -->
    <div class="print-header" style="display: none;">
        <div class="print-title">{{ area.name }}周菜单安排表</div>
        <div class="print-subtitle">{{ week_start }} 至 {{ week_end }}</div>
        <div class="print-subtitle">制表日期：<span id="print-date"></span></div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold text-primary">{{ area.name }} 周菜单计划 ({{ week_start }} 至 {{ week_end }})</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">菜单操作:</div>
                    <a class="dropdown-item" href="{{ url_for('weekly_menu_v2.copy', area_id=area_id) }}">
                        <i class="fas fa-copy fa-sm fa-fw me-2 text-gray-400"></i>
                        复制历史菜单
                    </a>
                    {% if existing_menu %}
                        <a class="dropdown-item" href="{{ url_for('weekly_menu_v2.view', id=existing_menu.id) }}">
                            <i class="fas fa-eye fa-sm fa-fw me-2 text-gray-400"></i>
                            查看菜单详情
                        </a>
                        {% if existing_menu.status == '已发布' %}
                            <a class="dropdown-item" href="{{ url_for('purchase_order.create_from_menu', weekly_menu_id=existing_menu.id) }}">
                                <i class="fas fa-shopping-cart fa-sm fa-fw me-2 text-gray-400"></i>
                                创建采购订单
                            </a>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 周次选择器 -->
            <div class="week-selector mb-4">
                {% for week in available_weeks %}
                    <div class="week-item {% if week.start_date == week_start %}active{% endif %}{% if not week.is_editable %} not-editable{% endif %}"
                         data-week="{{ week.start_date }}"
                         data-editable="{{ week.is_editable }}"
                         data-status="{{ week.status }}">
                        {{ week.display_text }}
                        <span class="badge bg-{{ week.status|status_class }}">{{ week.status }}</span>
                    </div>
                {% endfor %}
            </div>

            {% if not existing_menu and is_editable %}
                <!-- 创建菜单提示 -->
                <div id="createMenuPrompt" class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 当前周次没有菜单计划，请点击"创建菜单"按钮创建新的菜单计划。
                </div>
                <!-- 调试信息 -->
                <div class="alert alert-secondary small">
                    <strong>调试信息:</strong><br>
                    区域ID: {{ area_id }}<br>
                    周开始日期: {{ week_start }}<br>
                    用户ID: {{ current_user.id }}<br>
                    用户名: {{ current_user.name }}<br>
                    用户角色: {{ current_user.roles|map(attribute='name')|join(', ') }}
                </div>

                <!-- 错误信息显示区域 -->
                <div id="errorMessages" class="alert alert-danger" style="display: none;"></div>

                <!-- AJAX请求状态显示区域 -->
                <div id="ajaxStatus" class="alert alert-info" style="display: none;"></div>
            {% endif %}

            <!-- 菜单表单 -->
            <form id="menuForm" method="POST" action="{{ url_for('weekly_menu_v2.plan', area_id=area_id, week_start=week_start) }}" {% if not existing_menu %}style="display: none;"{% endif %}>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" id="area-id" name="area_id" value="{{ area_id }}">
                <input type="hidden" id="week-start" name="week_start" value="{{ week_start }}">
                <input type="hidden" id="menu-id" name="menu_id" value="{{ existing_menu.id if existing_menu else '' }}">
                <input type="hidden" id="menuData" name="menu_data" value="{{ menu_data|tojson }}">

                <div class="table-responsive">
                    <table class="menu-table">
                        <thead>
                            <tr>
                                <th width="15%">日期</th>
                                <th width="28%">早餐</th>
                                <th width="28%">午餐</th>
                                <th width="28%">晚餐</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date, info in week_dates.items() %}
                                <tr>
                                    <td>{{ info.date }}<br>{{ info.weekday }}</td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="早餐">
                                            {% if menu_data and date in menu_data and '早餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['早餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="午餐">
                                            {% if menu_data and date in menu_data and '午餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['午餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="晚餐">
                                            {% if menu_data and date in menu_data and '晚餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['晚餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="save-status"></div>
            </form>
        </div>
    </div>
</div>

<!-- 菜品选择模态框 -->
<!-- 纯CSS+JS菜品选择窗口 -->
<div class="menu-selector" id="menuModal" style="display: none;">
    <div class="menu-window">
        <div class="menu-window-header">
            <div class="menu-window-title">
                <i class="fas fa-utensils"></i>
                <span id="modalTitle">选择菜品</span>
            </div>
            <div class="menu-window-controls">
                <button class="menu-btn-close" onclick="closeMenuModal()">×</button>
            </div>
        </div>
        <div class="menu-content">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 fw-bold text-primary">已选菜品</h6>
                            </div>
                            <div class="card-body">
                                <div id="selectedDishes"></div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 fw-bold text-primary">添加自定义菜品</h6>
                            </div>
                            <div class="card-body">
                                <div class="input-group">
                                    <input type="text" id="customDishInput" class="form-control" placeholder="输入菜品名称">
                                    <button id="addCustomDishBtn" class="btn btn-primary" type="button">
                                        <i class="fas fa-plus"></i> 添加
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="m-0 fw-bold text-primary">菜品列表</h6>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" id="recipeSearch" class="form-control" placeholder="搜索菜品">
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 来源筛选按钮 -->
                                <div class="btn-group mb-3" role="group">
                                    <button type="button" class="btn btn-outline-success active" id="schoolRecipesBtn">
                                        <i class="fas fa-school"></i> 学校食谱
                                    </button>
                                    <button type="button" class="btn btn-outline-info" id="systemRecipesBtn">
                                        <i class="fas fa-globe"></i> 系统食谱
                                    </button>
                                </div>

                                <ul class="nav nav-tabs" id="recipeCategories">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-category="all" href="#">全部</a>
                                    </li>
                                    {% for category, recipes in recipes_by_category.items() %}
                                        <li class="nav-item">
                                            <a class="nav-link" data-category="{{ category }}" href="#">{{ category }}</a>
                                        </li>
                                    {% endfor %}
                                </ul>

                                <div class="tab-content mt-3">
                                    <div class="tab-pane fade show active">
                                        <div class="row">
                                            <!-- 食谱卡片将由JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <div class="menu-footer">
            <button type="button" class="menu-btn menu-btn-secondary" onclick="closeMenuModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" id="saveSelectionBtn" class="menu-btn menu-btn-primary">
                <i class="fas fa-check"></i> 保存选择
            </button>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/weekly_menu_v2.js') }}"></script>
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 强制隐藏加载遮罩
    $('.loading-overlay').hide().css({
        'display': 'none !important',
        'visibility': 'hidden',
        'pointer-events': 'none'
    });

    // 来源筛选功能
    $('#schoolRecipesBtn, #systemRecipesBtn').on('click', function() {
        // 切换按钮状态
        $('#schoolRecipesBtn, #systemRecipesBtn').removeClass('active');
        $(this).addClass('active');

        const source = $(this).attr('id') === 'schoolRecipesBtn' ? 'school' : 'system';

        // 发送AJAX请求获取筛选后的食谱
        $.ajax({
            url: '/weekly-menu-v2/api/recipes/filter',
            method: 'GET',
            data: { source: source },
            success: function(response) {
                if (response.success) {
                    updateCategoryTabs(response.data);
                    updateRecipeCards(response.data);
                } else {
                    loadBackupRecipes(source);
                }
            },
            error: function(xhr) {
                loadBackupRecipes(source);
            }
        });
    });

    // 更新分类标签
    function updateCategoryTabs(recipesByCategory) {
        const $categories = $('#recipeCategories');
        $categories.empty();

        // 添加"全部"标签
        $categories.append('<li class="nav-item"><a class="nav-link active" data-category="all" href="#">全部</a></li>');

        // 添加分类标签
        Object.keys(recipesByCategory).forEach(category => {
            $categories.append(`<li class="nav-item"><a class="nav-link" data-category="${category}" href="#">${category}</a></li>`);
        });
    }

    // 更新食谱卡片
    function updateRecipeCards(recipesByCategory) {
        const $container = $('.tab-content .row');
        $container.empty();

        const allRecipes = Object.values(recipesByCategory)
            .filter(recipes => recipes.length > 0)
            .flat();

        if (allRecipes.length === 0) {
            return;
        }

        allRecipes.forEach(recipe => {
            const safeName = $('<div>').text(recipe.display_name || recipe.name).html();
            const safeCategory = $('<div>').text(recipe.category).html();

            const cardHtml = `
                <div class="recipe-card-item" style="display: inline-block; width: 200px; margin: 10px; vertical-align: top;" data-category="${safeCategory}">
                    <div class="card recipe-card" data-id="${recipe.id}" data-category="${safeCategory}" onclick="selectRecipe(${recipe.id}, '${safeName}', '${safeCategory}')">
                        <div class="card-body">
                            <h6 class="card-title">${safeName}</h6>
                            <small class="text-muted">${safeCategory}</small>
                        </div>
                    </div>
                </div>
            `;
            $container.append(cardHtml);
        });
    }

    // 后备数据加载（使用模板中的数据）
    function loadBackupRecipes(source) {


        const backupRecipes = {
            {% for category, recipes in recipes_by_category.items() %}
            "{{ category }}": [
                {% for recipe in recipes %}
                {
                    id: {{ recipe.id }},
                    name: "{{ recipe.name }}",
                    display_name: "{{ recipe.name }}",
                    category: "{{ recipe.category }}",
                    is_global: {{ recipe.is_global|lower }},
                    area_id: {{ recipe.area_id or 'null' }}
                }{% if not loop.last %},{% endif %}
                {% endfor %}
            ]{% if not loop.last %},{% endif %}
            {% endfor %}
        };





        // 根据来源筛选
        let filteredRecipes = {};
        if (source === 'school') {
            // 只显示学校食谱（area_id不为null且is_global为false）
            Object.keys(backupRecipes).forEach(category => {
                const schoolRecipes = backupRecipes[category].filter(recipe =>
                    recipe.area_id !== null && !recipe.is_global
                );
                if (schoolRecipes.length > 0) {
                    filteredRecipes[category] = schoolRecipes;
                }
            });
        } else if (source === 'system') {
            // 只显示系统食谱（is_global为true或area_id为null）
            Object.keys(backupRecipes).forEach(category => {
                const systemRecipes = backupRecipes[category].filter(recipe =>
                    recipe.is_global || recipe.area_id === null
                );
                if (systemRecipes.length > 0) {
                    filteredRecipes[category] = systemRecipes;
                }
            });
        } else {
            filteredRecipes = backupRecipes;
        }

        if (Object.keys(filteredRecipes).length === 0) {
            filteredRecipes = backupRecipes;
        }

        updateCategoryTabs(filteredRecipes);
        updateRecipeCards(filteredRecipes);
    }

    // 分类筛选功能
    $(document).on('click', '#recipeCategories a', function(e) {
        e.preventDefault();

        // 更新激活状态
        $('#recipeCategories a').removeClass('active');
        $(this).addClass('active');

        const selectedCategory = $(this).data('category');

        // 筛选显示
        if (selectedCategory === 'all') {
            $('.recipe-card-item').show();
        } else {
            $('.recipe-card-item').hide();
            $(`.recipe-card-item[data-category="${selectedCategory}"]`).show();
        }
    });

    // 搜索功能
    $('#recipeSearch').on('input', function() {
        const keyword = $(this).val().toLowerCase().trim();

        if (keyword === '') {
            $('.recipe-card-item').show();
        } else {
            $('.recipe-card-item').each(function() {
                const recipeName = $(this).find('.card-title').text().toLowerCase();
                if (recipeName.includes(keyword)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
    });

    // 自定义菜品输入框回车键支持
    $('#customDishInput').on('keypress', function(e) {
        if (e.which === 13) { // 回车键
            e.preventDefault();
            $('#addCustomDishBtn').click();
        }
    });

    window.selectRecipe = function(recipeId, recipeName, category) {
        const recipe = {
            id: recipeId,
            name: recipeName,
            recipe_id: recipeId,
            recipe_name: recipeName,
            is_custom: false,
            category: category || '未分类'
        };

        addToSelectedDishes(recipe);

        const recipeCard = document.querySelector(`.recipe-card[data-id="${recipeId}"]`);
        if (recipeCard) {
            recipeCard.style.backgroundColor = '#d4edda';
            recipeCard.style.borderColor = '#28a745';
            recipeCard.style.borderWidth = '2px';
        }
    };

    // 设置打印日期
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const printDate = `${year}年${month}月${day}日`;
    $('#print-date').text(printDate);

    // 优化打印功能
    window.addEventListener('beforeprint', function() {
        // 打印前的处理
        document.title = '{{ area.name }}周菜单安排表_{{ week_start }}至{{ week_end }}';
    });

    window.addEventListener('afterprint', function() {
        // 打印后的处理
        document.title = '{{ title }}';
    });

    // 保存选择按钮事件
    $('#saveSelectionBtn').on('click', function() {
        if (!window.currentMenuSelection || !window.currentMenuSelection.date || !window.currentMenuSelection.meal) {
            return;
        }

        const { date, meal } = window.currentMenuSelection;

        const selectedTags = document.querySelectorAll('#selectedDishes [data-id]');
        console.log('找到的已选菜品标签数量:', selectedTags.length);

        const selectedRecipes = Array.from(selectedTags).map(tag => {
            // 获取菜品名称，排除关闭按钮的文本
            const nameText = tag.textContent.replace('×', '').trim();
            console.log('处理菜品:', nameText, 'ID:', tag.dataset.id);
            return {
                id: tag.dataset.id,
                name: nameText,
                recipe_id: tag.dataset.id,
                recipe_name: nameText,
                is_custom: tag.dataset.id.startsWith('custom_')
            };
        });

        console.log('处理后的菜品数组:', selectedRecipes);

        if (selectedRecipes.length === 0) {
            alert('请先选择菜品');
            return;
        }

        // 更新对应的菜单格子显示
        const menuInput = document.querySelector(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);
        if (menuInput) {
            // 清空原有内容
            menuInput.innerHTML = '';

            // 添加选择的菜品
            selectedRecipes.forEach(recipe => {
                const recipeDiv = document.createElement('div');
                recipeDiv.className = 'recipe-item-display';
                recipeDiv.textContent = recipe.name;
                menuInput.appendChild(recipeDiv);
            });

            // 添加has-recipes类
            menuInput.classList.add('has-recipes');

            // 同步数据到MenuDataManager（如果存在）
            if (typeof MenuDataManager !== 'undefined' && MenuDataManager.setRecipes) {
                console.log('同步到MenuDataManager:', date, meal, selectedRecipes);
                MenuDataManager.setRecipes(date, meal, selectedRecipes);
            }

            // 直接调用RecipeSelector.saveSelection()来保存数据
            if (typeof RecipeSelector !== 'undefined' && RecipeSelector.saveSelection) {
                // 设置当前状态
                RecipeSelector.state.currentDate = date;
                RecipeSelector.state.currentMeal = meal;
                // 设置选择的菜品
                RecipeSelector.selectedRecipes.clear();
                selectedRecipes.forEach(recipe => {
                    RecipeSelector.selectedRecipes.set(recipe.id, recipe);
                });
                // 调用保存
                RecipeSelector.saveSelection();
            }
        }

        // 关闭窗口
        closeMenuModal();
    });

    // 纯CSS+JS模态框控制函数
    window.showMenuModal = function(date, meal) {
        window.currentMenuSelection = {
            date: date,
            meal: meal
        };

        // 更新窗口标题
        const titleElement = document.getElementById('modalTitle');
        if (titleElement) {
            titleElement.textContent = `${date} ${meal} 菜品选择`;
        }

        // 清空已选菜品区域
        const selectedContainer = document.getElementById('selectedDishes');
        if (selectedContainer) {
            selectedContainer.innerHTML = '';
        }

        // 显示窗口
        const modal = document.getElementById('menuModal');
        if (modal) {
            modal.style.display = 'block';
            bindKeyboardShortcuts();
            setTimeout(() => {
                loadBackupRecipes('school');
            }, 100);
        }
    };

    window.closeMenuModal = function() {
        const modal = document.getElementById('menuModal');
        if (modal) {
            modal.style.display = 'none';
        }
        window.currentMenuSelection = null;
        $(document).off('keydown.menuModal');
    };

    // 键盘快捷键支持
    function bindKeyboardShortcuts() {
        $(document).on('keydown.menuModal', function(e) {
            const modal = document.getElementById('menuModal');
            if (!modal || modal.style.display === 'none') return;

            switch(e.which) {
                case 27: // ESC键关闭模态框
                    e.preventDefault();
                    closeMenuModal();
                    break;
                case 13: // 回车键保存选择
                    if (e.ctrlKey) {
                        e.preventDefault();
                        $('#saveSelectionBtn').click();
                    }
                    break;
                case 70: // Ctrl+F 聚焦搜索框
                    if (e.ctrlKey) {
                        e.preventDefault();
                        $('#recipeSearch').focus();
                    }
                    break;
            }
        });
    }

    // 真正的纯CSS+JS事件绑定
    document.addEventListener('DOMContentLoaded', function() {
        // 绑定所有菜单格子点击事件
        const menuInputs = document.querySelectorAll('.menu-input');
        menuInputs.forEach(input => {
            input.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.classList.contains('readonly')) {
                    return;
                }

                const date = this.getAttribute('data-date');
                const meal = this.getAttribute('data-meal');

                // 直接调用纯CSS+JS模态框
                showMenuModal(date, meal);
            });
        });
    });

    // 自定义菜品添加功能
    $('#addCustomDishBtn').on('click', function() {
        const input = document.getElementById('customDishInput');
        const dishName = input.value.trim();

        if (!dishName) {
            return;
        }

        // 生成自定义菜品ID
        const customId = 'custom_' + Date.now();

        // 创建自定义菜品对象
        const customRecipe = {
            id: customId,
            name: dishName,
            recipe_id: customId,
            recipe_name: dishName,
            is_custom: true,
            category: '自定义'
        };

        // 添加到已选菜品区域
        addToSelectedDishes(customRecipe);

        // 清空输入框
        input.value = '';

        console.log('添加自定义菜品:', customRecipe);
    });

    // 添加到已选菜品区域的函数
    function addToSelectedDishes(recipe) {
        const selectedContainer = document.getElementById('selectedDishes');

        if (!selectedContainer) {
            console.error('未找到已选菜品容器');
            return;
        }

        // 检查是否已经选择
        const existingTag = selectedContainer.querySelector(`[data-id="${recipe.id}"]`);
        if (existingTag) {
            // 高亮已存在的标签
            existingTag.style.backgroundColor = '#ffc107';
            existingTag.style.color = '#000';
            existingTag.style.transform = 'scale(1.1)';
            setTimeout(() => {
                existingTag.style.backgroundColor = '';
                existingTag.style.color = '';
                existingTag.style.transform = '';
            }, 1000);

            return;
        }

        // 检查选择数量限制（可选）
        const currentCount = selectedContainer.querySelectorAll('[data-id]').length;
        if (currentCount >= 10) {
            return;
        }

        // 安全处理菜品名称
        const safeName = $('<div>').text(recipe.name).html();
        const safeId = $('<div>').text(recipe.id).html();

        // 创建已选菜品标签
        const tagElement = document.createElement('span');
        tagElement.className = 'badge bg-success me-1 mb-1 d-inline-flex align-items-center';
        tagElement.setAttribute('data-id', recipe.id);
        tagElement.innerHTML = `
            ${safeName}
            <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;"
                    onclick="removeSelectedRecipe('${safeId}')"></button>
        `;

        selectedContainer.appendChild(tagElement);

        // 添加动画效果
        tagElement.style.opacity = '0';
        tagElement.style.transform = 'scale(0.8)';
        setTimeout(() => {
            tagElement.style.transition = 'all 0.3s ease';
            tagElement.style.opacity = '1';
            tagElement.style.transform = 'scale(1)';
        }, 10);


    }



    // 移除已选菜品
    window.removeSelectedRecipe = function(recipeId) {
        console.log('移除菜品:', recipeId);

        // 移除标签
        const tag = document.querySelector(`#selectedDishes [data-id="${recipeId}"]`);
        if (tag) {
            tag.remove();
        }

        // 恢复卡片样式（如果是食谱卡片）
        const recipeCard = document.querySelector(`.recipe-card[data-id="${recipeId}"]`);
        if (recipeCard) {
            recipeCard.style.backgroundColor = '';
            recipeCard.style.borderColor = '';
            recipeCard.style.borderWidth = '';
        }
    };

    // 按钮点击效果增强
    $('.action-buttons .btn').on('click', function() {
        $(this).addClass('btn-clicked');
        setTimeout(() => {
            $(this).removeClass('btn-clicked');
        }, 200);
    });

    // 添加按钮点击动画CSS
    $('<style>').text(`
        .btn-clicked {
            transform: scale(0.95) !important;
            transition: transform 0.1s ease !important;
        }

        .action-buttons .btn:active {
            transform: scale(0.98);
        }

        .btn-group .btn:active {
            transform: scale(0.98);
        }

        /* 禁用按钮样式优化 */
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }
    `).appendTo('head');
});
</script>
{% endblock %}
