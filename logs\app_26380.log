2025-06-16 19:04:01,377 INFO: 应用启动 - PID: 26380 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-16 19:04:20,285 INFO: 收到创建周菜单请求: b'{"area_id":"42","week_start":"2025-06-16"}' [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-16 19:04:20,285 INFO: 创建周菜单参数: area_id=42, week_start=2025-06-16 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-16 19:04:20,285 INFO: 检查用户权限: user_id=34, area_id=42 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-16 19:04:20,285 INFO: 用户角色: ['学校管理员'] [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-16 19:04:20,289 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-16 19:04:20,289 INFO: 开始创建周菜单: area_id=42, week_start=2025-06-16, created_by=34 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-16 19:04:20,289 INFO: 开始创建周菜单: area_id=42, week_start=2025-06-16, created_by=34 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-16 19:04:20,289 INFO: 转换后的日期对象: 2025-06-16, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-16 19:04:20,289 INFO: 计算的周结束日期: 2025-06-22 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-16 19:04:20,289 INFO: 检查是否已存在该周的菜单: area_id=42, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-16 19:04:20,289 INFO: 获取周菜单: area_id=42, week_start=2025-06-16, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-16 19:04:20,290 INFO: 使用优化后的查询: area_id=42, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-16 19:04:20,290 INFO: 执行主SQL查询: area_id=42, week_start_str=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-16 19:04:20,293 INFO: 主查询未找到菜单: area_id=42, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-16 19:04:20,294 INFO: 使用日期字符串: week_start_str=2025-06-16, week_end_str=2025-06-22 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-16 19:04:20,294 INFO: 准备执行SQL创建菜单 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-16 19:04:20,295 INFO: SQL参数: {'area_id': '42', 'week_start_str': '2025-06-16', 'week_end_str': '2025-06-22', 'status': '计划中', 'created_by': 34} [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-16 19:04:20,295 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-16 19:04:20,300 INFO: SQL执行成功，获取到ID: 42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-16 19:04:20,300 INFO: 检查数据库连接状态 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-16 19:04:20,301 INFO: 数据库连接正常 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-16 19:04:20,301 INFO: 事务提交成功 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-16 19:04:20,302 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-16 19:04:20,304 INFO: 验证成功: 菜单已创建 ID=42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-16 19:04:20,304 INFO: 周菜单创建成功: id=42 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-16 19:04:20,305 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 42, 'status': '计划中'} [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
