{% extends 'base.html' %}

{% block title %}控制面板 - {{ super() }}{% endblock %}

{% block page_title %}控制面板{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <p class="text-muted">欢迎回来，{{ current_user.real_name or current_user.username }}</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-3 col-md-6 col-12 mobile-mb-2">
        <div class="card text-white bg-primary mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">供应商</h6>
                        <h2 class="mb-0">{{ suppliers_count }}</h2>
                    </div>
                    <i class="fas fa-building fa-3x mobile-hidden"></i>
                    <i class="fas fa-building fa-2x mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="{{ url_for('main.suppliers') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mobile-mb-2">
        <div class="card text-white bg-success mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">食材</h6>
                        <h2 class="mb-0">{{ ingredients_count }}</h2>
                    </div>
                    <i class="fas fa-carrot fa-3x mobile-hidden"></i>
                    <i class="fas fa-carrot fa-2x mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="{{ url_for('main.ingredients') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mobile-mb-2">
        <div class="card text-white bg-warning mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">食谱</h6>
                        <h2 class="mb-0">{{ recipes_count }}</h2>
                    </div>
                    <i class="fas fa-utensils fa-3x mobile-hidden"></i>
                    <i class="fas fa-utensils fa-2x mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="{{ url_for('main.recipes') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mobile-mb-2">
        <div class="card text-white bg-danger mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">留样</h6>
                        <h2 class="mb-0">{{ samples_count }}</h2>
                    </div>
                    <i class="fas fa-vial fa-3x mobile-hidden"></i>
                    <i class="fas fa-vial fa-2x mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="{{ url_for('main.food_samples') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">最近采购订单</h5>
            </div>
            <div class="card-body">
                <!-- 桌面端表格 -->
                <div class="table-responsive d-none d-md-block">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>供应商</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>日期</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>{{ order.id }}</td>
                                <td>{{ order.supplier.name }}</td>
                                <td>¥{{ order.total_amount }}</td>
                                <td>
                                    {% if order.status == '待审核' %}
                                    <span class="badge bg-warning">{{ order.status }}</span>
                                    {% elif order.status == '已发货' %}
                                    <span class="badge bg-info">{{ order.status }}</span>
                                    {% elif order.status == '已完成' %}
                                    <span class="badge bg-success">{{ order.status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ order.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{  order.order_date|format_datetime('%Y-%m-%d')  }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无采购订单</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 移动端卡片 -->
                <div class="d-md-none">
                    {% for order in recent_orders %}
                    <div class="card mb-2 border-start-primary">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-8">
                                    <h6 class="mb-1">订单 #{{ order.id }}</h6>
                                    <small class="text-muted">{{ order.supplier.name }}</small>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="fw-bold">¥{{ order.total_amount }}</div>
                                    {% if order.status == '待审核' %}
                                    <span class="badge bg-warning">{{ order.status }}</span>
                                    {% elif order.status == '已发货' %}
                                    <span class="badge bg-info">{{ order.status }}</span>
                                    {% elif order.status == '已完成' %}
                                    <span class="badge bg-success">{{ order.status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ order.status }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mt-1">
                                <div class="col-12">
                                    <small class="text-muted">{{ order.order_date|format_datetime('%Y-%m-%d') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无采购订单</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="{{ url_for('main.purchase_orders') }}" class="btn btn-sm btn-primary">查看所有订单</a>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">快捷操作</h5>
            </div>
            <div class="card-body">
                <!-- 桌面端列表 -->
                <div class="list-group d-none d-md-block">
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 添加供应商
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 添加食材
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 添加食谱
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 创建采购订单
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 添加留样记录
                    </a>
                </div>

                <!-- 移动端按钮 -->
                <div class="action-buttons d-md-none">
                    <a href="#" class="btn btn-outline-primary">
                        <i class="fas fa-plus-circle"></i> 添加供应商
                    </a>
                    <a href="#" class="btn btn-outline-success">
                        <i class="fas fa-plus-circle"></i> 添加食材
                    </a>
                    <a href="#" class="btn btn-outline-warning">
                        <i class="fas fa-plus-circle"></i> 添加食谱
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-plus-circle"></i> 创建采购订单
                    </a>
                    <a href="#" class="btn btn-outline-danger">
                        <i class="fas fa-plus-circle"></i> 添加留样记录
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
