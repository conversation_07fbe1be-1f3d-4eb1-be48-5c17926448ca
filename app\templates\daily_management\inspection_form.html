{% extends 'base.html' %}

{% block title %}检查记录表单 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .inspection-form .mb-3 {
        margin-bottom: 1rem;
    }
    .inspection-form .invalid-feedback {
        display: block;
    }
    .inspection-type-selector {
        margin-bottom: 1.5rem;
    }
    .inspection-type-selector .btn {
        margin-right: 0.5rem;
    }
    .inspection-item-card {
        margin-bottom: 1rem;
    }
    .inspection-status-toggle {
        margin-top: 0.5rem;
    }

    .inspection-form .mb-3 {
        margin-bottom: 1rem;
    }
    .inspection-form .invalid-feedback {
        display: block;
    }
    .inspection-type-selector {
        margin-bottom: 1.5rem;
    }
    .inspection-type-selector .btn {
        margin-right: 0.5rem;
    }
    .inspection-item-card {
        margin-bottom: 1rem;
    }
    .inspection-status-toggle {
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if inspection %}
                        编辑检查记录
                        {% else %}
                        新建检查记录
                        {% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <form id="inspectionForm" class="inspection-form" method="POST" action="{{ url_for('inspection_api.create_inspection') if not inspection else url_for('inspection_api.update_inspection', inspection_id=inspection.id) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="daily_log_id" value="{{ daily_log.id }}">
                        
                        <div class="mb-3">
                            <label for="inspectionType">检查类型</label>
                            <div class="inspection-type-selector">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary {% if inspection and inspection.inspection_type == 'morning' %}active{% endif %}" data-value="morning">早餐</button>
                                    <button type="button" class="btn btn-outline-primary {% if inspection and inspection.inspection_type == 'noon' %}active{% endif %}" data-value="noon">午餐</button>
                                    <button type="button" class="btn btn-outline-primary {% if inspection and inspection.inspection_type == 'evening' %}active{% endif %}" data-value="evening">晚餐</button>
                                </div>
                                <input type="hidden" name="inspection_type" id="inspectionType" value="{{ inspection.inspection_type if inspection else '' }}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="inspectionItem">检查项目</label>
                            <select class="form-control" id="inspectionItem" name="inspection_item" required>
                                <option value="">请选择检查项目</option>
                                <option value="地面卫生" {% if inspection and inspection.inspection_item == '地面卫生' %}selected{% endif %}>地面卫生</option>
                                <option value="操作台卫生" {% if inspection and inspection.inspection_item == '操作台卫生' %}selected{% endif %}>操作台卫生</option>
                                <option value="设备卫生" {% if inspection and inspection.inspection_item == '设备卫生' %}selected{% endif %}>设备卫生</option>
                                <option value="食材存储" {% if inspection and inspection.inspection_item == '食材存储' %}selected{% endif %}>食材存储</option>
                                <option value="人员卫生" {% if inspection and inspection.inspection_item == '人员卫生' %}selected{% endif %}>人员卫生</option>
                                <option value="餐具消毒" {% if inspection and inspection.inspection_item == '餐具消毒' %}selected{% endif %}>餐具消毒</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label>检查状态</label>
                            <div class="inspection-status-toggle">
                                <div class="form-check form-check-inline">
                                    <input type="radio" id="statusNormal" name="status" value="normal" class="form-check-input" {% if not inspection or inspection.status == 'normal' %}checked{% endif %}>
                                    <label class="form-check-label" for="statusNormal">正常</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input type="radio" id="statusAbnormal" name="status" value="abnormal" class="form-check-input" {% if inspection and inspection.status == 'abnormal' %}checked{% endif %}>
                                    <label class="form-check-label" for="statusAbnormal">异常</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description">检查描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ inspection.description if inspection else '' }}</textarea>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                {% if inspection %}
                                保存修改
                                {% else %}
                                提交检查
                                {% endif %}
                            </button>
                            <a href="{{ url_for('daily_management.inspections', log_id=daily_log.id) }}" class="btn btn-secondary">返回列表</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/daily_management/inspection_form_validator.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // 检查类型选择器
        const typeButtons = document.querySelectorAll('.inspection-type-selector .btn');
        const typeInput = document.getElementById('inspectionType');
        
        typeButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有按钮的active类
                typeButtons.forEach(btn => btn.classList.remove('active'));
                
                // 添加当前按钮的active类
                this.classList.add('active');
                
                // 设置隐藏输入的值
                typeInput.value = this.getAttribute('data-value');
            });
        });
        
        // 表单提交成功回调
        window.onInspectionFormSuccess = function(data) {
            // 2秒后跳转到检查记录列表页
            setTimeout(function() {
                window.location.href = "{{ url_for('daily_management.inspections', log_id=daily_log.id) }}";
            }, 2000);
        };
    });
</script>
{% endblock %}
