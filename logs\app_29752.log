2025-06-15 20:33:40,393 INFO: 应用启动 - PID: 29752 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-15 20:33:40,958 ERROR: 周菜单操作异常: tag name expected [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\loaders.py", line 137, in load
    code = environment.compile(source, name, filename)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 489, in template
    <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
jinja2.exceptions.TemplateSyntaxError: tag name expected
2025-06-15 20:33:45,677 ERROR: 周菜单操作异常: tag name expected [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\loaders.py", line 137, in load
    code = environment.compile(source, name, filename)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 489, in template
    <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
jinja2.exceptions.TemplateSyntaxError: tag name expected
2025-06-15 20:33:47,693 ERROR: 周菜单操作异常: tag name expected [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\loaders.py", line 137, in load
    code = environment.compile(source, name, filename)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 489, in template
    <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
jinja2.exceptions.TemplateSyntaxError: tag name expected
2025-06-15 20:33:50,042 ERROR: 周菜单操作异常: tag name expected [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\loaders.py", line 137, in load
    code = environment.compile(source, name, filename)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 489, in template
    <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
jinja2.exceptions.TemplateSyntaxError: tag name expected
2025-06-15 20:33:53,555 ERROR: 周菜单操作异常: tag name expected [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\loaders.py", line 137, in load
    code = environment.compile(source, name, filename)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 489, in template
    <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
jinja2.exceptions.TemplateSyntaxError: tag name expected
2025-06-15 20:33:54,989 ERROR: 周菜单操作异常: tag name expected [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\loaders.py", line 137, in load
    code = environment.compile(source, name, filename)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 489, in template
    <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
jinja2.exceptions.TemplateSyntaxError: tag name expected
2025-06-15 20:33:58,202 ERROR: 周菜单操作异常: tag name expected [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\loaders.py", line 137, in load
    code = environment.compile(source, name, filename)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 489, in template
    <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
jinja2.exceptions.TemplateSyntaxError: tag name expected
2025-06-15 20:34:18,016 ERROR: 周菜单操作异常: tag name expected [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\loaders.py", line 137, in load
    code = environment.compile(source, name, filename)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 489, in template
    <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
jinja2.exceptions.TemplateSyntaxError: tag name expected
