{% extends 'base.html' %}

{% block title %}选择角色模板 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <p class="text-muted">从预定义的角色模板中选择一个作为起点，然后根据需要进行自定义。</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system.add_role') }}" class="btn btn-secondary">
            <i class="fas fa-plus"></i> 创建空白角色
        </a>
        <a href="{{ url_for('system.roles') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回角色列表
        </a>
    </div>
</div>

<div class="row">
    {% for key, template in templates.items() %}
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">{{ template.name }}</h5>
            </div>
            <div class="card-body">
                <p>{{ template.description }}</p>

                <h6>包含的权限：</h6>
                <div class="permission-summary">
                    {% for module, actions in template.permissions.items() %}
                    <div class="mb-2">
                        {% if module == '*' %}
                        <span class="badge bg-danger">全局权限</span>
                        {% elif module == 'user' %}
                        <span class="badge bg-primary">用户管理</span>
                        {% elif module == 'role' %}
                        <span class="badge bg-success">角色管理</span>
                        {% elif module == 'area' %}
                        <span class="badge bg-info">区域管理</span>
                        {% elif module == 'supplier' %}
                        <span class="badge bg-warning">供应商管理</span>
                        {% elif module == 'ingredient' %}
                        <span class="badge bg-secondary">食材管理</span>
                        {% elif module == 'menu' %}
                        <span class="badge bg-dark">食谱管理</span>
                        {% elif module == 'sample' %}
                        <span class="badge bg-light">留样管理</span>
                        {% elif module == 'setting' %}
                        <span class="badge bg-primary">系统设置</span>
                        {% elif module == 'log' %}
                        <span class="badge bg-secondary">日志管理</span>
                        {% elif module == 'report' %}
                        <span class="badge bg-info">报表管理</span>
                        {% elif module == 'purchase' %}
                        <span class="badge bg-warning">采购管理</span>
                        {% elif module == 'purchase_order' %}
                        <span class="badge bg-warning">采购订单管理</span>
                        {% elif module == 'carousel' %}
                        <span class="badge bg-primary">轮播图管理</span>
                        {% elif module == 'consultation' %}
                        <span class="badge bg-success">在线咨询管理</span>
                        {% else %}
                        <span class="badge bg-secondary">{{ module }}</span>
                        {% endif %}

                        {% if '*' in actions %}
                        <span class="badge rounded-pill bg-danger">所有操作</span>
                        {% else %}
                        {% for action in actions %}
                        <span class="badge rounded-pill bg-secondary">{{ action }}</span>
                        {% endfor %}
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('system.create_role_from_template', template_key=key) }}" class="btn btn-primary w-100">
                    <i class="fas fa-check"></i> 使用此模板
                </a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<div class="card mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">角色模板说明</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>系统管理员</h6>
                <p>拥有系统最高权限，可以执行所有操作。通常分配给系统管理人员。</p>
                <ul>
                    <li>可以管理所有用户和角色</li>
                    <li>可以配置系统设置</li>
                    <li>可以查看和导出系统日志</li>
                    <li>可以管理所有区域的数据</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>超级管理员</h6>
                <p>拥有除系统设置外的所有权限，通常分配给教育局和乡镇官员。</p>
                <ul>
                    <li>可以管理用户，但不能删除用户</li>
                    <li>可以查看角色，但不能修改角色</li>
                    <li>可以管理区域，但不能删除区域</li>
                    <li>可以完全管理供应商、食材、食谱和留样</li>
                </ul>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-6">
                <h6>学校管理员</h6>
                <p>拥有学校级别的管理权限，可以管理本校的食堂和供应商。</p>
                <ul>
                    <li>可以查看用户，但不能修改用户</li>
                    <li>可以查看区域，但不能修改区域</li>
                    <li>可以管理供应商和食材，但不能删除</li>
                    <li>可以查看和审核食谱</li>
                    <li>可以查看留样记录</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>食堂管理员</h6>
                <p>拥有食堂级别的管理权限，负责日常食堂运营。</p>
                <ul>
                    <li>可以查看供应商和食材</li>
                    <li>可以创建和编辑食谱，但不能删除</li>
                    <li>可以管理留样记录</li>
                    <li>可以查看和打印报表</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .permission-summary {
        max-height: 200px;
        overflow-y: auto;
    }

    .permission-summary {
        max-height: 200px;
        overflow-y: auto;
    }
</style>
{% endblock %}
