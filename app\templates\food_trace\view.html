{% extends 'base.html' %}

{% block title %}食材溯源详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-tools">
                        <a href="{{ url_for('food_trace.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <a href="{{ url_for('food_trace.print_samples', area_id=area.id, date=trace_date, meal_type=meal_type) }}" class="btn btn-primary btn-sm" target="_blank">
                            <i class="fas fa-print"></i> 打印留样记录
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-calendar-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">日期</span>
                                    <span class="info-box-number">{{ trace_date }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-utensils"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">餐次</span>
                                    <span class="info-box-number">{{ meal_type }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-school"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">区域</span>
                                    <span class="info-box-number">{{ area.name }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 溯源链详情 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card card-primary card-outline card-tabs">
                                <div class="card-header p-0 pt-1 border-bottom-0">
                                    <ul class="nav nav-tabs" id="trace-tabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="menu-tab" data-bs-toggle="pill" href="#menu" role="tab" aria-controls="menu" aria-selected="true">菜单</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="consumption-tab" data-bs-toggle="pill" href="#consumption" role="tab" aria-controls="consumption" aria-selected="false">消耗计划</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="stock-out-tab" data-bs-toggle="pill" href="#stock-out" role="tab" aria-controls="stock-out" aria-selected="false">出库记录</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="inventory-tab" data-bs-toggle="pill" href="#inventory" role="tab" aria-controls="inventory" aria-selected="false">库存批次</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="stock-in-tab" data-bs-toggle="pill" href="#stock-in" role="tab" aria-controls="stock-in" aria-selected="false">入库记录</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="purchase-tab" data-bs-toggle="pill" href="#purchase" role="tab" aria-controls="purchase" aria-selected="false">采购订单</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="supplier-tab" data-bs-toggle="pill" href="#supplier" role="tab" aria-controls="supplier" aria-selected="false">供应商</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="sample-tab" data-bs-toggle="pill" href="#sample" role="tab" aria-controls="sample" aria-selected="false">留样记录</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="card-body">
                                    <div class="tab-content" id="trace-tabContent">
                                        <!-- 菜单 -->
                                        <div class="tab-pane fade show active" id="menu" role="tabpanel" aria-labelledby="menu-tab">
                                            <h5>菜单信息</h5>
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>菜品名称</th>
                                                            <th>分类</th>
                                                            <th>计划数量</th>
                                                            <th>实际数量</th>
                                                            <th>来源</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for recipe in trace_data.menu.recipes %}
                                                        <tr>
                                                            <td>{{ recipe.name }}</td>
                                                            <td>{{ recipe.category }}</td>
                                                            <td>{{ recipe.planned_quantity|default('-') }}</td>
                                                            <td>{{ recipe.actual_quantity|default('-') }}</td>
                                                            <td>{{ '周菜单' if recipe.source == 'weekly_menu' else '日菜单' }}</td>
                                                        </tr>
                                                        {% else %}
                                                        <tr>
                                                            <td colspan="5" class="text-center">暂无菜品数据</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- 消耗计划 -->
                                        <div class="tab-pane fade" id="consumption" role="tabpanel" aria-labelledby="consumption-tab">
                                            <h5>消耗计划</h5>
                                            {% if trace_data.consumption_plans %}
                                                {% for plan in trace_data.consumption_plans %}
                                                <div class="card mb-3">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">消耗计划 #{{ plan.id }}</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-4">
                                                                <strong>消耗日期：</strong> {{ plan.consumption_date }}
                                                            </div>
                                                            <div class="col-md-4">
                                                                <strong>餐次：</strong> {{ plan.meal_type }}
                                                            </div>
                                                            <div class="col-md-4">
                                                                <strong>状态：</strong> {{ plan.status }}
                                                            </div>
                                                        </div>
                                                        
                                                        <h6>消耗明细</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>食材名称</th>
                                                                        <th>计划消耗量</th>
                                                                        <th>实际消耗量</th>
                                                                        <th>单位</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {% for detail in plan.details %}
                                                                    <tr>
                                                                        <td>{{ detail.ingredient_name }}</td>
                                                                        <td>{{ detail.planned_quantity }}</td>
                                                                        <td>{{ detail.actual_quantity|default('-') }}</td>
                                                                        <td>{{ detail.unit }}</td>
                                                                    </tr>
                                                                    {% else %}
                                                                    <tr>
                                                                        <td colspan="4" class="text-center">暂无消耗明细</td>
                                                                    </tr>
                                                                    {% endfor %}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            {% else %}
                                            <div class="alert alert-info">
                                                暂无消耗计划数据
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- 出库记录 -->
                                        <div class="tab-pane fade" id="stock-out" role="tabpanel" aria-labelledby="stock-out-tab">
                                            <h5>出库记录</h5>
                                            {% if trace_data.stock_outs %}
                                                {% for stock_out in trace_data.stock_outs %}
                                                <div class="card mb-3">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">出库单 #{{ stock_out.stock_out_number }}</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-4">
                                                                <strong>仓库：</strong> {{ stock_out.warehouse_name }}
                                                            </div>
                                                            <div class="col-md-4">
                                                                <strong>出库日期：</strong> {{ stock_out.stock_out_date }}
                                                            </div>
                                                            <div class="col-md-4">
                                                                <strong>状态：</strong> {{ stock_out.status }}
                                                            </div>
                                                        </div>
                                                        
                                                        <h6>出库明细</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>食材名称</th>
                                                                        <th>批次号</th>
                                                                        <th>数量</th>
                                                                        <th>单位</th>
                                                                        <th>有效期</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {% for item in stock_out.items %}
                                                                    <tr>
                                                                        <td>{{ item.ingredient_name }}</td>
                                                                        <td>{{ item.batch_number }}</td>
                                                                        <td>{{ item.quantity }}</td>
                                                                        <td>{{ item.unit }}</td>
                                                                        <td>{{ item.expiry_date }}</td>
                                                                    </tr>
                                                                    {% else %}
                                                                    <tr>
                                                                        <td colspan="5" class="text-center">暂无出库明细</td>
                                                                    </tr>
                                                                    {% endfor %}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            {% else %}
                                            <div class="alert alert-info">
                                                暂无出库记录数据
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- 库存批次 -->
                                        <div class="tab-pane fade" id="inventory" role="tabpanel" aria-labelledby="inventory-tab">
                                            <h5>库存批次</h5>
                                            {% if trace_data.inventory_batches %}
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>批次号</th>
                                                            <th>食材名称</th>
                                                            <th>仓库</th>
                                                            <th>库位</th>
                                                            <th>数量</th>
                                                            <th>单位</th>
                                                            <th>生产日期</th>
                                                            <th>有效期</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for batch in trace_data.inventory_batches %}
                                                        <tr>
                                                            <td>{{ batch.batch_number }}</td>
                                                            <td>{{ batch.ingredient_name }}</td>
                                                            <td>{{ batch.warehouse_name }}</td>
                                                            <td>{{ batch.storage_location_name }}</td>
                                                            <td>{{ batch.quantity }}</td>
                                                            <td>{{ batch.unit }}</td>
                                                            <td>{{ batch.production_date }}</td>
                                                            <td>{{ batch.expiry_date }}</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            {% else %}
                                            <div class="alert alert-info">
                                                暂无库存批次数据
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- 入库记录 -->
                                        <div class="tab-pane fade" id="stock-in" role="tabpanel" aria-labelledby="stock-in-tab">
                                            <h5>入库记录</h5>
                                            {% if trace_data.stock_ins %}
                                                {% for stock_in in trace_data.stock_ins %}
                                                <div class="card mb-3">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">入库单 #{{ stock_in.stock_in_number }}</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-3">
                                                                <strong>仓库：</strong> {{ stock_in.warehouse_name }}
                                                            </div>
                                                            <div class="col-md-3">
                                                                <strong>供应商：</strong> {{ stock_in.supplier_name }}
                                                            </div>
                                                            <div class="col-md-3">
                                                                <strong>入库日期：</strong> {{ stock_in.stock_in_date }}
                                                            </div>
                                                            <div class="col-md-3">
                                                                <strong>状态：</strong> {{ stock_in.status }}
                                                            </div>
                                                        </div>
                                                        
                                                        <h6>入库明细</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>食材名称</th>
                                                                        <th>批次号</th>
                                                                        <th>数量</th>
                                                                        <th>单位</th>
                                                                        <th>单价</th>
                                                                        <th>金额</th>
                                                                        <th>生产日期</th>
                                                                        <th>有效期</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {% for item in stock_in.items %}
                                                                    <tr>
                                                                        <td>{{ item.ingredient_name }}</td>
                                                                        <td>{{ item.batch_number }}</td>
                                                                        <td>{{ item.quantity }}</td>
                                                                        <td>{{ item.unit }}</td>
                                                                        <td>{{ item.unit_price }}</td>
                                                                        <td>{{ item.total_price }}</td>
                                                                        <td>{{ item.production_date }}</td>
                                                                        <td>{{ item.expiry_date }}</td>
                                                                    </tr>
                                                                    {% else %}
                                                                    <tr>
                                                                        <td colspan="8" class="text-center">暂无入库明细</td>
                                                                    </tr>
                                                                    {% endfor %}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            {% else %}
                                            <div class="alert alert-info">
                                                暂无入库记录数据
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- 采购订单 -->
                                        <div class="tab-pane fade" id="purchase" role="tabpanel" aria-labelledby="purchase-tab">
                                            <h5>采购订单</h5>
                                            {% if trace_data.purchase_orders %}
                                                {% for order in trace_data.purchase_orders %}
                                                <div class="card mb-3">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">采购订单 #{{ order.order_number }}</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-3">
                                                                <strong>供应商：</strong> {{ order.supplier_name }}
                                                            </div>
                                                            <div class="col-md-3">
                                                                <strong>区域：</strong> {{ order.area_name }}
                                                            </div>
                                                            <div class="col-md-3">
                                                                <strong>订单日期：</strong> {{ order.order_date }}
                                                            </div>
                                                            <div class="col-md-3">
                                                                <strong>状态：</strong> {{ order.status }}
                                                            </div>
                                                        </div>
                                                        
                                                        <h6>采购明细</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>食材名称</th>
                                                                        <th>数量</th>
                                                                        <th>单位</th>
                                                                        <th>单价</th>
                                                                        <th>金额</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {% for item in order.items %}
                                                                    <tr>
                                                                        <td>{{ item.ingredient_name }}</td>
                                                                        <td>{{ item.quantity }}</td>
                                                                        <td>{{ item.unit }}</td>
                                                                        <td>{{ item.unit_price }}</td>
                                                                        <td>{{ item.total_price }}</td>
                                                                    </tr>
                                                                    {% else %}
                                                                    <tr>
                                                                        <td colspan="5" class="text-center">暂无采购明细</td>
                                                                    </tr>
                                                                    {% endfor %}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            {% else %}
                                            <div class="alert alert-info">
                                                暂无采购订单数据
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- 供应商 -->
                                        <div class="tab-pane fade" id="supplier" role="tabpanel" aria-labelledby="supplier-tab">
                                            <h5>供应商信息</h5>
                                            {% if trace_data.suppliers %}
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>供应商名称</th>
                                                            <th>联系人</th>
                                                            <th>联系电话</th>
                                                            <th>电子邮箱</th>
                                                            <th>地址</th>
                                                            <th>营业执照</th>
                                                            <th>状态</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for supplier in trace_data.suppliers %}
                                                        <tr>
                                                            <td>{{ supplier.name }}</td>
                                                            <td>{{ supplier.contact_person }}</td>
                                                            <td>{{ supplier.phone }}</td>
                                                            <td>{{ supplier.email }}</td>
                                                            <td>{{ supplier.address }}</td>
                                                            <td>{{ supplier.business_license }}</td>
                                                            <td>{{ supplier.status }}</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            {% else %}
                                            <div class="alert alert-info">
                                                暂无供应商数据
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- 留样记录 -->
                                        <div class="tab-pane fade" id="sample" role="tabpanel" aria-labelledby="sample-tab">
                                            <h5>留样记录</h5>
                                            {% if trace_data.food_samples %}
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>留样编号</th>
                                                            <th>食谱名称</th>
                                                            <th>留样图片</th>
                                                            <th>留样数量</th>
                                                            <th>留样时间</th>
                                                            <th>销毁时间</th>
                                                            <th>存放位置</th>
                                                            <th>状态</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for sample in trace_data.food_samples %}
                                                        <tr>
                                                            <td>{{ sample.sample_number }}</td>
                                                            <td>{{ sample.recipe_name }}</td>
                                                            <td>
                                                                {% if sample.sample_image %}
                                                                <a href="{{ url_for('static', filename=sample.sample_image) }}" target="_blank">
                                                                    <img src="{{ url_for('static', filename=sample.sample_image) }}" alt="留样图片" style="max-height: 50px;">
                                                                </a>
                                                                {% else %}
                                                                无图片
                                                                {% endif %}
                                                            </td>
                                                            <td>{{ sample.sample_quantity }} {{ sample.sample_unit }}</td>
                                                            <td>{{ sample.start_time }}</td>
                                                            <td>{{ sample.end_time }}</td>
                                                            <td>{{ sample.storage_location }}</td>
                                                            <td>{{ sample.status }}</td>
                                                        </tr>
                                                        {% else %}
                                                        <tr>
                                                            <td colspan="8" class="text-center">暂无留样记录</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            {% else %}
                                            <div class="alert alert-info">
                                                暂无留样记录数据
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
