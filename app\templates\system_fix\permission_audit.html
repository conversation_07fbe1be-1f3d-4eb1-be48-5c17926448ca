{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ title }}</h2>
        <p class="text-muted">此工具用于检查角色权限是否与实际模块一致，识别可能存在问题的权限设置</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system_fix.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回系统修复
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">权限审计结果</h5>
            </div>
            <div class="card-body">
                {% if audit_results %}
                <div class="alert alert-warning">
                    <p><i class="fas fa-exclamation-triangle"></i> 发现 {{ audit_results|length }} 个角色存在权限问题：</p>
                    <a href="{{ url_for('permission_audit.fix_all_issues') }}" class="btn btn-warning">
                        <i class="fas fa-magic"></i> 自动修复所有问题
                    </a>
                </div>
                
                <div class="accordion" id="accordionAudit">
                    {% for result in audit_results %}
                    <div class="card">
                        <div class="card-header" id="heading{{ result.role.id }}">
                            <h2 class="mb-0">
                                <button class="btn btn-link w-100 text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ result.role.id }}" aria-expanded="true" aria-controls="collapse{{ result.role.id }}">
                                    <i class="fas fa-exclamation-circle text-warning"></i> {{ result.role.name }} ({{ result.issues|length }} 个问题)
                                </button>
                            </h2>
                        </div>
                        
                        <div id="collapse{{ result.role.id }}" class="collapse" aria-labelledby="heading{{ result.role.id }}" data-bs-parent="#accordionAudit">
                            <div class="card-body">
                                <ul class="list-group">
                                    {% for issue in result.issues %}
                                    <li class="list-group-item">
                                        {% if issue.type == 'deprecated' %}
                                        <span class="badge bg-warning">已弃用</span>
                                        {% elif issue.type == 'merged' %}
                                        <span class="badge bg-info">已合并</span>
                                        {% elif issue.type == 'missing' %}
                                        <span class="badge bg-danger">缺失</span>
                                        {% elif issue.type == 'unknown' %}
                                        <span class="badge bg-danger">未知模块</span>
                                        {% elif issue.type == 'unknown_action' %}
                                        <span class="badge bg-danger">未知操作</span>
                                        {% endif %}
                                        {{ issue.message }}
                                    </li>
                                    {% endfor %}
                                </ul>
                                
                                <div class="mt-3">
                                    <a href="{{ url_for('system.edit_role_permissions', id=result.role.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> 编辑权限
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> 恭喜！所有角色的权限设置都符合要求，没有发现问题。
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">权限审计说明</h5>
            </div>
            <div class="card-body">
                <p>权限审计工具会检查以下问题：</p>
                <ul>
                    <li><span class="badge bg-warning">已弃用</span> 使用了已弃用的模块但没有使用替代模块</li>
                    <li><span class="badge bg-info">已合并</span> 使用了已合并的模块但没有使用合并后的模块</li>
                    <li><span class="badge bg-danger">缺失</span> 缺少新模块的权限</li>
                    <li><span class="badge bg-danger">未知模块</span> 使用了不存在的模块</li>
                    <li><span class="badge bg-danger">未知操作</span> 使用了不存在的操作</li>
                </ul>
                
                <p>模块变化说明：</p>
                <ul>
                    <li><strong>菜单计划管理</strong> 已被 <strong>周菜单管理</strong> 模块替代</li>
                    <li><strong>留样管理</strong> 已与 <strong>食材溯源与留样</strong> 模块合并</li>
                    <li>新增 <strong>食堂日常管理</strong> 模块，用于管理食堂日常运营</li>
                </ul>
                
                <p>自动修复会执行以下操作：</p>
                <ul>
                    <li>将拥有"菜单计划管理"权限的角色添加相应的"周菜单管理"权限</li>
                    <li>将拥有"留样管理"权限的角色添加相应的"食材溯源与留样"权限</li>
                    <li>为食堂管理员角色添加"食堂日常管理"的所有权限</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
