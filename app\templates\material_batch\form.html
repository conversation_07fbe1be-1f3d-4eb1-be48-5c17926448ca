{% extends 'base.html' %}

{% block title %}{% if batch %}编辑批次{% else %}创建批次{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% if batch %}编辑批次{% else %}创建批次{% endif %}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('material_batch.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" novalidate novalidate>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ingredient_id">食材 <span class="text-danger">*</span></label>
                                    <select name="ingredient_id" id="ingredient_id" class="form-control" required>
                                        <option value="">请选择食材</option>
                                        {% for ingredient in ingredients %}
                                        <option value="{{ ingredient.id }}" {% if batch and batch.ingredient_id == ingredient.id %}selected{% endif %}>
                                            {{ ingredient.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supplier_id">供应商 <span class="text-danger">*</span></label>
                                    <select name="supplier_id" id="supplier_id" class="form-control" required>
                                        <option value="">请选择供应商</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if batch and batch.supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="production_date">生产日期 <span class="text-danger">*</span></label>
                                    <input type="date" name="production_date" id="production_date" class="form-control"
                                           value="{{ batch.production_date if batch else '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expiry_date">过期日期 <span class="text-danger">*</span></label>
                                    <input type="date" name="expiry_date" id="expiry_date" class="form-control"
                                           value="{{ batch.expiry_date if batch else '' }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="production_batch_no">生产批号</label>
                                    <input type="text" name="production_batch_no" id="production_batch_no" class="form-control"
                                           value="{{ batch.production_batch_no if batch else '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="origin_place">产地</label>
                                    <input type="text" name="origin_place" id="origin_place" class="form-control"
                                           value="{{ batch.origin_place if batch else '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="area_id">所属区域 <span class="text-danger">*</span></label>
                                    <select name="area_id" id="area_id" class="form-control" required>
                                        <option value="">请选择区域</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if batch and batch.area_id == area.id %}selected{% endif %}>
                                            {{ area.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="inspection_no">检验编号</label>
                                    <input type="text" name="inspection_no" id="inspection_no" class="form-control"
                                           value="{{ batch.inspection_no if batch else '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="certificate_no">合格证编号</label>
                                    <input type="text" name="certificate_no" id="certificate_no" class="form-control"
                                           value="{{ batch.certificate_no if batch else '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="unit_price">单价</label>
                                    <input type="number" name="unit_price" id="unit_price" class="form-control" step="0.01"
                                           value="{{ batch.unit_price if batch else '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="initial_quantity">初始数量 <span class="text-danger">*</span></label>
                                    <input type="number" name="initial_quantity" id="initial_quantity" class="form-control" step="0.01"
                                           value="{{ batch.initial_quantity if batch else '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unit">单位 <span class="text-danger">*</span></label>
                                    <input type="text" name="unit" id="unit" class="form-control"
                                           value="{{ batch.unit if batch else '' }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="remark">备注</label>
                            <textarea name="remark" id="remark" class="form-control" rows="3">{{ batch.remark if batch else '' }}</textarea>
                        </div>

                        <div class="mb-3 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <a href="{{ url_for('material_batch.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(function() {
        // 自动计算过期日期
        $('#ingredient_id, #production_date').change(function() {
            var ingredientId = $('#ingredient_id').val();
            var productionDate = $('#production_date').val();

            if (ingredientId && productionDate) {
                // 获取食材的保质期
                $.getJSON("{{ url_for('api.get_ingredient', id=0) }}".replace('0', ingredientId), function(data) {
                    if (data.shelf_life) {
                        var prodDate = new Date(productionDate);
                        prodDate.setDate(prodDate.getDate() + parseInt(data.shelf_life));

                        // 格式化日期为YYYY-MM-DD
                        var year = prodDate.getFullYear();
                        var month = (prodDate.getMonth() + 1).toString().padStart(2, '0');
                        var day = prodDate.getDate().toString().padStart(2, '0');

                        $('#expiry_date').val(year + '-' + month + '-' + day);
                    }
                });
            }
        });

        // 获取食材的默认单位
        $('#ingredient_id').change(function() {
            var ingredientId = $(this).val();
            if (ingredientId) {
                $.getJSON("{{ url_for('api.get_ingredient', id=0) }}".replace('0', ingredientId), function(data) {
                    if (data.unit) {
                        $('#unit').val(data.unit);
                    }
                });
            }
        });
    });
</script>
{% endblock %}
