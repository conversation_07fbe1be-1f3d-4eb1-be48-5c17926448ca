{% extends 'base.html' %}

{% block title %}留样管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">管理食品留样记录，确保食品安全追溯</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('food_trace.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> 返回溯源首页
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 查询表单 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label>日期</label>
                                <input type="date" id="date" class="form-control" value="{{ today }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label>餐次</label>
                                <select id="meal-type" class="form-control">
                                    <option value="早餐">早餐</option>
                                    <option value="午餐" selected>午餐</option>
                                    <option value="晚餐">晚餐</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label>区域</label>
                                <select id="area" class="form-control">
                                    {% for area in areas %}
                                    <option value="{{ area.id }}">{{ area.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label>&nbsp;</label>
                                <button id="search-btn" class="btn btn-primary w-100">查询</button>
                            </div>
                        </div>
                    </div>

                    <!-- 菜品展示区 -->
                    <div id="recipe-container" class="row">
                        <div class="col-12 text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p>正在加载菜品数据...</p>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <nav aria-label="菜品分页">
                                <ul class="pagination justify-content-center" id="pagination">
                                    <!-- 分页按钮将通过JS动态加载 -->
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 留样设置 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">留样设置</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label>留样量</label>
                                                <div class="input-group">
                                                    <input type="number" id="sample-quantity" class="form-control" value="50">
                                                    <div >
                                                        <span class="input-group-text">g</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label>保存时间</label>
                                                <div class="input-group">
                                                    <input type="number" id="storage-hours" class="form-control" value="48">
                                                    <div >
                                                        <span class="input-group-text">小时</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label>存放位置</label>
                                                <input type="text" id="storage-location" class="form-control" value="留样冰箱">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label>温度</label>
                                                <div class="input-group">
                                                    <input type="number" id="storage-temp" class="form-control" value="-18">
                                                    <div >
                                                        <span class="input-group-text">℃</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <button id="select-all" class="btn btn-secondary">全选</button>
                                            <button id="deselect-all" class="btn btn-secondary">取消全选</button>
                                            <button id="generate-labels" class="btn btn-success">生成留样标签</button>
                                            <button id="print-labels" class="btn btn-primary">打印</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .recipe-card {
        height: 100%;
        transition: all 0.3s ease;
    }

    .recipe-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .recipe-card .card-header {
        padding: 0.5rem 1rem;
    }

    .recipe-card .card-header h6 {
        margin: 0;
        font-size: 0.9rem;
        color: #495057;
    }

    .recipe-card .card-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .recipe-card .card-body {
        padding: 1rem;
    }

    .recipe-card .form-check {
        margin-top: 0.5rem;
    }

    .recipe-card.has-sample {
        border-color: #28a745;
    }

    .recipe-card.has-sample .card-header {
        background-color: #28a745;
        color: white;
    }

    .sample-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #28a745;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
    }

    .recipe-card {
        height: 100%;
        transition: all 0.3s ease;
    }

    .recipe-card:hover {
        transform: translateY(-5px);
        /* box-shadow: 0 4px 8px rgba(0,0,0,0.2); */ /* 移除阴影效果 */
    }

    .recipe-card .card-header {
        padding: 0.5rem 1rem;
    }

    .recipe-card .card-header h6 {
        margin: 0;
        font-size: 0.9rem;
        color: #495057;
    }

    .recipe-card .card-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .recipe-card .card-body {
        padding: 1rem;
    }

    .recipe-card .form-check {
        margin-top: 0.5rem;
    }

    .recipe-card.has-sample {
        border-color: #28a745;
    }

    .recipe-card.has-sample .card-header {
        background-color: #28a745;
        color: white;
    }

    .sample-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #28a745;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
    }
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 设置默认日期为今天
        const today = new Date().toISOString().split('T')[0];
        $('#date').val(today);

        // 初始加载
        loadRecipes();

        // 查询按钮点击事件
        $('#search-btn').click(function() {
            loadRecipes();
        });

        // 加载菜品
        function loadRecipes(page = 1) {
            const date = $('#date').val();
            const mealType = $('#meal-type').val();
            const areaId = $('#area').val();

            $('#recipe-container').html(`
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p>正在加载菜品数据...</p>
                </div>
            `);

            $.ajax({
                url: '/food-trace-api/menu-recipes',
                type: 'GET',
                data: {
                    date: date,
                    meal_type: mealType,
                    area_id: areaId,
                    page: page
                },
                success: function(response) {
                    if (response.success) {
                        renderRecipes(response.data.recipes);
                        renderPagination(response.data.total_pages, page);
                    } else {
                        $('#recipe-container').html('<div class="col-12 text-center"><p class="alert alert-warning">' + response.message + '</p></div>');
                    }
                },
                error: function() {
                    $('#recipe-container').html('<div class="col-12 text-center"><p class="alert alert-danger">加载菜品失败</p></div>');
                }
            });
        }

        // 渲染菜品
        function renderRecipes(recipes) {
            let html = '';

            if (recipes.length === 0) {
                html = '<div class="col-12 text-center"><p class="alert alert-info">暂无菜品数据</p></div>';
            } else {
                recipes.forEach(function(recipe) {
                    const hasSampleClass = recipe.has_sample ? 'has-sample' : '';
                    const sampleBadge = recipe.has_sample ? '<span class="sample-badge">已留样</span>' : '';

                    html += `
                        <div class="col-md-4 mb-4">
                            <div class="card recipe-card ${hasSampleClass}">
                                ${sampleBadge}
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">${recipe.category || '未分类'}</h6>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">${recipe.name}</h5>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input recipe-checkbox" id="recipe-${recipe.id}" value="${recipe.id}" ${recipe.has_sample ? 'checked disabled' : ''}>
                                        <label class="form-check-label" for="recipe-${recipe.id}">${recipe.has_sample ? '已留样' : '选择留样'}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }

            $('#recipe-container').html(html);
        }

        // 渲染分页
        function renderPagination(totalPages, currentPage) {
            if (totalPages <= 1) {
                $('#pagination').html('');
                return;
            }

            let html = '';

            // 上一页
            html += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a>
                </li>
            `;

            // 页码
            for (let i = 1; i <= totalPages; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            }

            // 下一页
            html += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a>
                </li>
            `;

            $('#pagination').html(html);

            // 绑定分页点击事件
            $('.page-link').click(function(e) {
                e.preventDefault();
                const page = $(this).data('page');
                loadRecipes(page);
            });
        }

        // 全选按钮
        $('#select-all').click(function() {
            $('.recipe-checkbox:not(:disabled)').prop('checked', true);
        });

        // 取消全选按钮
        $('#deselect-all').click(function() {
            $('.recipe-checkbox:not(:disabled)').prop('checked', false);
        });

        // 生成留样标签
        $('#generate-labels').click(function() {
            const selectedRecipes = [];
            $('.recipe-checkbox:checked:not(:disabled)').each(function() {
                selectedRecipes.push($(this).val());
            });

            if (selectedRecipes.length === 0) {
                alert('请至少选择一个未留样的菜品');
                return;
            }

            const date = $('#date').val();
            const mealType = $('#meal-type').val();
            const areaId = $('#area').val();
            const sampleQuantity = $('#sample-quantity').val();
            const storageHours = $('#storage-hours').val();
            const storageLocation = $('#storage-location').val();
            const storageTemp = $('#storage-temp').val();

            // 打开留样标签页面
            const url = `/food-trace/print-samples?date=${date}&meal_type=${mealType}&area_id=${areaId}&recipes=${selectedRecipes.join(',')}&quantity=${sampleQuantity}&hours=${storageHours}&location=${encodeURIComponent(storageLocation)}&temp=${storageTemp}`;
            window.open(url, '_blank');
        });

        // 打印按钮
        $('#print-labels').click(function() {
            window.print();
        });
    });
</script>
{% endblock %}
