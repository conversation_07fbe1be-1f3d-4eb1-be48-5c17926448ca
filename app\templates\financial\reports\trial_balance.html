{% extends "financial/base.html" %}

{% block page_title %}试算平衡表{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">财务凭证管理</a></li>
<li class="breadcrumb-item active">试算平衡表</li>
{% endblock %}

{% block page_actions %}
<div class="financial-actions">
    <a href="{{ url_for('financial.trial_balance_report', start_date=start_date, end_date=end_date, format='excel') }}" 
       class="btn btn-success">
        <i class="fas fa-file-excel"></i> 导出Excel
    </a>
    <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回凭证列表
    </a>
</div>
{% endblock %}

{% block financial_content %}
<div class="row">
    <div class="col-lg-12">
        <!-- 查询条件 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-search"></i> 查询条件
            </div>
            <div class="financial-card-body">
                <form method="GET" class="financial-form">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">开始日期</label>
                                <input type="date" class="form-control" name="start_date" 
                                       value="{{ start_date }}" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">结束日期</label>
                                <input type="date" class="form-control" name="end_date" 
                                       value="{{ end_date }}" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 试算平衡表 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-table"></i> 试算平衡表
                <span class="ms-2 text-muted">{{ start_date }} 至 {{ end_date }}</span>
                <div class="float-end">
                    <span class="badge bg-info">{{ user_area.name }}</span>
                </div>
            </div>
            <div class="financial-card-body">
                {% if table_data %}
                <div class="table-responsive">
                    <table class="table financial-table">
                        <thead>
                            <tr>
                                <th width="15%">科目编码</th>
                                <th width="25%">科目名称</th>
                                <th width="15%" class="text-end">借方发生额</th>
                                <th width="15%" class="text-end">贷方发生额</th>
                                <th width="15%" class="text-end">借方余额</th>
                                <th width="15%" class="text-end">贷方余额</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set total_debit_amount = 0 %}
                            {% set total_credit_amount = 0 %}
                            {% set total_debit_balance = 0 %}
                            {% set total_credit_balance = 0 %}
                            
                            {% for row in table_data %}
                            <tr>
                                <td>{{ row['科目编码'] }}</td>
                                <td>{{ row['科目名称'] }}</td>
                                <td class="text-end">
                                    <span class="financial-amount">{{ row['借方发生额'] }}</span>
                                </td>
                                <td class="text-end">
                                    <span class="financial-amount">{{ row['贷方发生额'] }}</span>
                                </td>
                                <td class="text-end">
                                    <span class="financial-amount">{{ row['借方余额'] }}</span>
                                </td>
                                <td class="text-end">
                                    <span class="financial-amount">{{ row['贷方余额'] }}</span>
                                </td>
                            </tr>
                            
                            {% set total_debit_amount = total_debit_amount + (row['借方发生额']|float) %}
                            {% set total_credit_amount = total_credit_amount + (row['贷方发生额']|float) %}
                            {% set total_debit_balance = total_debit_balance + (row['借方余额']|float) %}
                            {% set total_credit_balance = total_credit_balance + (row['贷方余额']|float) %}
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-info">
                                <th colspan="2">合计</th>
                                <th class="text-end">{{ "%.2f"|format(total_debit_amount) }}</th>
                                <th class="text-end">{{ "%.2f"|format(total_credit_amount) }}</th>
                                <th class="text-end">{{ "%.2f"|format(total_debit_balance) }}</th>
                                <th class="text-end">{{ "%.2f"|format(total_credit_balance) }}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                <!-- 平衡验证 -->
                <div class="row mt-3">
                    <div class="col-md-12">
                        {% set debit_credit_diff = total_debit_amount - total_credit_amount %}
                        {% set balance_diff = total_debit_balance - total_credit_balance %}
                        
                        <div class="alert {{ 'alert-success' if (debit_credit_diff|abs < 0.01 and balance_diff|abs 0.01) else 'alert-warning' }}">
                            <h6><i class="fas fa-balance-scale"></i> 平衡验证</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>发生额平衡：</strong>
                                    {% if debit_credit_diff|abs < 0.01 %}
                                        <span class="text-success">✓ 借贷平衡</span>
                                    {% else %}
                                        <span class="text-warning">⚠ 差额 {{ "%.2f"|format(debit_credit_diff) }}</span>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <strong>余额平衡：</strong>
                                    {% if balance_diff|abs < 0.01 %}
                                        <span class="text-success">✓ 借贷平衡</span>
                                    {% else %}
                                        <span class="text-warning">⚠ 差额 {{ "%.2f"|format(balance_diff) }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                    <h5>暂无数据</h5>
                    <p>所选期间内没有已审核或已记账的凭证数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 自动设置默认日期范围
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');
    
    if (!startDateInput.value || !endDateInput.value) {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        
        if (!startDateInput.value) {
            startDateInput.value = firstDay.toISOString().split('T')[0];
        }
        if (!endDateInput.value) {
            endDateInput.value = today.toISOString().split('T')[0];
        }
    }
});
</script>
{% endblock %}
