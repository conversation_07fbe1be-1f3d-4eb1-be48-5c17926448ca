{% extends "financial/base.html" %}

{% block page_title %}创建付款记录{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.payments_index') }}">付款记录管理</a></span>
<span class="uf-breadcrumb-item active">创建付款记录</span>
{% endblock %}

{% block page_actions %}
<a href="{{ url_for('financial.payments_index') }}" class="uf-btn">
    <i class="fas fa-arrow-left uf-icon"></i> 返回列表
</a>
{% endblock %}

{% block financial_content %}
<div style="max-width: 800px; margin: 0 auto;">
    <div class="uf-card">
        <div class="uf-card-header">
            <i class="fas fa-plus uf-icon"></i> 创建付款记录
        </div>
        <div class="uf-card-body">
            <form method="POST">                {{ form.hidden_tag() }}

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 12px;">
                    <div class="uf-mb-3">
                        {{ form.payment_date.label(class="uf-form-label") }}
                        {{ form.payment_date(class="uf-form-control") }}
                        {% if form.payment_date.errors %}
                            <div style="color: var(--uf-danger); font-size: 10px; margin-top: 2px;">
                                {% for error in form.payment_date.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="uf-mb-3">
                        {{ form.payment_method.label(class="uf-form-label") }}
                        {{ form.payment_method(class="uf-form-control") }}
                        {% if form.payment_method.errors %}
                            <div style="color: var(--uf-danger); font-size: 10px; margin-top: 2px;">
                                {% for error in form.payment_method.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 12px;">
                    <div class="uf-mb-3">
                        {{ form.payable_id.label(class="uf-form-label") }}
                        {{ form.payable_id(class="uf-form-control", id="payable_select") }}
                        {% if form.payable_id.errors %}
                            <div style="color: var(--uf-danger); font-size: 10px; margin-top: 2px;">
                                {% for error in form.payable_id.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="uf-mb-3">
                        {{ form.amount.label(class="uf-form-label") }}
                        <div style="display: flex; align-items: center;">
                            <span class="uf-currency" style="margin-right: 4px;">¥</span>
                            {{ form.amount(class="uf-form-control", id="amount_input", style="flex: 1;") }}
                        </div>
                        {% if form.amount.errors %}
                            <div style="color: var(--uf-danger); font-size: 10px; margin-top: 2px;">
                                {% for error in form.amount.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div id="balance_info" style="font-size: 10px; margin-top: 2px; color: #666;"></div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 12px;">
                    <div class="uf-mb-3">
                        {{ form.bank_account.label(class="uf-form-label") }}
                        {{ form.bank_account(class="uf-form-control") }}
                        {% if form.bank_account.errors %}
                            <div style="color: var(--uf-danger); font-size: 10px; margin-top: 2px;">
                                {% for error in form.bank_account.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="uf-mb-3">
                        {{ form.reference_number.label(class="uf-form-label") }}
                        {{ form.reference_number(class="uf-form-control") }}
                        {% if form.reference_number.errors %}
                            <div style="color: var(--uf-danger); font-size: 10px; margin-top: 2px;">
                                {% for error in form.reference_number.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="uf-mb-3" style="margin-bottom: 12px;">
                    {{ form.summary.label(class="uf-form-label") }}
                    {{ form.summary(class="uf-form-control") }}
                    {% if form.summary.errors %}
                        <div style="color: var(--uf-danger); font-size: 10px; margin-top: 2px;">
                            {% for error in form.summary.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="uf-mb-3" style="margin-bottom: 16px;">
                    {{ form.notes.label(class="uf-form-label") }}
                    {{ form.notes(class="uf-form-control", rows="3") }}
                    {% if form.notes.errors %}
                        <div style="color: var(--uf-danger); font-size: 10px; margin-top: 2px;">
                            {% for error in form.notes.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="uf-btn uf-btn-primary uf-btn-lg">
                        <i class="fas fa-save uf-icon"></i> 创建付款记录
                    </button>
                    <a href="{{ url_for('financial.payments_index') }}" class="uf-btn uf-btn-lg" style="margin-left: 12px;">
                        <i class="fas fa-times uf-icon"></i> 取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const payableSelect = document.getElementById('payable_select');
    const amountInput = document.getElementById('amount_input');
    const balanceInfo = document.getElementById('balance_info');
    
    // 当选择应付账款时，显示余额信息
    payableSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value && selectedOption.value !== '0') {
            const text = selectedOption.text;
            const balanceMatch = text.match(/¥([\d,]+\.?\d*)/);
            if (balanceMatch) {
                const balance = balanceMatch[1].replace(/,/g, '');
                balanceInfo.textContent = `应付账款余额：¥${balance}`;
                balanceInfo.className = 'form-text text-info';
                
                // 设置最大付款金额
                amountInput.setAttribute('max', balance);
            }
        } else {
            balanceInfo.textContent = '';
            amountInput.removeAttribute('max');
        }
    });
    
    // 验证付款金额
    amountInput.addEventListener('input', function() {
        const maxAmount = parseFloat(this.getAttribute('max'));
        const currentAmount = parseFloat(this.value);
        
        if (maxAmount && currentAmount > maxAmount) {
            balanceInfo.textContent = `付款金额不能超过应付账款余额 ¥${maxAmount}`;
            balanceInfo.className = 'form-text text-danger';
        } else if (maxAmount) {
            balanceInfo.textContent = `应付账款余额：¥${maxAmount}`;
            balanceInfo.className = 'form-text text-info';
        }
    });
    
    // 如果URL中有payable_id参数，自动选择对应的应付账款
    const urlParams = new URLSearchParams(window.location.search);
    const payableId = urlParams.get('payable_id');
    if (payableId) {
        payableSelect.value = payableId;
        payableSelect.dispatchEvent(new Event('change'));
    }
});
</script>
{% endblock %}
