// 菜单数据管理模块 - 使用Map结构优化
const MenuDataManager = {
    // 菜单数据 - 使用Map结构提高查找效率
    menuData: new Map(),

    // 副表数据 - 用于存储临时菜品数据
    tempData: new Map(),

    // 缓存和状态
    _cache: {
        lastModified: null,
        hasChanges: false,
        weeklyMenuId: null,
        areaId: null
    },

    // 初始化
    init(initialData) {
        this.menuData.clear();
        this.tempData.clear();
        this._cache.lastModified = new Date();
        this._cache.hasChanges = false;
        this._cache.weeklyMenuId = $('#menu-id').val();
        this._cache.areaId = $('#area-id').val();

        if (initialData && typeof initialData === 'object') {
            try {
                // 将对象结构转换为嵌套Map结构
                Object.entries(initialData).forEach(([date, meals]) => {
                    const dateMap = new Map();
                    if (meals && typeof meals === 'object') {
                        Object.entries(meals).forEach(([meal, recipes]) => {
                            if (Array.isArray(recipes)) {
                                dateMap.set(meal, [...recipes]);
                            } else {
                                console.warn(`初始化菜单数据: ${date} ${meal} 的菜品不是数组`);
                                dateMap.set(meal, []);
                            }
                        });
                    }
                    this.menuData.set(date, dateMap);
                });
                console.log('菜单数据初始化成功');

                // 初始化完成后，加载副表数据
                this.loadTempData();
            } catch (error) {
                console.error('菜单数据初始化失败:', error);
                // 初始化失败时使用空Map
                this.menuData.clear();
            }
        } else {
            // 如果没有初始数据，也尝试加载副表数据
            this.loadTempData();
        }

        console.log('菜单数据初始化完成');
        return this;
    },

    // 加载副表数据
    loadTempData() {
        const weeklyMenuId = this._cache.weeklyMenuId;
        if (!weeklyMenuId) {
            console.warn('加载副表数据: 缺少菜单ID');
            return false;
        }

        console.log(`开始加载副表数据: 菜单ID=${weeklyMenuId}`);

        // 发送AJAX请求获取副表数据
        $.ajax({
            url: `/api/weekly-menu/${weeklyMenuId}/temp-recipes`,
            method: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success && response.data) {
                    this.processTempData(response.data);
                } else {
                    console.warn('加载副表数据: 服务器返回空数据');
                }
            },
            error: (xhr) => {
                console.error('加载副表数据失败:', xhr.responseText);
            }
        });
    },

    // 处理副表数据
    processTempData(data) {
        try {
            if (!Array.isArray(data) || data.length === 0) {
                console.warn('处理副表数据: 数据为空');
                return;
            }

            console.log(`处理副表数据: ${data.length}条记录`);

            // 清空临时数据
            this.tempData.clear();

            // 处理每条记录
            data.forEach(item => {
                const date = item.day_of_week;
                const meal = item.meal_type;

                if (!date || !meal) {
                    console.warn('处理副表数据: 记录缺少日期或餐次信息', item);
                    return;
                }

                // 创建菜品对象
                const recipe = {
                    id: item.recipe_id || `temp_${Date.now()}`,
                    name: item.recipe_name || '未命名菜品',
                    recipe_id: item.recipe_id,
                    recipe_name: item.recipe_name,
                    is_custom: item.is_custom || false,
                    temp_id: item.id // 保存副表记录ID
                };

                // 添加到临时数据
                if (!this.tempData.has(date)) {
                    this.tempData.set(date, new Map());
                }

                if (!this.tempData.get(date).has(meal)) {
                    this.tempData.get(date).set(meal, []);
                }

                this.tempData.get(date).get(meal).push(recipe);
            });

            console.log('副表数据处理完成');

            // 更新主数据
            this.syncTempDataToMain();
        } catch (error) {
            console.error('处理副表数据失败:', error);
        }
    },

    // 将副表数据同步到主数据
    syncTempDataToMain() {
        try {
            console.log('开始同步副表数据到主数据');

            this.tempData.forEach((mealMap, date) => {
                mealMap.forEach((recipes, meal) => {
                    if (recipes.length > 0) {
                        console.log(`同步数据: ${date} ${meal}, ${recipes.length}个菜品`);
                        this.setRecipes(date, meal, recipes);
                    }
                });
            });

            console.log('副表数据同步完成');

            // 更新UI
            this.updateAllInputs();
        } catch (error) {
            console.error('同步副表数据失败:', error);
        }
    },

    // 更新所有输入框
    updateAllInputs() {
        this.menuData.forEach((mealMap, date) => {
            mealMap.forEach((recipes, meal) => {
                const input = $(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);
                if (input.length) {
                    UIManager.updateInputDisplay(input, recipes);

                    // 将菜品ID存储到输入框的data属性中
                    const recipeIds = recipes.map(r => r.recipe_id || r.id || null);
                    input.data('recipe-ids', recipeIds);
                }
            });
        });
    },

    // 获取菜品列表
    getRecipes(date, meal) {
        const dateMap = this.menuData.get(date);
        if (!dateMap) return [];

        const recipes = dateMap.get(meal);
        return recipes ? [...recipes] : [];
    },

    // 设置菜品列表
    setRecipes(date, meal, recipes) {
        if (!Array.isArray(recipes)) {
            console.error('设置菜品列表失败: recipes不是数组');
            return false;
        }

        try {
            // 确保日期Map存在
            if (!this.menuData.has(date)) {
                this.menuData.set(date, new Map());
            }

            // 确保每个菜品都有recipe_id
            const processedRecipes = recipes.map(recipe => {
                // 创建新对象，避免修改原始对象
                const newRecipe = {...recipe};

                // 如果recipe_id为空但id存在，则使用id作为recipe_id
                if (!newRecipe.recipe_id && newRecipe.id) {
                    newRecipe.recipe_id = newRecipe.id;
                    console.log(`修正菜品数据: 使用id(${newRecipe.id})作为recipe_id`);
                }

                // 如果是自定义菜品且没有recipe_id，设置为null
                if (newRecipe.is_custom && !newRecipe.recipe_id) {
                    newRecipe.recipe_id = null;
                    console.log(`自定义菜品: ${newRecipe.name}, recipe_id设置为null`);
                }

                // 确保recipe_name存在
                if (!newRecipe.recipe_name && newRecipe.name) {
                    newRecipe.recipe_name = newRecipe.name;
                }

                return newRecipe;
            });

            // 设置菜品列表
            this.menuData.get(date).set(meal, processedRecipes);

            // 标记数据已更改
            this._cache.hasChanges = true;
            this._cache.lastModified = new Date();

            console.log(`已设置菜品列表: ${date} ${meal}, ${processedRecipes.length}个菜品`);

            return true;
        } catch (error) {
            console.error('设置菜品列表失败:', error);
            return false;
        }
    },

    // 更新副表数据
    updateTempData(date, meal, recipes) {
        try {
            const weeklyMenuId = this._cache.weeklyMenuId;
            if (!weeklyMenuId) {
                console.warn('更新副表数据: 缺少菜单ID');
                return false;
            }

            console.log(`更新副表数据: ${date} ${meal}, ${recipes.length}个菜品`);

            // 准备请求数据
            const requestData = {
                weekly_menu_id: weeklyMenuId,
                day_of_week: date,
                meal_type: meal,
                recipes: recipes.map(recipe => ({
                    recipe_id: recipe.recipe_id || recipe.id || null,
                    recipe_name: recipe.recipe_name || recipe.name || '未命名菜品',
                    is_custom: recipe.is_custom || false
                }))
            };

            // 发送AJAX请求更新副表数据
            $.ajax({
                url: `/api/weekly-menu/${weeklyMenuId}/temp-recipes`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: (response) => {
                    if (response.success) {
                        console.log('副表数据更新成功');
                    } else {
                        console.warn('副表数据更新失败:', response.message);
                    }
                },
                error: (xhr) => {
                    console.error('副表数据更新请求失败:', xhr.responseText);
                }
            });

            return true;
        } catch (error) {
            console.error('更新副表数据失败:', error);
            return false;
        }
    },

    // 检查是否有未保存的更改
    hasUnsavedChanges() {
        return this._cache.hasChanges;
    },

    // 重置更改状态
    resetChangeStatus() {
        this._cache.hasChanges = false;
    },

    // 获取完整数据（用于保存）
    getFullData() {
        const result = {};

        this.menuData.forEach((mealMap, date) => {
            result[date] = {};

            mealMap.forEach((recipes, meal) => {
                result[date][meal] = [...recipes];
            });
        });

        return result;
    }
};

// UI交互管理模块
const UIManager = {
    // 配置选项
    config: {
        messageTimeout: 3000,
        errorTimeout: 10000,
        ajaxStatusTimeout: 5000,
        animationDuration: 300,
        toastPosition: 'top-right'
    },

    // 状态管理
    state: {
        isLoading: false,
        activeModals: new Set(),
        notifications: []
    },

    // 初始化
    init() {
        // 创建Toast容器
        this._createToastContainer();

        // 使用事件委托绑定全局事件
        this._bindGlobalEvents();

        // 初始化加载指示器
        this._initLoadingIndicator();

        console.log('UI管理器初始化完成');
        return this;
    },

    // 创建Toast容器
    _createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed ' + this.config.toastPosition;
        container.style.zIndex = '1090';
        document.body.appendChild(container);
        this._toastContainer = container;
    },

    // 绑定全局事件
    _bindGlobalEvents() {
        // 使用事件委托处理模态框事件
        $(document).on('show.bs.modal', '.modal', (e) => {
            const modalId = e.target.id;
            this.state.activeModals.add(modalId);
        });

        $(document).on('hidden.bs.modal', '.modal', (e) => {
            const modalId = e.target.id;
            this.state.activeModals.delete(modalId);
        });

        // 监听菜单数据变更事件
        document.addEventListener('menudata:changed', (e) => {
            if (e.detail.hasChanges) {
                this._updateSaveIndicator(true);
            } else {
                this._updateSaveIndicator(false);
            }
        });

        // 监听网络状态
        window.addEventListener('online', () => {
            this.showMessage('网络连接已恢复', 'success');
        });

        window.addEventListener('offline', () => {
            this.showMessage('网络连接已断开', 'warning');
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，检查是否需要刷新数据
                this._checkDataFreshness();
            }
        });
    },

    // 初始化加载指示器
    _initLoadingIndicator() {
        // 检查是否已存在加载指示器
        if (!document.querySelector('.loading-overlay')) {
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            `;
            document.body.appendChild(overlay);
        }
    },

    // 更新保存指示器
    _updateSaveIndicator(hasChanges) {
        const saveBtn = document.getElementById('saveMenuBtn');
        if (saveBtn) {
            if (hasChanges) {
                saveBtn.classList.add('btn-warning');
                saveBtn.classList.remove('btn-primary');
                saveBtn.innerHTML = '<i class="fas fa-save"></i> 保存菜单 <span class="badge bg-light">*</span>';
            } else {
                saveBtn.classList.remove('btn-warning');
                saveBtn.classList.add('btn-primary');
                saveBtn.innerHTML = '<i class="fas fa-save"></i> 保存菜单';
            }
        }
    },

    // 检查数据新鲜度
    _checkDataFreshness() {
        // 如果页面隐藏超过一定时间，可能需要刷新数据
        const lastActive = this._lastActiveTime || 0;
        const now = Date.now();
        const threshold = 5 * 60 * 1000; // 5分钟

        if (now - lastActive > threshold) {
            // 提示用户刷新
            this.showConfirm('页面数据可能已过期，是否刷新？', () => {
                location.reload();
            });
        }

        this._lastActiveTime = now;
    },

    // 显示加载状态
    showLoading() {
        this.state.isLoading = true;
        $('.loading-overlay').css('display', 'flex').hide().fadeIn(this.config.animationDuration);
        return this;
    },

    // 隐藏加载状态
    hideLoading() {
        this.state.isLoading = false;
        $('.loading-overlay').fadeOut(this.config.animationDuration);
        return this;
    },

    // 显示消息提示
    showMessage(message, type = 'info', timeout = null) {
        if (!message) return this;

        // 确定超时时间
        const duration = timeout || (type === 'error' ? this.config.errorTimeout : this.config.messageTimeout);

        // 创建Toast元素
        const toastId = 'toast-' + Date.now();
        const toast = document.createElement('div');
        toast.className = `toast bg-${this._getBootstrapColor(type)}`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        toast.setAttribute('data-delay', duration);
        toast.setAttribute('id', toastId);

        toast.innerHTML = `
            <div class="toast-header">
                <strong class="mr-auto">${this._getMessageTitle(type)}</strong>
                <small>${this._formatTime(new Date())}</small>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="toast-body text-white">
                ${message}
            </div>
        `;

        // 添加到容器
        this._toastContainer.appendChild(toast);

        // 显示Toast
        const bsToast = new bootstrap.Toast(toast, {
            delay: duration
        });
        bsToast.show();

        // 添加到通知列表
        this.state.notifications.push({
            id: toastId,
            message,
            type,
            time: new Date()
        });

        // 限制通知数量
        if (this.state.notifications.length > 10) {
            this.state.notifications.shift();
        }

        return this;
    },

    // 获取Bootstrap颜色类
    _getBootstrapColor(type) {
        const colorMap = {
            success: 'success',
            error: 'danger',
            warning: 'warning',
            info: 'info'
        };
        return colorMap[type] || 'info';
    },

    // 获取消息标题
    _getMessageTitle(type) {
        const titleMap = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '提示'
        };
        return titleMap[type] || '提示';
    },

    // 格式化时间
    _formatTime(date) {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    },

    // 显示确认对话框
    showConfirm(message, onConfirm, onCancel) {
        // 检查是否已存在确认对话框
        let confirmModal = document.getElementById('confirmModal');

        if (!confirmModal) {
            // 创建确认对话框
            confirmModal = document.createElement('div');
            confirmModal.className = 'modal fade';
            confirmModal.id = 'confirmModal';
            confirmModal.setAttribute('tabindex', '-1');
            confirmModal.setAttribute('role', 'dialog');
            confirmModal.setAttribute('aria-labelledby', 'confirmModalLabel');
            confirmModal.setAttribute('aria-hidden', 'true');

            confirmModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="confirmModalLabel">确认操作</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" id="confirmModalBody">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="confirmCancelBtn">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmOkBtn">确定</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(confirmModal);
        }

        // 设置消息内容
        document.getElementById('confirmModalBody').textContent = message;

        // 绑定事件
        const modal = new bootstrap.Modal(confirmModal);

        // 移除旧的事件监听器
        const okBtn = document.getElementById('confirmOkBtn');
        const cancelBtn = document.getElementById('confirmCancelBtn');

        const newOkBtn = okBtn.cloneNode(true);
        const newCancelBtn = cancelBtn.cloneNode(true);

        okBtn.parentNode.replaceChild(newOkBtn, okBtn);
        cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);

        // 添加新的事件监听器
        newOkBtn.addEventListener('click', () => {
            modal.hide();
            if (typeof onConfirm === 'function') {
                onConfirm();
            }
        });

        newCancelBtn.addEventListener('click', () => {
            if (typeof onCancel === 'function') {
                onCancel();
            }
        });

        // 显示对话框
        modal.show();

        return this;
    },

    // 显示错误消息
    showError(message) {
        return this.showMessage(message, 'error');
    },

    // 显示成功消息
    showSuccess(message) {
        return this.showMessage(message, 'success');
    },

    // 更新输入框显示
    updateInputDisplay(input, recipes) {
        if (!input || !Array.isArray(recipes)) return false;

        try {
            // 获取输入框元素
            const $input = $(input);
            if (!$input.length) return false;

            // 更新输入框内容
            if (recipes.length === 0) {
                $input.html('<span class="text-muted">未选择菜品</span>');
                $input.removeClass('has-recipes');
            } else {
                const recipeNames = recipes.map(r => r.name || r.recipe_name).join('、');
                $input.html(recipeNames);
                $input.addClass('has-recipes');
            }

            // 添加动画效果
            $input.addClass('updated');
            setTimeout(() => {
                $input.removeClass('updated');
            }, 1000);

            return true;
        } catch (error) {
            console.error('更新输入框显示失败:', error);
            return false;
        }
    },

    // 虚拟滚动实现
    virtualScroll: {
        config: {
            itemHeight: 80,
            containerHeight: 400,
            bufferSize: 5
        },

        init(containerId, items, itemRenderer) {
            const container = document.getElementById(containerId);
            if (!container) return false;

            this.container = container;
            this.items = items || [];
            this.itemRenderer = itemRenderer || this._defaultItemRenderer;

            // 设置容器样式
            container.style.height = `${this.config.containerHeight}px`;
            container.style.overflowY = 'auto';
            container.style.position = 'relative';

            // 创建内容容器
            const content = document.createElement('div');
            content.className = 'virtual-scroll-content';
            content.style.position = 'relative';
            container.appendChild(content);
            this.content = content;

            // 绑定滚动事件
            container.addEventListener('scroll', this._handleScroll.bind(this));

            // 初始渲染
            this.render();

            return true;
        },

        // 默认项目渲染器
        _defaultItemRenderer(item) {
            return `
                <div class="card recipe-card mb-2">
                    <div class="card-body">
                        <h5 class="card-title">${item.name || ''}</h5>
                    </div>
                </div>
            `;
        },

        // 处理滚动事件
        _handleScroll() {
            requestAnimationFrame(() => {
                this.render();
            });
        },

        // 渲染可见项目
        render() {
            if (!this.container || !this.content) return;

            const scrollTop = this.container.scrollTop;
            const containerHeight = this.container.clientHeight;

            // 计算可见范围
            const startIndex = Math.max(0, Math.floor(scrollTop / this.config.itemHeight) - this.config.bufferSize);
            const endIndex = Math.min(
                this.items.length - 1,
                Math.ceil((scrollTop + containerHeight) / this.config.itemHeight) + this.config.bufferSize
            );

            // 设置内容高度
            this.content.style.height = `${this.items.length * this.config.itemHeight}px`;

            // 生成HTML
            let html = '';

            // 渲染可见项目
            for (let i = startIndex; i <= endIndex; i++) {
                const item = this.items[i];
                const top = i * this.config.itemHeight;
                html += `<div style="position:absolute;top:${top}px;width:100%;">${this.itemRenderer(item)}</div>`;
            }

            this.content.innerHTML = html;
        }
    }
};

// 菜品选择模块
const RecipeSelector = {
    // 状态管理
    state: {
        currentDate: null,
        currentMeal: null,
        isSearching: false,
        lastSearchKeyword: '',
        lastCategory: 'all'
    },

    // 当前选择的菜品
    selectedRecipes: new Map(),

    // 初始化
    init() {
        console.log('初始化菜品选择器');
        this.bindEvents();
        return this;
    },

    // 使用事件委托绑定事件
    bindEvents() {
        try {
            // 使用事件委托处理菜品分类切换
            $(document).on('click', '#recipeCategories .nav-link', (e) => {
                e.preventDefault();
                const category = $(e.currentTarget).data('category') || 'all';
                this.state.lastCategory = category;
                this.filterByCategory(category);
            });

            // 使用防抖处理菜品搜索
            let searchTimer;
            $(document).on('input', '#recipeSearch', (e) => {
                const keyword = $(e.target).val().trim();

                // 清除之前的定时器
                if (searchTimer) {
                    clearTimeout(searchTimer);
                }

                // 设置搜索状态
                this.state.isSearching = true;

                // 300ms防抖
                searchTimer = setTimeout(() => {
                    this.state.lastSearchKeyword = keyword;
                    this.searchRecipes(keyword);
                    this.state.isSearching = false;
                }, 300);
            });

            // 添加自定义菜品
            $(document).on('click', '#addCustomDishBtn', () => {
                this.addCustomRecipe();
            });

            // 回车添加自定义菜品
            $(document).on('keypress', '#customDishInput', (e) => {
                if (e.which === 13) {
                    e.preventDefault();
                    this.addCustomRecipe();
                }
            });

            // 使用事件委托处理菜品卡片点击
            $(document).on('click', '.recipe-card .card', (e) => {
                console.log('菜品卡片被点击', e.currentTarget);

                const card = $(e.currentTarget);
                const recipeId = card.data('id');
                const recipeNameWithSchool = card.data('name'); // 获取包含学校名称的完整菜品名

                // 去除学校名称 (括号及其内容)
                const recipeName = recipeNameWithSchool ? recipeNameWithSchool.replace(/（.*?）/, '').trim() : '';

                console.log('菜品数据:', { id: recipeId, name: recipeName });

                if (!recipeId || !recipeNameWithSchool) {
                    console.warn('菜品卡片缺少必要属性:', card);
                    return;
                }

                // 创建菜品对象
                const recipe = {
                    id: recipeId,
                    name: recipeName, // 使用处理后的纯菜品名
                    recipe_id: recipeId,
                    recipe_name: recipeName // 使用处理后的纯菜品名
                };

                // 添加到已选菜品
                this.addToSelection(recipe);
            });

            // 保存选择
            $(document).on('click', '#saveSelectionBtn', () => {
                this.saveSelection();
            });

            console.log('菜品选择器事件绑定完成');
        } catch (error) {
            console.error('绑定菜品选择器事件失败:', error);
        }
    },

    // 显示菜品选择模态框
    showModal(date, meal) {
        try {
            if (!date || !meal) {
                console.error('显示模态框: 缺少日期或餐次信息');
                return false;
            }

            // 更新状态
            this.state.currentDate = date;
            this.state.currentMeal = meal;

            console.log(`显示菜品选择模态框: ${date} ${meal}`);

            // 清空已选菜品
            this.selectedRecipes.clear();
            $('#selectedDishes').empty();

            // 清空搜索框
            $('#recipeSearch').val('');
            this.state.lastSearchKeyword = '';

            // 清空自定义菜品输入框
            $('#customDishInput').val('');

            // 重置分类筛选
            $('#recipeCategories .nav-link').removeClass('active');
            $('#recipeCategories .nav-link[data-category="all"]').addClass('active');
            this.state.lastCategory = 'all';
            $('.recipe-card').show();

            // 获取当前输入框
            const $input = $(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);

            // 获取当前输入框中的菜品
            let currentRecipes = MenuDataManager.getRecipes(date, meal);
            console.log(`从MenuDataManager获取菜品: ${currentRecipes.length}个`);

            // 如果MenuDataManager中没有数据，但输入框显示有菜品
            if (currentRecipes.length === 0 && $input.length && $input.hasClass('has-recipes')) {
                console.log('MenuDataManager中无数据，尝试从输入框恢复');

                // 尝试从输入框的data属性获取菜品ID
                const recipeIds = $input.data('recipe-ids');

                if (recipeIds && recipeIds.length > 0) {
                    console.log(`从输入框恢复菜品数据: IDs=${recipeIds}`);

                    // 创建菜品对象
                    currentRecipes = recipeIds.map(id => {
                        // 如果是自定义菜品(以custom_开头)
                        if (id && typeof id === 'string' && id.startsWith('custom_')) {
                            return {
                                id: id,
                                name: $input.text().trim(),
                                recipe_id: null,
                                recipe_name: $input.text().trim(),
                                is_custom: true
                            };
                        }

                        // 普通菜品
                        return {
                            id: id,
                            name: $input.text().trim(),
                            recipe_id: id,
                            recipe_name: $input.text().trim()
                        };
                    });

                    // 更新MenuDataManager
                    MenuDataManager.setRecipes(date, meal, currentRecipes);
                    console.log(`已恢复菜品数据: ${currentRecipes.length}个菜品`);
                } else {
                    // 如果没有保存菜品ID，尝试从文本内容创建
                    const text = $input.text().trim();
                    if (text && !text.includes('未选择菜品')) {
                        console.log(`从文本创建菜品: 文本="${text}"`);

                        // 创建自定义菜品
                        const customId = 'custom_' + Date.now();
                        const recipe = {
                            id: customId,
                            name: text,
                            recipe_id: null,
                            recipe_name: text,
                            is_custom: true
                        };

                        currentRecipes = [recipe];

                        // 更新MenuDataManager
                        MenuDataManager.setRecipes(date, meal, currentRecipes);
                        console.log(`已创建自定义菜品: ID=${customId}`);
                    }
                }
            }

            // 批量添加到已选菜品
            if (currentRecipes.length > 0) {
                const addedCount = this.addRecipes(currentRecipes);
                console.log(`已添加${addedCount}个菜品到选择器`);
            }

            // 更新模态框标题
            $('#modalTitle').text(`${date} ${meal} 菜品选择`);

            // 显示模态框 - Bootstrap 5兼容语法
            const menuModal = new bootstrap.Modal(document.getElementById('menuModal'));
            menuModal.show();

            // 默认选中肉类标签
            setTimeout(() => {
                $('.nav-link[data-category="肉类"]').click();
            }, 100); // 短暂延时确保DOM已完全加载

            return true;
        } catch (error) {
            console.error('显示菜品选择模态框失败:', error);
            return false;
        }
    },

    // 按分类筛选菜品
    filterByCategory(category) {
        try {
            if (category === 'all') {
                $('.recipe-card-item').show();
            } else {
                $('.recipe-card-item').each(function() {
                    const $card = $(this).find('.recipe-card');
                    if ($card.data('category') === category) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            // 如果同时有搜索关键词，需要结合搜索结果
            if (this.state.lastSearchKeyword) {
                this.searchRecipes(this.state.lastSearchKeyword);
            }

            return true;
        } catch (error) {
            console.error('筛选菜品失败:', error);
            return false;
        }
    },

    // 搜索菜品
    searchRecipes(keyword) {
        try {
            if (!keyword) {
                // 如果没有关键词，恢复到分类筛选状态
                this.filterByCategory(this.state.lastCategory);
                return true;
            }

            // 先按分类筛选
            if (this.state.lastCategory !== 'all') {
                $('.recipe-card').hide();
                $(`.recipe-card[data-category="${this.state.lastCategory}"]`).show();
            } else {
                $('.recipe-card').show();
            }

            // 再按关键词筛选
            $('.recipe-card:visible').each(function() {
                const name = $(this).find('.card-title').text().toLowerCase();
                $(this).toggle(name.includes(keyword.toLowerCase()));
            });

            return true;
        } catch (error) {
            console.error('搜索菜品失败:', error);
            return false;
        }
    },

    // 添加自定义菜品
    addCustomRecipe() {
        try {
            const input = $('#customDishInput');
            const name = input.val().trim();

            if (!name) {
                console.warn('添加自定义菜品: 名称为空');
                return false;
            }

            // 创建自定义菜品ID
            const customId = 'custom_' + Date.now();

            // 创建菜品对象
            const recipe = {
                id: customId,
                name: name,
                recipe_id: null,
                recipe_name: name,
                is_custom: true
            };

            // 添加到已选菜品
            if (this.addToSelection(recipe)) {
                // 清空输入框
                input.val('');
            }

            return true;
        } catch (error) {
            console.error('添加自定义菜品失败:', error);
            return false;
        }
    },

    // 批量添加菜品
    addRecipes(recipes) {
        if (!Array.isArray(recipes)) {
            console.error('批量添加菜品: recipes不是数组');
            return 0;
        }

        let addedCount = 0;
        recipes.forEach(recipe => {
            if (this.addToSelection(recipe, false)) {
                addedCount++;
            }
        });

        return addedCount;
    },

    // 添加到已选菜品
    addToSelection(recipe, animate = true) {
        try {
            console.log('尝试添加菜品:', recipe);

            // 验证菜品数据
            if (!recipe || !recipe.id) {
                console.warn('添加菜品: 无效的菜品数据');
                return false;
            }

            // 检查是否已经选择了该菜品
            if (this.selectedRecipes.has(recipe.id)) {
                console.log('菜品已经被选择:', recipe.name);
                this.highlightExistingRecipe(recipe.id);
                return false;
            }

            // 添加到选择集合
            this.selectedRecipes.set(recipe.id, recipe);
            console.log('菜品已添加到选择集合，当前已选菜品数量:', this.selectedRecipes.size);

            // 检查已选菜品容器是否存在
            const $selectedDishes = $('#selectedDishes');
            if (!$selectedDishes.length) {
                console.error('未找到已选菜品容器 #selectedDishes');
                return false;
            }

            // 更新UI
            const dishTag = $(`<div class="selected-recipe-tag" data-id="${recipe.id}">
                ${recipe.name}
                <span class="remove-btn">&times;</span>
            </div>`);

            // 绑定移除事件
            dishTag.find('.remove-btn').on('click', () => {
                this.removeFromSelection(recipe.id);
            });

            // 添加到已选区域
            $selectedDishes.append(dishTag);

            // 添加动画效果
            if (animate) {
                dishTag.hide().fadeIn(300);
            }

            return true;
        } catch (error) {
            console.error('添加菜品失败:', error);
            return false;
        }
    },

    // 从选择中移除
    removeFromSelection(recipeId) {
        try {
            // 从选择集合中移除
            this.selectedRecipes.delete(recipeId);

            // 从DOM中移除
            $(`.selected-recipe-tag[data-id="${recipeId}"]`).fadeOut(200, function() {
                $(this).remove();
            });

            // 更新卡片选中状态
            $(`.recipe-card .card[data-id="${recipeId}"]`).removeClass('selected');

            return true;
        } catch (error) {
            console.error('移除菜品失败:', error);
            return false;
        }
    },

    // 高亮已存在的菜品
    highlightExistingRecipe(recipeId) {
        const tag = $(`.selected-recipe-tag[data-id="${recipeId}"]`);
        tag.addClass('highlight');
        setTimeout(() => {
            tag.removeClass('highlight');
        }, 1000);
    },

    // 保存选择
    saveSelection() {
        try {
            const date = this.state.currentDate;
            const meal = this.state.currentMeal;

            if (!date || !meal) {
                console.error('保存选择: 缺少日期或餐次信息');
                UIManager.showError('保存失败: 缺少日期或餐次信息');
                return false;
            }

            // 获取所有已选菜品
            const recipes = Array.from(this.selectedRecipes.values());
            console.log(`保存选择: ${date} ${meal}, ${recipes.length}个菜品`);

            // 确保每个菜品都有recipe_id
            recipes.forEach(recipe => {
                // 如果recipe_id为空但id存在，则使用id作为recipe_id
                if (!recipe.recipe_id && recipe.id) {
                    recipe.recipe_id = recipe.id;
                    console.log(`修正菜品数据: 使用id(${recipe.id})作为recipe_id`);
                }

                // 如果是自定义菜品且没有recipe_id，设置为null
                if (recipe.is_custom && !recipe.recipe_id) {
                    recipe.recipe_id = null;
                    console.log(`自定义菜品: ${recipe.name}, recipe_id设置为null`);
                }

                // 确保recipe_name存在
                if (!recipe.recipe_name && recipe.name) {
                    recipe.recipe_name = recipe.name;
                }
            });

            // 更新菜单数据
            const updated = MenuDataManager.setRecipes(date, meal, recipes);

            if (!updated) {
                console.error('保存选择: 更新菜单数据失败');
                UIManager.showError('保存失败: 更新菜单数据失败');
                return false;
            }

            // 更新输入框显示
            const input = $(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);
            if (input.length) {
                UIManager.updateInputDisplay(input, recipes);

                // 将菜品ID存储到输入框的data属性中
                const recipeIds = recipes.map(r => r.recipe_id || r.id || null);
                input.data('recipe-ids', recipeIds);
                console.log(`已将菜品ID保存到输入框data属性: ${recipeIds}`);
            } else {
                console.warn(`保存选择: 未找到输入框 ${date} ${meal}`);
            }

            // 关闭模态框
            $('#menuModal').modal.hide();

            // 显示保存提示
            UIManager.showMessage('菜品已选择，请点击"保存菜单"按钮保存到数据库', 'info');

            return true;
        } catch (error) {
            console.error('保存菜品选择失败:', error);
            UIManager.showError('保存失败: ' + error.message);
            return false;
        }
    },

    // 清空选择
    clearSelection() {
        try {
            this.selectedRecipes.clear();
            $('#selectedDishes').empty();
            return true;
        } catch (error) {
            console.error('清空选择失败:', error);
            return false;
        }
    }
};

// 周菜单核心模块
const WeeklyMenuCore = {
    // 状态
    state: {
        isSaving: false,
        hasUnsavedChanges: false,
        currentWeek: null
    },

    // 初始化
    init(initialData) {
        // 初始化菜单数据
        MenuDataManager.init(initialData);

        // 初始化UI管理器
        UIManager.init();

        // 初始化菜品选择器
        RecipeSelector.init();

        // 绑定事件
        this.bindEvents();

        console.log('周菜单核心模块初始化完成');
    },

    // 绑定事件
    bindEvents() {
        // 菜单输入框点击事件
        $('.menu-input').on('click', (e) => {
            if ($(e.target).hasClass('readonly')) return;

            const date = $(e.target).data('date');
            const meal = $(e.target).data('meal');
            RecipeSelector.showModal(date, meal);
        });

        // 保存菜单按钮点击事件
        $('#saveMenuBtn').on('click', () => {
            this.saveMenu();
        });

        // 发布菜单按钮点击事件
        $('#publishMenuBtn').on('click', () => {
            this.publishMenu();
        });

        // 解除发布按钮点击事件
        $('#unpublishMenuBtn').on('click', () => {
            this.unpublishMenu();
        });

        // 创建菜单按钮点击事件
        $('#createMenuBtn').on('click', () => {
            this.createMenu();
        });

        // 周次选择器点击事件
        $('.week-item').on('click', (e) => {
            const weekStart = $(e.target).data('week');
            // 检查是否是活动项
            if ($(e.target).hasClass('active')) return;

            // 如果有未保存的更改，提示用户
            if (this.hasUnsavedChanges()) {
                UIManager.showConfirm('您有未保存的更改，确定要离开吗？', () => {
                    this.loadWeek(weekStart);
                });
            } else {
                this.loadWeek(weekStart);
            }
        });
    },

    // 检查是否有未保存的更改
    hasUnsavedChanges() {
        return MenuDataManager.hasUnsavedChanges();
    },

    // 加载指定周次的菜单
    loadWeek(weekStart) {
        const areaId = $('#area-id').val();
        window.location.href = `/weekly-menu-v2/plan?area_id=${areaId}&week_start=${weekStart}`;
    },

    // 创建菜单
    createMenu() {
        UIManager.showLoading();

        const areaId = $('#area-id').val();
        const weekStart = $('#week-start').val();
        const csrfToken = $('meta[name="csrf-token"]').attr('content');

        // 发送创建请求
        $.ajax({
            url: '/api/weekly-menu/create',
            method: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': csrfToken
            },
            data: JSON.stringify({
                area_id: areaId,
                week_start: weekStart
            }),
            success: (response) => {
                console.log('创建周菜单响应:', response);

                if (response.success) {
                    // 隐藏创建提示，显示菜单表单
                    $('#createMenuPrompt').hide();
                    $('#menuForm').show();

                    // 设置菜单ID
                    $('#menu-id').val(response.weekly_menu_id);

                    // 更新缓存中的菜单ID
                    MenuDataManager._cache.weeklyMenuId = response.weekly_menu_id;

                    // 显示成功消息
                    UIManager.showMessage('周菜单创建成功，正在刷新页面...', 'success');

                    // 延迟刷新页面以确保状态完全更新
                    setTimeout(() => {
                        const areaId = $('#area-id').val();
                        const weekStart = $('#week-start').val();
                        window.location.href = `/weekly-menu-v2/plan?area_id=${areaId}&week_start=${weekStart}`;
                    }, 1500);
                } else {
                    console.error('创建周菜单失败:', response.message);
                    UIManager.showMessage('创建失败: ' + response.message, 'error');
                    UIManager.showError('创建周菜单失败: ' + response.message);
                }
            },
            error: (xhr) => {
                console.error('创建周菜单请求失败:', xhr.responseText);
                UIManager.showError('创建周菜单请求失败: ' + xhr.responseText);
            },
            complete: () => {
                UIManager.hideLoading();
            }
        });
    },

    // 保存菜单
    saveMenu() {
        try {
            UIManager.showLoading();

            // 获取菜单ID和区域ID
            const menuId = $('#menu-id').val();
            const areaId = $('#area-id').val();

            if (!menuId || !areaId) {
                UIManager.showError('保存失败: 缺少菜单ID或区域ID');
                UIManager.hideLoading();
                return;
            }

            console.log(`准备保存菜单: ID=${menuId}, 区域ID=${areaId}`);

            // 在提交前，确保所有格子的数据都已更新到MenuDataManager
            this._ensureAllCellsDataSaved();

            // 获取完整的菜单数据
            const menuData = MenuDataManager.getFullData();
            console.log('菜单数据:', menuData);

            // 准备表单数据
            $('#menuData').val(JSON.stringify(menuData));

            // 提交表单（后端会同时保存到主表和副表）
            $('#menuForm').submit();
        } catch (error) {
            console.error('保存菜单失败:', error);
            UIManager.showError('保存菜单失败: ' + error.message);
            UIManager.hideLoading();
        }
    },

    // 确保所有格子的数据都已保存到MenuDataManager
    _ensureAllCellsDataSaved() {
        try {
            console.log('确保所有格子数据已保存...');

            // 遍历所有菜单输入框
            $('.menu-input').each((index, input) => {
                const $input = $(input);
                const date = $input.data('date');
                const meal = $input.data('meal');

                if (!date || !meal) {
                    console.warn('输入框缺少日期或餐次信息:', input);
                    return;
                }

                // 检查输入框是否有菜品
                if ($input.hasClass('has-recipes')) {
                    // 获取当前MenuDataManager中的数据
                    const currentRecipes = MenuDataManager.getRecipes(date, meal);

                    // 如果MenuDataManager中没有数据，但输入框显示有菜品
                    if (currentRecipes.length === 0) {
                        console.log(`发现未同步数据: ${date} ${meal}`);

                        // 尝试从输入框的data属性获取菜品ID
                        const recipeIds = $input.data('recipe-ids');

                        if (recipeIds && recipeIds.length > 0) {
                            console.log(`从输入框恢复菜品数据: ${date} ${meal}, IDs=${recipeIds}`);

                            // 创建菜品对象
                            const recipes = recipeIds.map(id => {
                                // 如果是自定义菜品(以custom_开头)
                                if (id && typeof id === 'string' && id.startsWith('custom_')) {
                                    return {
                                        id: id,
                                        name: $input.text().trim(),
                                        recipe_id: null,
                                        recipe_name: $input.text().trim(),
                                        is_custom: true
                                    };
                                }

                                // 普通菜品
                                return {
                                    id: id,
                                    name: $input.text().trim(),
                                    recipe_id: id,
                                    recipe_name: $input.text().trim()
                                };
                            });

                            // 更新MenuDataManager
                            MenuDataManager.setRecipes(date, meal, recipes);
                            console.log(`已恢复菜品数据: ${date} ${meal}, ${recipes.length}个菜品`);
                        } else {
                            // 如果没有保存菜品ID，尝试从文本内容创建
                            const text = $input.text().trim();
                            if (text && !text.includes('未选择菜品')) {
                                console.log(`从文本创建菜品: ${date} ${meal}, 文本="${text}"`);

                                // 创建自定义菜品
                                const customId = 'custom_' + Date.now() + '_' + index;
                                const recipe = {
                                    id: customId,
                                    name: text,
                                    recipe_id: null,
                                    recipe_name: text,
                                    is_custom: true
                                };

                                // 更新MenuDataManager
                                MenuDataManager.setRecipes(date, meal, [recipe]);
                                console.log(`已创建自定义菜品: ${date} ${meal}, ID=${customId}`);
                            }
                        }
                    } else {
                        console.log(`格子数据已同步: ${date} ${meal}, ${currentRecipes.length}个菜品`);
                    }
                }
            });

            console.log('所有格子数据检查完成');
            return true;
        } catch (error) {
            console.error('确保所有格子数据已保存失败:', error);
            return false;
        }
    },

    // 发布菜单
    publishMenu() {
        const menuId = $('#menu-id').val();
        if (!menuId) {
            UIManager.showMessage('请先保存菜单', 'error');
            return;
        }

        UIManager.showConfirm('确定要发布此菜单吗？发布后将无法修改。', () => {
            UIManager.showLoading();

            $.ajax({
                url: `/api/weekly-menu/${menuId}/publish`,
                method: 'POST',
                success: (response) => {
                    if (response.success) {
                        UIManager.showMessage('菜单发布成功', 'success');
                        // 刷新页面
                        location.reload();
                    } else {
                        UIManager.showMessage(response.message, 'error');
                    }
                },
                error: (xhr) => {
                    UIManager.showMessage('发布菜单失败: ' + xhr.responseText, 'error');
                },
                complete: () => {
                    UIManager.hideLoading();
                }
            });
        });
    },

    // 解除发布
    unpublishMenu() {
        const menuId = $('#menu-id').val();
        if (!menuId) {
            UIManager.showMessage('请先保存菜单', 'error');
            return;
        }

        UIManager.showConfirm('确定要解除发布此菜单吗？', () => {
            UIManager.showLoading();

            $.ajax({
                url: `/api/weekly-menu/${menuId}/unpublish`,
                method: 'POST',
                success: (response) => {
                    if (response.success) {
                        UIManager.showMessage('菜单已解除发布', 'success');
                        // 刷新页面
                        location.reload();
                    } else {
                        UIManager.showMessage(response.message, 'error');
                    }
                },
                error: (xhr) => {
                    UIManager.showMessage('解除发布失败: ' + xhr.responseText, 'error');
                },
                complete: () => {
                    UIManager.hideLoading();
                }
            });
        });
    }
};

// 页面加载完成后初始化
$(document).ready(() => {
    // 获取初始菜单数据
    const menuDataElement = document.getElementById('menuData');
    let initialData = {};

    if (menuDataElement && menuDataElement.value) {
        try {
            initialData = JSON.parse(menuDataElement.value);
        } catch (e) {
            console.error('解析菜单数据失败:', e);
        }
    }

    // 初始化周菜单核心模块
    WeeklyMenuCore.init(initialData);
});
