{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('area.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回区域列表
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" novalidate novalidate novalidate>            {{ form.hidden_tag() }}

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.name.label }}
                        {{ form.name(class="form-control") }}
                        {% for error in form.name.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.code.label }}
                        {{ form.code(class="form-control") }}
                        {% for error in form.code.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                        <small class="form-text text-muted">区域代码必须唯一，建议使用行政区划代码</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.level.label }}
                        {{ form.level(class="form-control") }}
                        {% for error in form.level.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.parent_id.label }}
                        {{ form.parent_id(class="form-control") }}
                        {% for error in form.parent_id.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                {{ form.description.label }}
                {{ form.description(class="form-control", rows=3) }}
                {% for error in form.description.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>

            <div class="mb-3">
                <div class="form-check">
                    {{ form.is_township_school(class="form-check-input") }}
                    {{ form.is_township_school.label(class="form-check-label") }}
                </div>
                <small class="form-text text-muted">勾选此选项表示该学校直接属于乡镇级别，乡镇级别用户可以直接管理此学校</small>
            </div>

            <div class="mb-3 text-center">
                <a href="{{ url_for('area.index') }}" class="btn btn-secondary">取消</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
