{% extends 'daily_management/print/base_print.html' %}

{% block title %}食堂问题记录 - {{ date.strftime('%Y-%m-%d') }}{% endblock %}

{% block document_title %}食堂问题记录{% endblock %}

{% block document_subtitle %}{{ date.strftime('%Y年%m月%d日') }}{% endblock %}

{% block document_info %}
<div class="info-row">
    <div class="info-label">日期：</div>
    <div class="info-value">{{ date.strftime('%Y-%m-%d') }}</div>
</div>
<div class="info-row">
    <div class="info-label">问题数量：</div>
    <div class="info-value">{{ issues|length }}个</div>
</div>
{% endblock %}

{% block content %}
<!-- 问题记录 -->
<div class="section-title">问题记录列表</div>
{% if issues %}
<table>
    <thead>
        <tr>
            <th width="20%">问题类型</th>
            <th width="15%">发现时间</th>
            <th width="15%">状态</th>
            <th width="15%">优先级</th>
            <th width="35%">问题描述</th>
        </tr>
    </thead>
    <tbody>
        {% for issue in issues %}
        <tr>
            <td>{{ issue.issue_type }}</td>
            <td>
                {% if issue.found_time %}
                    {% if issue.found_time is string %}
                        {{ issue.found_time }}
                    {% else %}
                        {{ issue.found_time.strftime('%H:%M') }}
                    {% endif %}
                {% else %}
                    未记录
                {% endif %}
            </td>
            <td>
                {% if issue.status == 'pending' %}
                <span class="abnormal">待处理</span>
                {% elif issue.status == 'processing' %}
                <span class="abnormal">处理中</span>
                {% elif issue.status == 'resolved' %}
                <span class="normal">已解决</span>
                {% else %}
                {{ issue.status }}
                {% endif %}
            </td>
            <td>
                {% if issue.priority == 'high' %}
                <span class="abnormal">高</span>
                {% elif issue.priority == 'medium' %}
                <span>中</span>
                {% elif issue.priority == 'low' %}
                <span class="normal">低</span>
                {% else %}
                {{ issue.priority|default('中') }}
                {% endif %}
            </td>
            <td>{{ issue.description }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- 问题详情 -->
<div class="section-title">问题详情</div>
{% for issue in issues %}
<div style="margin-bottom: 20px; border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
    <h3 style="margin-top: 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">
        {{ issue.issue_type }} - {{ issue.title }}
    </h3>
    
    <div style="margin-bottom: 10px;">
        <strong>发现时间：</strong>
        {% if issue.found_time %}
            {% if issue.found_time is string %}
                {{ issue.found_time }}
            {% else %}
                {{ issue.found_time.strftime('%Y-%m-%d %H:%M') }}
            {% endif %}
        {% else %}
            未记录
        {% endif %}
        <strong class="ms-3">报告人：</strong> {{ issue.reporter_name|default('未知') }}
        <strong class="ms-3">状态：</strong>
        {% if issue.status == 'pending' %}
        <span class="abnormal">待处理</span>
        {% elif issue.status == 'processing' %}
        <span class="abnormal">处理中</span>
        {% elif issue.status == 'resolved' %}
        <span class="normal">已解决</span>
        {% else %}
        {{ issue.status }}
        {% endif %}
        <strong class="ms-3">优先级：</strong>
        {% if issue.priority == 'high' %}
        <span class="abnormal">高</span>
        {% elif issue.priority == 'medium' %}
        <span>中</span>
        {% elif issue.priority == 'low' %}
        <span class="normal">低</span>
        {% else %}
        {{ issue.priority|default('中') }}
        {% endif %}
    </div>
    
    <div style="margin-bottom: 10px;">
        <strong>问题描述：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ issue.description|default('无描述')|nl2br }}
        </div>
    </div>
    
    {% if issue.solution %}
    <div style="margin-bottom: 10px;">
        <strong>解决方案：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ issue.solution|nl2br }}
        </div>
    </div>
    {% endif %}
    
    {% if issue.resolution_time %}
    <div>
        <strong>解决时间：</strong>
        {% if issue.resolution_time is string %}
            {{ issue.resolution_time }}
        {% else %}
            {{ issue.resolution_time.strftime('%Y-%m-%d %H:%M') }}
        {% endif %}
        <strong class="ms-3">解决人：</strong> {{ issue.resolver_name|default('未知') }}
    </div>
    {% endif %}
</div>
{% endfor %}
{% else %}
<p>暂无问题记录</p>
{% endif %}
{% endblock %}

{% block signature %}
<div class="signature-item">
    <div class="signature-line"></div>
    <div>记录人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>食堂负责人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>学校负责人</div>
</div>
{% endblock %}
