{% extends 'base.html' %}

{% block title %}批次编辑器 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .batch-editor-container {
    margin-top: 20px;
  }
  .batch-card {
    margin-bottom: 15px;
    border-start: 4px solid #007bff;
  }
  .batch-header {
    background-color: #f8f9fa;
    padding: 10px;
    cursor: pointer;
  }
  .batch-body {
    padding: 15px;
  }
  .supplier-select {
    max-width: 300px;
  }
  .action-buttons {
    margin-top: 20px;
  }
  .batch-table th, .batch-table td {
    vertical-align: middle;
  }
  .batch-group {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
  }
  .batch-group-header {
    background-color: #e9ecef;
    padding: 10px 15px;
    font-weight: bold;
  }
  .batch-items {
    padding: 15px;
  }
  .row g-3 {
    margin-bottom: 15px;
  }

  /* 新增样式 */
  .highlight-box {
    background-color: #f8f9fa;
    border-start: 4px solid #dc3545;
    padding: 15px;
    margin-bottom: 20px;
  }

  .batch-table th.bg-light {
    background-color: #f8f9fa !important;
    font-size: 1.1rem;
    font-weight: bold;
  }

  .batch-table input[type="number"] {
    transition: all 0.3s;
  }

  .batch-table input[type="number"]:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    background-color: #fff9f9;
  }

  .batch-row:hover {
    background-color: #f8f9fa;
  }

  .batch-row td {
    padding: 12px 8px;
  }

  /* 勾选和未勾选行的样式 */
  .batch-row.table-success {
    background-color: #d4edda !important;
    border-start: 4px solid #28a745;
  }

  .batch-row.table-secondary {
    background-color: #f2f2f2 !important;
    border-start: 4px solid #6c757d;
    opacity: 0.7;
  }

  .batch-row.table-warning {
    background-color: #fff3cd !important;
    border-start: 4px solid #ffc107;
  }

  /* 禁用字段的样式 */
  .batch-row.table-secondary input,
  .batch-row.table-secondary select {
    background-color: #e9ecef;
    cursor: not-allowed;
  }

  .total-price {
    font-weight: bold;
    color: #dc3545;
    font-size: 1.1rem;
  }

  .document-upload {
    border: 2px dashed #e3e6f0;
    padding: 10px;
    text-align: center;
    border-radius: 5px;
    background-color: #f8f9fc;
    cursor: pointer;
    transition: all 0.3s;
  }

  .document-upload:hover {
    border-color: #4e73df;
    background-color: #eef1ff;
  }

  .document-preview {
    max-width: 100%;
    max-height: 150px;
    margin-top: 10px;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
  }

  .date-input-group {
    position: relative;
  }

  .date-input-group .form-control {
    padding-right: 40px;
  }

  .date-input-group .calendar-icon {
    position: absolute;
    right: 10px;
    top: 10px;
    color: #4e73df;
    pointer-events: none;
  }

  .document-list {
    margin-top: 10px;
  }

  .document-item {
    display: flex;
    align-items: center;
    padding: 5px;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
    margin-bottom: 5px;
    background-color: #f8f9fc;
    font-size: 0.85rem;
  }

  .document-item i {
    margin-right: 5px;
    color: #4e73df;
  }

  .document-item .document-name {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .document-item .document-actions {
    margin-left: 5px;
  }

  .info-row {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 5px;
  }

  .info-row i {
    width: 16px;
    text-align: center;
    margin-right: 5px;
  }

  .date-column {
    width: 140px;
  }

  .date-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 2px;
  }

  .quantity-price-cell {
    background-color: #f8f9fc;
    border-start: 3px solid #4e73df;
  }

  .document-cell {
    background-color: #f8f9fc;
    border-start: 3px solid #1cc88a;
  }

  /* 文档操作按钮样式 */
  .document-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    align-items: center;
    justify-content: flex-start;
    max-width: 100px; /* 限制最大宽度，确保自动换行 */
  }

  .document-actions .btn {
    width: 28px;
    height: 28px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 12px;
    border-width: 1px;
    flex-shrink: 0; /* 防止按钮被压缩 */
  }

  .document-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  /* 响应式调整 */
  @d-flex (max-width: 1200px) {
    .document-actions {
      max-width: 90px;
    }
  }

  @d-flex (max-width: 992px) {
    .document-actions {
      max-width: 80px;
    }
  }

  /* 工作流进度条样式 */
  .progress-workflow {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
  }

  .workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 200px;
    position: relative;
  }

  .workflow-step.active .step-icon {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }

  .workflow-step.completed .step-icon {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
  }

  .workflow-step .step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid #dee2e6;
    background-color: #f8f9fa;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
  }

  .workflow-step .step-content h6 {
    margin-bottom: 5px;
    font-weight: bold;
  }

  .workflow-step .step-content p {
    margin: 0;
    color: #6c757d;
  }

  .workflow-arrow {
    font-size: 24px;
    color: #dee2e6;
    margin: 0 20px;
    font-weight: bold;
  }

  .workflow-step.active .step-content h6,
  .workflow-step.completed .step-content h6 {
    color: #495057;
  }

  .workflow-step.active .step-content p,
  .workflow-step.completed .step-content p {
    color: #6c757d;
  }

  @d-flex (max-width: 768px) {
    .progress-workflow {
      flex-direction: column;
    }

    .workflow-arrow {
      transform: rotate(90deg);
      margin: 10px 0;
    }

    .workflow-step {
      min-width: auto;
      width: 100%;
    }
  }

  .batch-editor-container {
    margin-top: 20px;
  }
  .batch-card {
    margin-bottom: 15px;
    border-start: 4px solid #007bff;
  }
  .batch-header {
    background-color: #f8f9fa;
    padding: 10px;
    cursor: pointer;
  }
  .batch-body {
    padding: 15px;
  }
  .supplier-select {
    max-width: 300px;
  }
  .action-buttons {
    margin-top: 20px;
  }
  .batch-table th, .batch-table td {
    vertical-align: middle;
  }
  .batch-group {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
  }
  .batch-group-header {
    background-color: #e9ecef;
    padding: 10px 15px;
    font-weight: bold;
  }
  .batch-items {
    padding: 15px;
  }
  .row g-3 {
    margin-bottom: 15px;
  }

  /* 新增样式 */
  .highlight-box {
    background-color: #f8f9fa;
    border-start: 4px solid #dc3545;
    padding: 15px;
    margin-bottom: 20px;
  }

  .batch-table th.bg-light {
    background-color: #f8f9fa !important;
    font-size: 1.1rem;
    font-weight: bold;
  }

  .batch-table input[type="number"] {
    transition: all 0.3s;
  }

  .batch-table input[type="number"]:focus {
    border-color: #dc3545;
    /* box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25); */ /* 移除阴影效果 */
    background-color: #fff9f9;
  }

  .batch-row:hover {
    background-color: #f8f9fa;
  }

  .batch-row td {
    padding: 12px 8px;
  }

  /* 勾选和未勾选行的样式 */
  .batch-row.table-success {
    background-color: #d4edda !important;
    border-start: 4px solid #28a745;
  }

  .batch-row.table-secondary {
    background-color: #f2f2f2 !important;
    border-start: 4px solid #6c757d;
    opacity: 0.7;
  }

  .batch-row.table-warning {
    background-color: #fff3cd !important;
    border-start: 4px solid #ffc107;
  }

  /* 禁用字段的样式 */
  .batch-row.table-secondary input,
  .batch-row.table-secondary select {
    background-color: #e9ecef;
    cursor: not-allowed;
  }

  .total-price {
    font-weight: bold;
    color: #dc3545;
    font-size: 1.1rem;
  }

  .document-upload {
    border: 2px dashed #e3e6f0;
    padding: 10px;
    text-align: center;
    border-radius: 5px;
    background-color: #f8f9fc;
    cursor: pointer;
    transition: all 0.3s;
  }

  .document-upload:hover {
    border-color: #4e73df;
    background-color: #eef1ff;
  }

  .document-preview {
    max-width: 100%;
    max-height: 150px;
    margin-top: 10px;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
  }

  .date-input-group {
    position: relative;
  }

  .date-input-group .form-control {
    padding-right: 40px;
  }

  .date-input-group .calendar-icon {
    position: absolute;
    right: 10px;
    top: 10px;
    color: #4e73df;
    pointer-events: none;
  }

  .document-list {
    margin-top: 10px;
  }

  .document-item {
    display: flex;
    align-items: center;
    padding: 5px;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
    margin-bottom: 5px;
    background-color: #f8f9fc;
    font-size: 0.85rem;
  }

  .document-item i {
    margin-right: 5px;
    color: #4e73df;
  }

  .document-item .document-name {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .document-item .document-actions {
    margin-left: 5px;
  }

  .info-row {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 5px;
  }

  .info-row i {
    width: 16px;
    text-align: center;
    margin-right: 5px;
  }

  .date-column {
    width: 140px;
  }

  .date-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 2px;
  }

  .quantity-price-cell {
    background-color: #f8f9fc;
    border-start: 3px solid #4e73df;
  }

  .document-cell {
    background-color: #f8f9fc;
    border-start: 3px solid #1cc88a;
  }

  /* 文档操作按钮样式 */
  .document-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    align-items: center;
    justify-content: flex-start;
    max-width: 100px; /* 限制最大宽度，确保自动换行 */
  }

  .document-actions .btn {
    width: 28px;
    height: 28px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 12px;
    border-width: 1px;
    flex-shrink: 0; /* 防止按钮被压缩 */
  }

  .document-actions .btn:hover {
    transform: translateY(-1px);
    /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
  }

  /* 响应式调整 */
  @d-flex (max-width: 1200px) {
    .document-actions {
      max-width: 90px;
    }
  }

  @d-flex (max-width: 992px) {
    .document-actions {
      max-width: 80px;
    }
  }

  /* 工作流进度条样式 */
  .progress-workflow {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
  }

  .workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 200px;
    position: relative;
  }

  .workflow-step.active .step-icon {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }

  .workflow-step.completed .step-icon {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
  }

  .workflow-step .step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid #dee2e6;
    background-color: #f8f9fa;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
  }

  .workflow-step .step-content h6 {
    margin-bottom: 5px;
    font-weight: bold;
  }

  .workflow-step .step-content p {
    margin: 0;
    color: #6c757d;
  }

  .workflow-arrow {
    font-size: 24px;
    color: #dee2e6;
    margin: 0 20px;
    font-weight: bold;
  }

  .workflow-step.active .step-content h6,
  .workflow-step.completed .step-content h6 {
    color: #495057;
  }

  .workflow-step.active .step-content p,
  .workflow-step.completed .step-content p {
    color: #6c757d;
  }

  @d-flex (max-width: 768px) {
    .progress-workflow {
      flex-direction: column;
    }

    .workflow-arrow {
      transform: rotate(90deg);
      margin: 10px 0;
    }

    .workflow-step {
      min-width: auto;
      width: 100%;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- 消息容器 -->
  <div id="alertContainer"></div>

  <div class="row">
    <div class="col-md-12">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 fw-bold text-primary">
            批次编辑器
            {% if stock_in.status == '已审核' %}
              <span class="badge bg-success">已审核</span>
            {% else %}
              <span class="badge bg-warning">{{ stock_in.status }}</span>
            {% endif %}
          </h6>
          <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
              <div class="dropdown-header">操作:</div>
              <a class="dropdown-item" href="#" id="selectAllBatches"><i class="fas fa-check-square fa-sm fa-fw me-2 text-gray-400"></i>全选</a>
              <a class="dropdown-item" href="#" id="deselectAllBatches"><i class="far fa-square fa-sm fa-fw me-2 text-gray-400"></i>取消全选</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="#" id="groupByIngredient"><i class="fas fa-object-group fa-sm fa-fw me-2 text-gray-400"></i>按食材分组</a>
              <a class="dropdown-item" href="#" id="groupBySupplier"><i class="fas fa-building fa-sm fa-fw me-2 text-gray-400"></i>按供应商分组</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="#" id="previewBtn"><i class="fas fa-eye fa-sm fa-fw me-2 text-gray-400"></i>预览打印效果</a>
              <a class="dropdown-item" href="#" id="printBtn"><i class="fas fa-print fa-sm fa-fw me-2 text-gray-400"></i>打印入库单</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="{{ url_for('stock_in.view_details', id=stock_in.id) }}"><i class="fas fa-table fa-sm fa-fw me-2 text-gray-400"></i>传统视图</a>
              <a class="dropdown-item" href="{{ url_for('stock_in.index') }}"><i class="fas fa-list fa-sm fa-fw me-2 text-gray-400"></i>返回列表</a>
            </div>
          </div>
        </div>
        <div class="card-body">
          <!-- 入库工作流进度条 -->
          <div class="card mb-4">
            <div class="card-header bg-info text-white">
              <h5 class="mb-0"><i class="fas fa-tasks"></i> 入库工作流进度</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-12">
                  <div class="progress-workflow">
                    <div class="workflow-step active">
                      <div class="step-icon">
                        <i class="fas fa-edit"></i>
                      </div>
                      <div class="step-content">
                        <h6>当前步骤: 编辑批次信息</h6>
                        <p class="small">设置供应商、存储位置、数量单价等</p>
                      </div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                      <div class="step-icon">
                        <i class="fas fa-save"></i>
                      </div>
                      <div class="step-content">
                        <h6>下一步: 保存信息</h6>
                        <p class="small">保存批次信息并跳转到详情页面</p>
                      </div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                      <div class="step-icon">
                        <i class="fas fa-warehouse"></i>
                      </div>
                      <div class="step-content">
                        <h6>最后步骤: 审核入库</h6>
                        <p class="small">在详情页面进行审核和确认入库</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 当前状态提示 -->
              <div class="mt-3">
                <div class="alert alert-info">
                  <i class="fas fa-info-circle"></i>
                  <strong>批次编辑器</strong> - 请完成批次信息编辑，然后点击"保存批次信息"按钮。保存后将跳转到详情页面进行审核和入库操作。
                </div>
                {% if stock_in.status != '待审核' %}
                <div class="alert alert-warning">
                  <i class="fas fa-exclamation-triangle"></i>
                  <strong>注意：</strong>当前入库单状态为"{{ stock_in.status }}"，建议在详情页面进行后续操作。
                </div>
                {% endif %}
              </div>
            </div>
          </div>

          <div class="highlight-box">
            <h5><i class="fas fa-info-circle text-primary"></i> 批次编辑器使用说明</h5>
            <div class="row">
              <div class="col-md-9">
                <p>请按照以下步骤操作：</p>
                <ol>
                  <li>勾选需要入库的食材（<strong>只有勾选的食材会被入库</strong>）</li>
                  <li>使用上方的<strong>批量设置</strong>功能统一设置供应商、存储位置和日期</li>
                  <li><strong class="text-danger">重点关注数量和单价</strong>，确保数值准确无误</li>
                  <li>为每个批次上传<strong>检验检疫证明</strong>或<strong>入库单据</strong></li>
                  <li>完成编辑后点击<strong>保存批次信息</strong>按钮</li>
                  <li class="text-success"><strong>最后通过工作流完成审核和入库，数据将保存到学校系统</strong></li>
                </ol>
              </div>
              <div class="col-md-3">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <h5 class="card-title">已选择食材</h5>
                    <p class="card-text">
                      <span id="selectedCount" class="badge bg-success" style="font-size: 1.5rem;">0</span> /
                      <span id="totalCount">0</span>
                    </p>
                    <p class="text-muted small">勾选的食材将被入库<br>未勾选的食材将被忽略</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 批量操作表单 -->
          <form id="batchEditForm" method="post" action="{{ url_for('stock_in.save_batch_edit_simplified', stock_in_id=stock_in.id) }}" enctype="multipart/form-data" novalidate novalidate>
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <!-- 批量操作工具栏 -->
            <div class="card mb-4">
              <div class="card-header bg-light">
                <h6 class="mb-0 text-primary"><i class="fas fa-tools"></i> 批量设置工具</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3">
                    <div class="mb-3 mb-0">
                      <label for="bulkSupplier" class="form-label"><strong>批量设置供应商:</strong></label>
                      <div class="input-group">
                        <select class="form-control" id="bulkSupplier">
                          <option value="">-- 请选择供应商 --</option>
                          {% for supplier in suppliers %}
                          <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                          {% endfor %}
                        </select>
                        <div >
                          <button class="btn btn-primary" type="button" id="applyBulkSupplier" style="height: 38px;">应用</button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="mb-3 mb-0">
                      <label for="bulkStorageLocation" class="form-label"><strong>批量设置存储位置:</strong></label>
                      <div class="input-group">
                        <select class="form-control" id="bulkStorageLocation">
                          <option value="">-- 请选择存储位置 --</option>
                          {% for location in storage_locations %}
                          <option value="{{ location.id }}">{{ location.name }}</option>
                          {% endfor %}
                        </select>
                        <div >
                          <button class="btn btn-primary" type="button" id="applyBulkStorageLocation" style="height: 38px;">应用</button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="mb-3 mb-0">
                      <label class="form-label"><strong>批量设置日期:</strong></label>
                      <div class="input-group">
                        <input type="date" class="form-control" id="bulkProductionDate" value="{{ now.strftime('%Y-%m-%d') }}">
                        <div >
                          <button class="btn btn-primary" type="button" id="applyBulkDates" style="height: 38px;">应用</button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="mb-3 mb-0">
                      <label class="form-label"><strong>文档管理:</strong></label>
                      <button type="button" class="btn btn-success w-100" data-bs-toggle="modal" data-bs-target="#uploadDocumentModal" style="height: 38px;">
                        <i class="fas fa-upload"></i> 上传文档
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 批次列表 -->
            <div class="table-responsive">
              <table class="table table-bordered batch-table" id="batchTable" width="100%" cellspacing="0">
                <thead class="thead-dark">
                  <tr>
                    <th width="5%" class="text-center">
                      <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="selectAll">
                        <label class="form-check-label" for="selectAll"></label>
                      </div>
                    </th>
                    <th width="20%">食材信息</th>
                    <th width="18%">供应商/存储位置</th>
                    <th width="17%">日期信息</th>
                    <th width="20%">数量/单价</th>
                    <th width="20%">检验检疫证明/入库单据</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in stock_in_items %}
                  <tr class="batch-row" data-ingredient-id="{{ item.ingredient_id }}" data-supplier-id="{{ item.supplier_id }}" id="row_{{ item.id }}">
                    <td>
                      <div class="form-check">
                        <input type="checkbox" class="form-check-input batch-checkbox" id="batch{{ loop.index }}" name="selected_items[]" value="{{ item.id }}">
                        <label class="form-check-label" for="batch{{ loop.index }}"></label>
                      </div>
                    </td>
                    <td>
                      <div class="fw-bold">{{ item.ingredient.name }}</div>
                      <div class="info-row">
                        <i class="fas fa-barcode"></i> 批次号:
                        {% if item.batch_number %}
                          <span class="text-primary fw-bold">{{ item.batch_number }}</span>
                        {% else %}
                          <span class="text-danger">未设置</span>
                        {% endif %}
                      </div>
                      <div class="info-row">
                        <i class="fas fa-balance-scale"></i> 单位: {{ item.unit }}
                      </div>
                      {% if item.ingredient.category_rel %}
                      <div class="info-row">
                        <i class="fas fa-tags"></i> 分类: {{ item.ingredient.category_rel.name }}
                      </div>
                      {% endif %}

                      <input type="hidden" name="batch_number_{{ item.id }}" value="{{ item.batch_number }}">
                      <input type="hidden" name="unit_{{ item.id }}" value="{{ item.unit }}">
                    </td>
                    <td>
                      <select class="form-control mb-2 supplier-select" name="supplier_id_{{ item.id }}" required>
                        <option value="">-- 请选择供应商 --</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if item.supplier_id == supplier.id %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                      </select>
                      <select class="form-control" name="storage_location_id_{{ item.id }}" required>
                        <option value="">-- 请选择存储位置 --</option>
                        {% for location in storage_locations %}
                        <option value="{{ location.id }}" {% if item.storage_location_id == location.id %}selected{% endif %}>{{ location.name }}</option>
                        {% endfor %}
                      </select>
                    </td>
                    <td>
                      <div class="date-label">生产日期:</div>
                      <div class="date-input-group mb-2">
                        <input type="date" class="form-control" name="production_date_{{ item.id }}"
                               value="{{ item.production_date.strftime('%Y-%m-%d') if item.production_date else now.strftime('%Y-%m-%d') }}" required>
                        <div class="calendar-icon">
                          <i class="fas fa-calendar-alt"></i>
                        </div>
                      </div>
                      <div class="date-label">过期日期:</div>
                      <div class="date-input-group">
                        <input type="date" class="form-control" name="expiry_date_{{ item.id }}"
                               value="{{ item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else (now + timedelta(days=30)).strftime('%Y-%m-%d') }}" required>
                        <div class="calendar-icon">
                          <i class="fas fa-calendar-alt"></i>
                        </div>
                      </div>
                    </td>
                    <td class="quantity-price-cell">
                      <div class="mb-3">
                        <label class="fw-bold text-danger">数量:</label>
                        <input type="number" class="form-control form-control-lg fw-bold text-danger"
                               name="quantity_{{ item.id }}" value="{{ item.quantity }}" step="0.01" min="0"
                               style="font-size: 1.2rem;" required>
                      </div>
                      <div class="mb-3">
                        <label class="fw-bold text-danger">单价(元):</label>
                        <input type="number" class="form-control form-control-lg fw-bold text-danger"
                               name="unit_price_{{ item.id }}" value="{{ item.unit_price }}" step="0.01" min="0"
                               style="font-size: 1.2rem;" required>
                      </div>
                      <div class="total-price mt-2"></div>
                    </td>
                    <td class="document-cell">
                      <div class="document-list" id="document_list_{{ item.id }}">
                        {% for doc in item.documents %}
                        <div class="document-item mb-2">
                          <span class="badge badge-{{ 'warning' if doc.document_type in ['检验检疫证明', '质量检测报告'] else 'info' }}" style="font-size: 0.9rem; padding: 0.5rem 0.75rem;">
                            {{ doc.document_type }}
                          </span>
                          <div class="document-actions mt-1">
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#viewDocModal{{ doc.id }}" title="查看文档">
                              <i class="fas fa-eye"></i>
                            </button>
                            <a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" download class="btn btn-outline-success" title="下载文档">
                              <i class="fas fa-download"></i>
                            </a>
                            {% if stock_in.status == '待审核' %}
                            <button type="button" class="btn btn-outline-danger" data-action="critical-confirm" data-delete-code="deleteDocument({{ doc.id }})" data-confirm-message="确定要删除吗？" style="cursor: pointer;" title="删除文档">
                              <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                          </div>
                        </div>
                        {% else %}
                        <div class="text-center text-muted py-3">
                          <i class="fas fa-file-alt fa-2x mb-2"></i>
                          <p class="mb-0">暂无文档</p>
                          <small>请使用上方"上传新文档"功能添加</small>
                        </div>
                        {% endfor %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <!-- 批次编辑操作 -->
              <div class="text-center mt-3 mb-4">
                <div class="btn-group" role="group">
                  <a href="{{ url_for('stock_in.view', id=stock_in.id) }}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times"></i> 取消
                  </a>
                  <button type="submit" class="btn btn-success btn-lg" id="saveBtn">
                    <i class="fas fa-save"></i> 保存批次信息
                  </button>
                </div>
              </div>

              <!-- 下一步操作提示 -->
              <div class="card mt-4">
                <div class="card-header bg-success text-white">
                  <h5 class="mb-0"><i class="fas fa-arrow-right"></i> 下一步操作</h5>
                </div>
                <div class="card-body">
                  <div class="text-center">
                    <div class="alert alert-success">
                      <i class="fas fa-info-circle"></i>
                      <strong>保存完成后</strong> - 系统将自动跳转到详情页面，您可以在那里进行审核和确认入库操作
                    </div>
                    <a href="{{ url_for('stock_in.view_details', id=stock_in.id) }}" class="btn btn-info btn-lg">
                      <i class="fas fa-eye"></i> 直接查看详情页面
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- 文档查看模态框 -->
{% for item in stock_in_items %}
  {% for doc in item.documents %}
<div class="modal fade" id="viewDocModal{{ doc.id }}" tabindex="-1" role="dialog" aria-labelledby="viewDocModalLabel{{ doc.id }}" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="viewDocModalLabel{{ doc.id }}">
                    <i class="fas fa-file-alt"></i> 查看文档 - {{ doc.document_type }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="row g-0">
                    <!-- 文档信息面板 -->
                    <div class="col-md-3 document-info-panel">
                        <div class="p-3">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle"></i> 文档信息
                            </h6>

                            <div class="mb-3">
                                <small class="text-muted">文档类型</small>
                                <div>
                                    <span class="badge badge-{{ 'warning' if doc.document_type in ['检验检疫证明', '质量检测报告'] else 'info' }}">
                                        {{ doc.document_type }}
                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">文件名</small>
                                <div class="small text-break">{{ doc.file_path.split('/')[-1] }}</div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">上传时间</small>
                                <div class="small">{{ doc.created_at|format_datetime('%Y-%m-%d %H:%M') }}</div>
                            </div>

                            {% if doc.notes %}
                            <div class="mb-3">
                                <small class="text-muted">备注</small>
                                <div class="small">{{ doc.notes }}</div>
                            </div>
                            {% endif %}

                            <div class="mt-4">
                                <a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" download class="btn btn-success btn-sm w-100">
                                    <i class="fas fa-download"></i> 下载文档
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 文档预览面板 -->
                    <div class="col-md-9 document-preview-panel">
                      <div class="p-3">
                          <div class="text-center document-preview">
                              {% set file_ext = doc.file_path.split('.')[-1].lower() %}
                              {% if file_ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] %}
                              <!-- 图片预览 -->
                              <div class="text-center py-5 image-loading" id="loading_{{ doc.id }}">
                                  <i class="fas fa-spinner fa-spin fa-3x text-primary"></i>
                                  <p class="mt-3 text-muted">正在加载图片...</p>
                              </div>
                              <img src="{{ url_for('static', filename=doc.file_path|fix_path) }}"
                                   class="img-fluid"
                                   style="max-height: 70vh; border: 1px solid #ddd; border-radius: 4px; display: none;"
                                   alt="文档预览"
                                   id="image_{{ doc.id }}"
                                   onload="showImage({{ doc.id }})"
                                   onerror="handleImageError(this, '{{ url_for('static', filename=doc.file_path|fix_path) }}', {{ doc.id }})">
                              {% elif file_ext == 'pdf' %}
                              <!-- PDF预览 -->
                              <iframe src="{{ url_for('static', filename=doc.file_path|fix_path) }}"
                                      style="width: 100%; height: 70vh; border: 1px solid #ddd; border-radius: 4px;"
                                      frameborder="0">
                                  <p>您的浏览器不支持PDF预览。<a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" target="_blank">点击这里下载文档</a></p>
                              </iframe>
                              {% else %}
                              <!-- 其他文件类型 -->
                              <div class="text-center py-5">
                                  <div class="mb-4 file-type-icon">
                                      <i class="fas fa-file-alt fa-5x"></i>
                                  </div>
                                  <h5 class="text-muted">无法预览此文件类型</h5>
                                  <p class="text-muted">
                                      文件类型：{{ file_ext.upper() }}<br>
                                      请下载文件后使用相应软件打开
                                  </p>
                                  <a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" download class="btn btn-primary">
                                      <i class="fas fa-download"></i> 下载文档
                                  </a>
                              </div>
                              {% endif %}
                          </div>
                      </div>
                  </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                {% if stock_in.status == '待审核' %}
                <button type="button" class="btn btn-danger" data-action="critical-confirm" data-delete-code="deleteDocument({{ doc.id }})" data-confirm-message="确定要删除吗？" style="cursor: pointer;">
                    <i class="fas fa-trash"></i> 删除文档
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
  {% endfor %}
{% endfor %}

<!-- 文档上传模态框 -->
<div class="modal fade" id="uploadDocumentModal" tabindex="-1" role="dialog" aria-labelledby="uploadDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="uploadDocumentModalLabel"><i class="fas fa-file-upload"></i> 上传入库单据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="uploadDocumentForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="document_type">单据类型 <span class="text-danger">*</span></label>
                        <select class="form-control" id="document_type" name="document_type" required>
                            <option value="送货单">送货单</option>
                            <option value="检验检疫证明">检验检疫证明</option>
                            <option value="质量检测报告">质量检测报告</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="document_supplier_id">关联供应商</label>
                        <select class="form-control" id="document_supplier_id" name="supplier_id">
                            <option value="">-- 请选择供应商 --</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="batch_numbers">关联食材批次号</label>
                        <textarea class="form-control" id="batch_numbers" name="batch_numbers" rows="3"
                                  placeholder="请输入要关联的批次号，每行一个批次号。例如：&#10;B20250127001&#10;B20250127002"></textarea>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i>
                            输入要关联的食材批次号，每行一个。系统会自动匹配该入库单中的对应批次。
                        </small>

                        <!-- 显示当前入库单的批次号 -->
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="showBatchNumbers()">
                                <i class="fas fa-list"></i> 查看当前入库单的批次号
                            </button>
                        </div>

                        <!-- 批次号列表（隐藏） -->
                        <div id="batchNumbersList" class="mt-2" style="display: none;">
                            <div class="alert alert-info">
                                <strong>当前入库单的批次号：</strong><br>
                                {% for item in stock_in_items %}
                                <span class="badge bg-secondary me-1 mb-1" onclick="addBatchNumber('{{ item.batch_number }}')" style="cursor: pointer;">
                                    {{ item.ingredient.name }}: {{ item.batch_number }}
                                </span>
                                {% endfor %}
                                <br><small class="text-muted">点击批次号可快速添加到关联列表</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="document">选择文件 <span class="text-danger">*</span></label>
                        <div class="form-control">
                            <input type="file" class="form-control-input" id="document" name="document" required>
                            <label class="form-control-label" for="document">选择文件...</label>
                        </div>
                        <small class="form-text text-muted">支持的文件格式：PDF, PNG, JPG, JPEG, DOC, DOCX, XLS, XLSX</small>
                    </div>

                    <div class="mb-3">
                        <label for="document_notes">备注</label>
                        <textarea class="form-control" id="document_notes" name="notes" rows="3"></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-upload"></i> 上传文档
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}
{% block scripts %}
{{ super() }}
{% include 'stock_in/batch_editor_simplified_scripts.html' %}
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>