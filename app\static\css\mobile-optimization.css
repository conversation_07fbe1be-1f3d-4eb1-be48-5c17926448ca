/* 移动端专用优化样式 - 全面重构版本 */

/* === 移动端显示控制 === */
@media (max-width: 768px) {
  .desktop-only { display: none !important; }
  .mobile-only { display: block !important; }
  .mobile-hidden { display: none !important; }
  .mobile-mb-2 { margin-bottom: 0.5rem !important; }
}

@media (min-width: 769px) {
  .desktop-only { display: block !important; }
  .mobile-only { display: none !important; }
  .mobile-hidden { display: block !important; }
}

/* === 侧边栏布局移动端优化 === */
@media (max-width: 768px) {
  /* 防止body滚动当侧边栏打开时 */
  body.sidebar-open {
    overflow: hidden;
  }

  /* 侧边栏在移动端的特殊样式 */
  .sidebar {
    z-index: 1050; /* 确保在Bootstrap模态框之上 */
  }

  /* 侧边栏遮罩层 */
  .sidebar-overlay {
    z-index: 1040;
  }

  /* 移动端侧边栏菜单项优化 */
  .sidebar-nav-link,
  .sidebar-dropdown-item {
    padding: 15px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  /* 移动端下拉菜单优化 */
  .sidebar-dropdown-menu {
    background: rgba(0, 0, 0, 0.3);
  }

  .sidebar-dropdown-item {
    padding: 12px 20px 12px 52px;
    font-size: 15px;
  }
}

/* === 基础移动端适配 === */
@media (max-width: 768px) {
  /* 全局移动端优化 */
  * {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    -webkit-touch-callout: none;
  }

  /* 容器优化 */
  .container,
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }

  /* 输入框优化 */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  input[type="date"],
  input[type="time"],
  textarea,
  select {
    font-size: 16px; /* 防止iOS缩放 */
    -webkit-appearance: none;
    border-radius: 8px;
    touch-action: manipulation;
    min-height: 44px;
    padding: 12px 16px;
  }

  /* 滚动优化 */
  body {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 页面标题优化 */
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.1rem; }
  h5 { font-size: 1rem; }
  h6 { font-size: 0.9rem; }
}

/* === 导航栏移动端优化 === */
@media (max-width: 768px) {
  .navbar {
    padding: 0.5rem 1rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
    padding: 0.25rem 0;
  }

  .navbar-toggler {
    padding: 0.25rem 0.5rem;
    font-size: 1rem;
    border: none;
  }

  .navbar-toggler:focus {
    /* box-shadow: none; */ /* 移除阴影效果 */
  }

  .navbar-collapse {
    margin-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 0.5rem;
  }

  .navbar-nav .nav-item {
    width: 100%;
    margin: 0;
  }

  .navbar-nav .nav-link {
    padding: 12px 16px;
    font-size: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    min-height: 44px;
    border-radius: 0;
    margin: 0;
  }

  .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: none;
  }

  .navbar-nav .nav-link:last-child {
    border-bottom: none;
  }

  .navbar-nav .nav-link i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
  }

  /* 下拉菜单移动端优化 */
  .navbar-nav .dropdown-menu {
    position: static;
    float: none;
    width: 100%;
    margin-top: 0;
    background-color: rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 0;
    /* box-shadow: none; */ /* 移除阴影效果 */
    padding: 0;
  }

  .navbar-nav .dropdown-menu::before {
    display: none;
  }

  .navbar-nav .dropdown-item {
    padding: 12px 32px;
    font-size: 15px;
    color: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 44px;
    display: flex;
    align-items: center;
    margin: 0;
    border-radius: 0;
    text-decoration: none;
    transform: none;
  }

  .navbar-nav .dropdown-item:hover,
  .navbar-nav .dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: none;
    /* box-shadow: none; */ /* 移除阴影效果 */
    text-decoration: none;
  }

  .navbar-nav .dropdown-item i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
  }

  /* 主题切换器移动端优化 */
  .navbar-nav .dropdown-menu[aria-labelledby="themeDropdown"] {
    max-height: 60vh !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .navbar-nav .dropdown-header {
    padding: 8px 32px !important;
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.6) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    margin: 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: none !important;
  }

  .theme-preview {
    width: 16px !important;
    height: 16px !important;
    border-radius: 50% !important;
    margin-right: 8px !important;
    display: inline-block !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
  }

  /* 移动端触摸优化 */
  .navbar-nav .dropdown-toggle {
    cursor: pointer !important;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1) !important;
    touch-action: manipulation !important;
    user-select: none !important;
  }

  /* 确保移动端下拉菜单正常显示 */
  .navbar-nav .dropdown.show .dropdown-menu {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* 移动端下拉菜单动画禁用 */
  .navbar-nav .dropdown-menu.show {
    animation: none !important;
  }
}

/* === 容器和布局移动端优化 === */
@media (max-width: 768px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }

  /* 卡片布局优化 */
  .card {
    margin-bottom: 15px;
    border-radius: 12px;
    /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
  }

  .card-header {
    padding: 15px;
    border-radius: 12px 12px 0 0 !important;
  }

  .card-body {
    padding: 15px;
  }

  .card-footer {
    padding: 12px 15px;
  }

  /* 行间距优化 */
  .row {
    margin-left: -10px;
    margin-right: -10px;
  }

  .row > [class*="col-"] {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 15px;
  }
}

/* === 按钮移动端优化 === */
@media (max-width: 768px) {
  .btn {
    min-height: 44px;
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 8px;
    font-weight: 500;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    margin-bottom: 8px;
  }

  .btn-sm {
    min-height: 36px;
    padding: 8px 16px;
    font-size: 14px;
  }

  .btn-lg {
    min-height: 52px;
    padding: 16px 24px;
    font-size: 18px;
  }

  /* 移动端按钮组 */
  .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .btn-group .btn {
    flex: 1;
    margin-bottom: 0;
  }

  /* 移动端操作按钮 */
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons .btn {
    width: 100%;
    margin-bottom: 0;
  }
}

  /* 按钮组优化 */
  .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .btn-group .btn {
    flex: 1;
    margin-bottom: 0;
  }

  .btn-group-vertical .btn {
    width: 100%;
    margin-bottom: 8px;
  }

  /* 操作按钮区域 */
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
  }

  .action-buttons .btn {
    width: 100%;
  }

  /* 快速操作按钮 */
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin: 20px 0;
  }

  .quick-actions .btn {
    padding: 16px 12px;
    flex-direction: column;
    min-height: 80px;
  }

  .quick-actions .btn i {
    font-size: 24px;
    margin-bottom: 8px;
  }
}

/* === 表单移动端优化 === */
@media (max-width: 768px) {
  .form-group {
    margin-bottom: 20px;
  }

  .form-control {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  .form-control:focus {
    border-color: var(--theme-primary);
    /* box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.25); */ /* 移除阴影效果 */
  }

  .form-control-sm {
    min-height: 36px;
    padding: 8px 12px;
    font-size: 14px;
  }

  .form-control-lg {
    min-height: 52px;
    padding: 16px 20px;
    font-size: 18px;
  }

  /* 标签优化 */
  .form-label,
  label {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
  }

  /* 选择框优化 */
  select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
  }

  /* 复选框和单选框优化 */
  .form-check {
    padding-left: 0;
    margin-bottom: 12px;
  }

  .form-check-input {
    width: 20px;
    height: 20px;
    margin-top: 0;
    margin-right: 12px;
    vertical-align: top;
    border: 2px solid #dee2e6;
    border-radius: 4px;
  }

  .form-check-input:checked {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
  }

  .form-check-label {
    font-size: 16px;
    line-height: 1.5;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  /* 输入组优化 */
  .input-group {
    margin-bottom: 20px;
  }

  .input-group-text {
    padding: 12px 16px;
    font-size: 16px;
    border: 2px solid #e9ecef;
    background-color: #f8f9fa;
  }

  /* 文件上传优化 */
  .custom-file {
    height: 44px;
  }

  .custom-file-input {
    height: 44px;
  }

  .custom-file-label {
    height: 44px;
    padding: 12px 16px;
    font-size: 16px;
    line-height: 1.2;
    border: 2px solid #e9ecef;
    border-radius: 8px;
  }

  .custom-file-label::after {
    height: 40px;
    padding: 12px 16px;
    line-height: 1.2;
    border-radius: 0 6px 6px 0;
  }
}

/* === 表格移动端优化增强 === */
@media (max-width: 768px) {
  .table-responsive {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
  }

  .table {
    margin-bottom: 0;
    font-size: 14px;
  }

  .table thead th {
    padding: 12px 8px;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .table tbody td {
    padding: 12px 8px;
    font-size: 14px;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
  }

  .table tbody tr:hover {
    background-color: rgba(var(--theme-primary-rgb), 0.05);
  }

  /* 操作列优化 */
  .table .action-column {
    width: 100px;
    text-align: center;
  }

  .table .action-column .btn {
    padding: 6px 10px;
    font-size: 12px;
    margin: 2px;
  }

  /* 状态徽章优化 */
  .badge {
    font-size: 12px;
    padding: 6px 10px;
    border-radius: 20px;
    font-weight: 500;
  }

  /* 移动端表格卡片模式 */
  .table-mobile-cards {
    display: none;
  }

  .table-mobile-cards .mobile-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
    border: 1px solid #e9ecef;
  }

  .table-mobile-cards .mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e9ecef;
  }

  .table-mobile-cards .mobile-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }

  .table-mobile-cards .mobile-card-body {
    display: grid;
    gap: 8px;
  }

  .table-mobile-cards .mobile-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
  }

  .table-mobile-cards .mobile-field-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
  }

  .table-mobile-cards .mobile-field-value {
    font-size: 14px;
    color: #495057;
    text-align: right;
  }

  .table-mobile-cards .mobile-card-actions {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

/* === 模态框移动端优化 === */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 10px;
    max-width: calc(100vw - 20px);
  }

  .modal-content {
    border-radius: 16px;
    border: none;
    /* box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2); */ /* 移除阴影效果 */
  }

  .modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 16px 16px 0 0;
  }

  .modal-title {
    font-size: 18px;
    font-weight: 600;
  }

  .modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 16px 16px;
  }

  .modal-footer .btn {
    width: 100%;
    margin-bottom: 8px;
  }

  .modal-footer .btn:last-child {
    margin-bottom: 0;
  }

  /* 关闭按钮优化 */
  .close {
    padding: 8px;
    margin: -8px;
    font-size: 24px;
    line-height: 1;
    opacity: 0.7;
  }

  .close:hover {
    opacity: 1;
  }
}

/* === 通知和提示移动端优化 === */
@media (max-width: 768px) {
  .alert {
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    font-size: 15px;
    line-height: 1.5;
  }

  .alert-dismissible .close {
    padding: 12px 16px;
    font-size: 20px;
  }

  /* Toast通知优化 */
  .toast {
    max-width: calc(100vw - 30px);
    margin: 0 15px;
    border-radius: 12px;
    /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); */ /* 移除阴影效果 */
  }

  .toast-header {
    padding: 12px 16px;
    border-radius: 12px 12px 0 0;
  }

  .toast-body {
    padding: 12px 16px;
    font-size: 15px;
  }

  /* 通知下拉菜单优化 */
  .notification-dropdown {
    width: calc(100vw - 30px) !important;
    max-width: 400px !important;
    left: 15px !important;
    right: 15px !important;
    transform: none !important;
  }

  .notification-item {
    padding: 16px !important;
    border-bottom: 1px solid #e9ecef;
  }

  .notification-title {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .notification-content {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 4px;
  }

  .notification-time {
    font-size: 12px;
    color: #adb5bd;
  }
}

/* === 分页移动端优化 === */
@media (max-width: 768px) {
  .pagination {
    justify-content: center;
    margin: 20px 0;
  }

  .page-item .page-link {
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 8px;
    margin: 0 2px;
    min-width: 44px;
    text-align: center;
  }

  /* 简化分页显示 */
  .pagination-mobile {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
  }

  .pagination-mobile .btn {
    min-width: 100px;
  }

  .pagination-info {
    font-size: 14px;
    color: #6c757d;
    text-align: center;
  }
}

/* === 搜索和筛选移动端优化 === */
@media (max-width: 768px) {
  .search-form {
    margin-bottom: 20px;
  }

  .search-input-group {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
  }

  .search-input-group .form-control {
    flex: 1;
  }

  .search-input-group .btn {
    min-width: 60px;
    padding: 12px;
  }

  /* 筛选器优化 */
  .filter-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
  }

  .filter-row {
    display: grid;
    gap: 12px;
    margin-bottom: 12px;
  }

  .filter-row:last-child {
    margin-bottom: 0;
  }

  .filter-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
  }

  .filter-actions .btn {
    flex: 1;
  }

  /* 快速筛选标签 */
  .quick-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
  }

  .quick-filter-tag {
    padding: 8px 12px;
    background: #e9ecef;
    border-radius: 20px;
    font-size: 14px;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s;
  }

  .quick-filter-tag.active,
  .quick-filter-tag:hover {
    background: var(--theme-primary);
    color: white;
  }
}

/* === 特殊组件移动端优化 === */
@media (max-width: 768px) {
  /* 进度条优化 */
  .progress {
    height: 8px;
    border-radius: 4px;
    margin: 12px 0;
  }

  /* 面包屑导航优化 */
  .breadcrumb {
    background: transparent;
    padding: 12px 0;
    margin-bottom: 16px;
    font-size: 14px;
  }

  .breadcrumb-item {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 标签页优化 */
  .nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }

  .nav-tabs .nav-item {
    display: inline-block;
    float: none;
  }

  .nav-tabs .nav-link {
    padding: 12px 16px;
    font-size: 15px;
    border: none;
    border-bottom: 3px solid transparent;
    border-radius: 0;
    min-width: 100px;
    text-align: center;
  }

  .nav-tabs .nav-link.active {
    border-bottom-color: var(--theme-primary);
    background: transparent;
    color: var(--theme-primary);
    font-weight: 600;
  }

  /* 手风琴优化 */
  .accordion .card {
    border: 1px solid #e9ecef;
    border-radius: 8px !important;
    margin-bottom: 8px;
  }

  .accordion .card-header {
    padding: 16px;
    background: #f8f9fa;
    border-bottom: none;
    border-radius: 8px !important;
  }

  .accordion .btn-link {
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    color: #495057;
    width: 100%;
    text-align: left;
    padding: 0;
  }

  .accordion .card-body {
    padding: 16px;
  }
}

/* === 移动端专用工具类 === */
@media (max-width: 768px) {
  /* 显示/隐藏工具类 */
  .mobile-only {
    display: block !important;
  }

  .mobile-hidden {
    display: none !important;
  }

  .desktop-only {
    display: none !important;
  }

  /* 间距工具类 */
  .mobile-mt-0 { margin-top: 0 !important; }
  .mobile-mt-1 { margin-top: 8px !important; }
  .mobile-mt-2 { margin-top: 16px !important; }
  .mobile-mt-3 { margin-top: 24px !important; }
  .mobile-mt-4 { margin-top: 32px !important; }

  .mobile-mb-0 { margin-bottom: 0 !important; }
  .mobile-mb-1 { margin-bottom: 8px !important; }
  .mobile-mb-2 { margin-bottom: 16px !important; }
  .mobile-mb-3 { margin-bottom: 24px !important; }
  .mobile-mb-4 { margin-bottom: 32px !important; }

  .mobile-p-0 { padding: 0 !important; }
  .mobile-p-1 { padding: 8px !important; }
  .mobile-p-2 { padding: 16px !important; }
  .mobile-p-3 { padding: 24px !important; }

  /* 文本工具类 */
  .mobile-text-center { text-align: center !important; }
  .mobile-text-left { text-align: left !important; }
  .mobile-text-right { text-align: right !important; }

  .mobile-text-sm { font-size: 14px !important; }
  .mobile-text-base { font-size: 16px !important; }
  .mobile-text-lg { font-size: 18px !important; }

  /* 布局工具类 */
  .mobile-full-width { width: 100% !important; }
  .mobile-flex { display: flex !important; }
  .mobile-flex-column { flex-direction: column !important; }
  .mobile-flex-wrap { flex-wrap: wrap !important; }
  .mobile-justify-center { justify-content: center !important; }
  .mobile-align-center { align-items: center !important; }
}

/* === 触控优化 === */
@media (max-width: 768px) {
  /* 触控反馈 */
  .btn:active,
  .nav-link:active,
  .dropdown-item:active {
    transform: scale(0.98);
    transition: transform 0.1s;
  }

  /* 滑动指示器 */
  .swipe-indicator {
    position: relative;
  }

  .swipe-indicator::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: #dee2e6;
    border-radius: 2px;
  }

  /* 长按效果 */
  .long-press {
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  /* 防止意外缩放 */
  .no-zoom {
    touch-action: manipulation;
  }
}

/* === 性能优化 === */
@media (max-width: 768px) {
  /* 减少动画 */
  .reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  /* 硬件加速 */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  /* 滚动优化 */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* === 移动端表格响应式优化 === */
@media (max-width: 768px) {
  /* 隐藏桌面表格，显示移动端卡片 */
  .table-responsive.mobile-cards-enabled .table {
    display: none !important;
  }

  .table-responsive.mobile-cards-enabled .table-mobile-cards {
    display: block !important;
  }

  /* 移动端表格滚动优化 */
  .table-responsive {
    -webkit-overflow-scrolling: touch !important;
    scrollbar-width: thin !important;
  }

  .table-responsive::-webkit-scrollbar {
    height: 6px !important;
  }

  .table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
  }

  .table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;
  }
}

/* === 移动端页面布局优化 === */
@media (max-width: 768px) {
  /* 页面标题优化 */
  .page-header {
    margin-bottom: 20px !important;
    padding: 15px 0 !important;
  }

  .page-header h1 {
    font-size: 24px !important;
    margin-bottom: 10px !important;
  }

  .page-header .breadcrumb {
    font-size: 14px !important;
    padding: 8px 0 !important;
    margin-bottom: 0 !important;
  }

  /* 操作栏优化 */
  .action-bar {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
    margin-bottom: 20px !important;
  }

  .action-bar .btn-group {
    width: 100% !important;
  }

  .action-bar .btn {
    width: 100% !important;
    justify-content: center !important;
  }
}

/* === 移动端通知优化 === */
@media (max-width: 768px) {
  .alert {
    margin-bottom: 15px !important;
    padding: 12px 15px !important;
    font-size: 14px !important;
    border-radius: 8px !important;
  }

  .alert .close {
    padding: 12px 15px !important;
    font-size: 18px !important;
  }

  /* Toast通知移动端优化 */
  .toast-top-right {
    top: 20px !important;
    right: 15px !important;
    left: 15px !important;
    width: auto !important;
  }

  .toast {
    font-size: 14px !important;
    border-radius: 8px !important;
  }
}

/* === 移动端卡片视图优化 === */
@media (max-width: 768px) {
  /* 移动端卡片样式 */
  .card.border-left-primary { border-left: 4px solid #007bff; }
  .card.border-left-success { border-left: 4px solid #28a745; }
  .card.border-left-warning { border-left: 4px solid #ffc107; }
  .card.border-left-danger { border-left: 4px solid #dc3545; }
  .card.border-left-info { border-left: 4px solid #17a2b8; }
  .card.border-left-secondary { border-left: 4px solid #6c757d; }

  /* 移动端按钮组优化 */
  .btn-group.w-100 .btn {
    flex: 1;
    font-size: 12px;
    padding: 8px 4px;
  }

  /* 移动端操作按钮 */
  .action-buttons .btn {
    margin-bottom: 8px;
  }

  /* 移动端表单优化 */
  .form-group {
    margin-bottom: 1rem;
  }

  /* 移动端分页优化 */
  .pagination {
    flex-wrap: wrap;
  }

  .pagination .page-item {
    margin: 2px;
  }

  .pagination .page-link {
    padding: 8px 12px;
    font-size: 14px;
  }
}

/* === 小屏幕特殊优化 (320px-480px) === */
@media (max-width: 480px) {
  .container,
  .container-fluid {
    padding-left: 12px;
    padding-right: 12px;
  }

  .card {
    margin-bottom: 12px;
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: 12px;
  }

  .btn {
    min-height: 40px;
    padding: 10px 16px;
    font-size: 15px;
  }

  .form-control {
    min-height: 40px;
    padding: 10px 14px;
    font-size: 15px;
  }

  .table thead th,
  .table tbody td {
    padding: 8px 6px;
    font-size: 13px;
  }

  .modal-dialog {
    margin: 5px;
    max-width: calc(100vw - 10px);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 15px;
  }

  /* 超小屏幕导航优化 */
  .navbar-brand {
    font-size: 1rem;
  }

  .navbar-nav .nav-link {
    padding: 10px 14px;
    font-size: 15px;
  }

  .navbar-nav .dropdown-item {
    padding: 10px 28px;
    font-size: 14px;
  }

  /* 超小屏幕按钮组 */
  .btn-group.w-100 .btn {
    font-size: 10px;
    padding: 6px 2px;
  }

  /* 超小屏幕卡片 */
  .card-body {
    padding: 10px;
  }

  .card-title {
    font-size: 1rem;
  }
}

/* === 增强的移动端体验 === */
@media (max-width: 768px) {
  /* 食谱卡片特殊样式 */
  .recipe-card {
    overflow: hidden;
    transition: all 0.3s ease;
    border-radius: 12px;
  }

  .recipe-card:hover {
    transform: translateY(-2px);
    /* box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); */ /* 移除阴影效果 */
  }

  .recipe-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
  }

  .recipe-info {
    padding: 12px;
  }

  .recipe-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    line-height: 1.3;
  }

  .recipe-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.8rem;
    color: #6c757d;
  }

  .recipe-tags .badge {
    font-size: 0.7rem;
    margin-right: 4px;
    margin-bottom: 4px;
    padding: 2px 6px;
  }

  /* 筛选表单优化 */
  .filter-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
  }

  .filter-title {
    font-weight: 600;
    margin-bottom: 15px;
    color: #495057;
  }

  /* 分页优化 */
  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }

  .pagination .page-item {
    margin: 2px;
  }

  .pagination .page-link {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 6px;
  }

  /* 模态框优化 */
  .modal-dialog {
    margin: 10px;
    max-width: calc(100vw - 20px);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 15px;
  }

  .modal-title {
    font-size: 1.1rem;
  }

  /* 徽章优化 */
  .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 6px;
  }

  /* 表单组优化 */
  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #495057;
  }

  /* 工具栏优化 */
  .compact-toolbar {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
  }

  /* 状态指示器 */
  .status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
  }

  .status-indicator i {
    font-size: 0.8rem;
  }
}

/* === 移动端数字输入步进器样式 === */
@media (max-width: 768px) {
  .mobile-number-stepper {
    display: flex;
    align-items: center;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
  }

  .mobile-number-stepper input {
    border: none;
    text-align: center;
    flex: 1;
    min-height: 40px;
  }

  .mobile-number-stepper input:focus {
    /* box-shadow: none; */ /* 移除阴影效果 */
    border: none;
  }

  .mobile-number-stepper .btn {
    border: none;
    border-radius: 0;
    min-height: 40px;
    width: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-number-stepper .btn:first-child {
    border-right: 1px solid #e9ecef;
  }

  .mobile-number-stepper .btn:last-child {
    border-left: 1px solid #e9ecef;
  }
}

/* === 移动端选择器搜索样式 === */
@media (max-width: 768px) {
  .mobile-select-wrapper {
    position: relative;
  }

  .mobile-select-search {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: none;
  }

  .mobile-select-wrapper select {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    max-height: 200px;
    overflow-y: auto;
  }
}

/* === 移动端焦点状态增强 === */
@media (max-width: 768px) {
  .form-group.focused {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
  }

  .form-group.focused .form-label {
    color: var(--theme-primary);
    font-weight: 600;
  }

  .form-group.focused .form-control {
    border-color: var(--theme-primary);
    /* box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.25); */ /* 移除阴影效果 */
  }
}

/* === 移动端加载状态 === */
@media (max-width: 768px) {
  .mobile-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  }

  .mobile-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--theme-primary);
    border-radius: 50%;
    animation: mobile-spin 1s linear infinite;
  }

  @keyframes mobile-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .mobile-loading-text {
    margin-top: 16px;
    font-size: 16px;
    color: #6c757d;
  }
}

/* === 移动端错误状态 === */
@media (max-width: 768px) {
  .mobile-error-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    padding: 20px;
  }

  .mobile-error-icon {
    font-size: 64px;
    color: #dc3545;
    margin-bottom: 20px;
  }

  .mobile-error-title {
    font-size: 24px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
  }

  .mobile-error-message {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 24px;
    line-height: 1.5;
  }

  .mobile-error-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
    max-width: 300px;
  }
}

/* === 移动端空状态 === */
@media (max-width: 768px) {
  .mobile-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 40vh;
    text-align: center;
    padding: 20px;
  }

  .mobile-empty-icon {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 16px;
  }

  .mobile-empty-title {
    font-size: 18px;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 8px;
  }

  .mobile-empty-message {
    font-size: 14px;
    color: #adb5bd;
    margin-bottom: 20px;
    line-height: 1.5;
  }

  .mobile-empty-action {
    width: 100%;
    max-width: 200px;
  }
}

/* === 移动端成功状态 === */
@media (max-width: 768px) {
  .mobile-success-message {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    margin: 20px 0;
    /* box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3); */ /* 移除阴影效果 */
  }

  .mobile-success-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .mobile-success-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .mobile-success-text {
    font-size: 14px;
    opacity: 0.9;
  }
}

/* === 移动端底部固定操作栏 === */
@media (max-width: 768px) {
  .mobile-bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 12px 16px;
    /* box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
    z-index: 1000;
  }

  .mobile-bottom-actions .btn {
    width: 100%;
    margin-bottom: 8px;
  }

  .mobile-bottom-actions .btn:last-child {
    margin-bottom: 0;
  }

  /* 为有底部操作栏的页面添加底部间距 */
  .has-bottom-actions {
    padding-bottom: 80px;
  }
}

/* === 移动端侧边栏 === */
@media (max-width: 768px) {
  .mobile-sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    height: 100vh;
    background: white;
    /* box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
    transition: left 0.3s ease;
    z-index: 1050;
    overflow-y: auto;
  }

  .mobile-sidebar.show {
    left: 0;
  }

  .mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .mobile-sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  .mobile-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mobile-sidebar-title {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
  }

  .mobile-sidebar-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-sidebar-body {
    padding: 20px;
  }
}
