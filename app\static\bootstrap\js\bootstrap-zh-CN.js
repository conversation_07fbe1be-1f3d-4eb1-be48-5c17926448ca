/*!
 * Bootstrap v5.3.6 中文本地化 (https://getbootstrap.com/)
 * 兼容Bootstrap 5的中文本地化文件
 * 移除jQuery依赖，使用原生JavaScript
 */
(function () {
  'use strict';

  // 检查Bootstrap是否已加载
  if (typeof bootstrap === 'undefined') {
    console.warn('Bootstrap 5 未加载，中文本地化将在Bootstrap加载后应用');
    return;
  }

  // Bootstrap 5 中文本地化配置
  const zhCNLocale = {
    // 工具提示本地化
    tooltip: {
      title: '提示',
      placement: 'top',
      trigger: 'hover focus',
      delay: { show: 500, hide: 100 }
    },

    // 弹出框本地化
    popover: {
      title: '标题',
      content: '内容',
      placement: 'right',
      trigger: 'click'
    },

    // 模态框本地化
    modal: {
      backdrop: true,
      keyboard: true,
      focus: true
    },

    // 警告框本地化
    alert: {
      close: '关闭',
      dismiss: '忽略'
    },

    // 下拉菜单本地化
    dropdown: {
      toggle: '切换下拉菜单',
      autoClose: true,
      boundary: 'clippingParents'
    },

    // 轮播图本地化
    carousel: {
      interval: 5000,
      keyboard: true,
      pause: 'hover',
      ride: false,
      wrap: true,
      touch: true
    },

    // 折叠面板本地化
    collapse: {
      parent: null,
      toggle: true
    },

    // 表单验证消息本地化
    validation: {
      required: '此字段为必填项',
      email: '请输入有效的电子邮件地址',
      url: '请输入有效的URL',
      number: '请输入有效的数字',
      digits: '只能输入数字',
      minlength: '至少输入 {0} 个字符',
      maxlength: '最多输入 {0} 个字符',
      min: '请输入不小于 {0} 的值',
      max: '请输入不大于 {0} 的值',
      range: '请输入 {0} 到 {1} 之间的值',
      pattern: '请输入匹配的格式',
      date: '请输入有效的日期',
      time: '请输入有效的时间',
      datetime: '请输入有效的日期时间'
    }
  };
  // 应用中文本地化到Bootstrap组件
  function applyChineseLocalization() {
    // 更新工具提示的默认配置
    if (bootstrap.Tooltip) {
      const originalTooltipDefaults = bootstrap.Tooltip.Default;
      Object.assign(originalTooltipDefaults, zhCNLocale.tooltip);
    }

    // 更新弹出框的默认配置
    if (bootstrap.Popover) {
      const originalPopoverDefaults = bootstrap.Popover.Default;
      Object.assign(originalPopoverDefaults, zhCNLocale.popover);
    }

    // 更新模态框的默认配置
    if (bootstrap.Modal) {
      const originalModalDefaults = bootstrap.Modal.Default;
      Object.assign(originalModalDefaults, zhCNLocale.modal);
    }

    // 更新下拉菜单的默认配置
    if (bootstrap.Dropdown) {
      const originalDropdownDefaults = bootstrap.Dropdown.Default;
      Object.assign(originalDropdownDefaults, zhCNLocale.dropdown);
    }

    // 更新轮播图的默认配置
    if (bootstrap.Carousel) {
      const originalCarouselDefaults = bootstrap.Carousel.Default;
      Object.assign(originalCarouselDefaults, zhCNLocale.carousel);
    }

    // 更新折叠面板的默认配置
    if (bootstrap.Collapse) {
      const originalCollapseDefaults = bootstrap.Collapse.Default;
      Object.assign(originalCollapseDefaults, zhCNLocale.collapse);
    }
  }

  // 表单验证中文化函数
  function localizeFormValidation() {
    // 查找所有需要验证的表单
    const forms = document.querySelectorAll('.needs-validation, form[data-validate]');

    forms.forEach(form => {
      const inputs = form.querySelectorAll('input, select, textarea');

      inputs.forEach(input => {
        // 设置中文验证消息
        if (input.hasAttribute('required')) {
          input.setCustomValidity('');
          input.addEventListener('invalid', function() {
            if (this.validity.valueMissing) {
              this.setCustomValidity(zhCNLocale.validation.required);
            }
          });
        }

        if (input.type === 'email') {
          input.addEventListener('invalid', function() {
            if (this.validity.typeMismatch) {
              this.setCustomValidity(zhCNLocale.validation.email);
            }
          });
        }

        if (input.type === 'url') {
          input.addEventListener('invalid', function() {
            if (this.validity.typeMismatch) {
              this.setCustomValidity(zhCNLocale.validation.url);
            }
          });
        }

        if (input.type === 'number') {
          input.addEventListener('invalid', function() {
            if (this.validity.badInput) {
              this.setCustomValidity(zhCNLocale.validation.number);
            }
          });
        }

        // 清除自定义验证消息当输入有效时
        input.addEventListener('input', function() {
          this.setCustomValidity('');
        });
      });
    });
  }

  // 添加中文化的工具函数
  window.BootstrapZhCN = {
    // 获取本地化文本
    getText: function(key, params) {
      const keys = key.split('.');
      let value = zhCNLocale;

      for (const k of keys) {
        value = value[k];
        if (value === undefined) return key;
      }

      // 支持参数替换
      if (params && Array.isArray(params)) {
        params.forEach((param, index) => {
          value = value.replace(`{${index}}`, param);
        });
      }

      return value;
    },

    // 手动应用本地化
    apply: applyChineseLocalization,

    // 本地化表单验证
    localizeValidation: localizeFormValidation,

    // 获取完整的本地化配置
    getLocale: function() {
      return zhCNLocale;
    }
  };

  // 自动应用本地化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      applyChineseLocalization();
      localizeFormValidation();
    });
  } else {
    applyChineseLocalization();
    localizeFormValidation();
  }

  // 监听动态添加的元素
  if (window.MutationObserver) {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // 延迟执行以确保新元素完全加载
          setTimeout(localizeFormValidation, 100);
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  console.log('Bootstrap 5 中文本地化已加载');

})();
