{% macro render_field(field) %}
  <div class="mb-3">
    {{ field.label }}
    {{ field(**kwargs)|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_checkbox(field) %}
  <div class="mb-3 form-check">
    {{ field(class="form-check-input")|safe }}
    {{ field.label(class="form-check-label") }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_radio(field) %}
  <div class="mb-3">
    {{ field.label }}
    <div>
      {% for subfield in field %}
        <div class="form-check form-check-inline">
          {{ subfield(class="form-check-input")|safe }}
          {{ subfield.label(class="form-check-label") }}
        </div>
      {% endfor %}
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_select(field) %}
  <div class="mb-3">
    {{ field.label }}
    {{ field(class="form-control")|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_textarea(field) %}
  <div class="mb-3">
    {{ field.label }}
    {{ field(class="form-control", rows=5)|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_date(field) %}
  <div class="mb-3">
    {{ field.label }}
    <div class="input-group date" id="{{ field.id }}_datepicker">
      {{ field(class="form-control datepicker")|safe }}
      <div >
        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
      </div>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_datetime(field) %}
  <div class="mb-3">
    {{ field.label }}
    <div class="input-group date" id="{{ field.id }}_datetimepicker">
      {{ field(class="form-control datetimepicker")|safe }}
      <div >
        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
      </div>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_file(field) %}
  <div class="mb-3">
    {{ field.label }}
    <div class="form-control">
      {{ field(class="form-control-input")|safe }}
      <label class="form-control-label" for="{{ field.id }}">选择文件</label>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}
