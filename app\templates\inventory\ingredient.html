{% extends 'base.html' %}

{% block title %}食材库存详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">🥬 食材库存详情</h5>
                    <div class="card-tools">
                        <a href="{{ url_for('inventory.index', view_type='summary') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回汇总
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">食材名称</th>
                                    <td>{{ ingredient.name }}</td>
                                </tr>
                                <tr>
                                    <th>食材类别</th>
                                    <td>{{ ingredient.category }}</td>
                                </tr>
                                <tr>
                                    <th>单位</th>
                                    <td>{{ ingredient.unit }}</td>
                                </tr>
                                <tr>
                                    <th>总库存</th>
                                    <td>{{ total_quantity }} {{ ingredient.unit }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">库存预警设置</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>区域</th>
                                                <th>预警类型</th>
                                                <th>预警阈值</th>
                                                <th>预警级别</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for alert in alerts %}
                                            <tr>
                                                <td>{{ alert.area.name }}</td>
                                                <td>{{ alert.alert_type }}</td>
                                                <td>
                                                    {% if alert.alert_type == '库存不足' %}
                                                    {{ alert.min_quantity }} {{ ingredient.unit }}
                                                    {% elif alert.alert_type == '临期预警' %}
                                                    {{ alert.expiry_days }} 天
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if alert.alert_level == '紧急' %}
                                                    <span class="badge bg-danger">紧急</span>
                                                    {% elif alert.alert_level == '警告' %}
                                                    <span class="badge bg-warning">警告</span>
                                                    {% elif alert.alert_level == '提醒' %}
                                                    <span class="badge bg-info">提醒</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% else %}
                                            <tr>
                                                <td colspan="4" class="text-center">暂无预警设置</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 库存批次列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">库存批次列表</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>仓库</th>
                                            <th>存储位置</th>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>生产日期</th>
                                            <th>过期日期</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inventory in inventories %}
                                        <tr>
                                            <td>{{ inventory.warehouse.name }}</td>
                                            <td>{{ inventory.storage_location.name }} ({{ inventory.storage_location.location_code }})</td>
                                            <td>{{ inventory.batch_number }}</td>
                                            <td>{{ inventory.quantity }} {{ inventory.unit }}</td>
                                            <td>{{  inventory.production_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td>
                                                {{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}
                                                {% if inventory.status == '已过期' %}
                                                    <span class="badge bg-danger">已过期</span>
                                                {% elif inventory.status == '临期' %}
                                                    <span class="badge bg-warning">临期</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if inventory.status == '正常' %}
                                                <span class="badge bg-success">正常</span>
                                                {% elif inventory.status == '待检' %}
                                                <span class="badge bg-warning">待检</span>
                                                {% elif inventory.status == '冻结' %}
                                                <span class="badge bg-info">冻结</span>
                                                {% elif inventory.status == '已过期' %}
                                                <span class="badge bg-danger">已过期</span>
                                                {% elif inventory.status == '已用完' %}
                                                <span class="badge bg-secondary">已用完</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('inventory.detail', id=inventory.id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看
                                                </a>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="8" class="text-center">暂无库存数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
