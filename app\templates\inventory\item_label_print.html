<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>库存标签打印</title>
    <style nonce="{{ csp_nonce }}">
        @d-flex print {
            @page {
                size: A4;
                margin: 10mm;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: Sim<PERSON><PERSON>, "Microsoft YaHei", sans-serif;
                font-size: 12px;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-after: always;
            }
        }

        body {
            font-family: SimSun, "Microsoft YaHei", sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #000;
            background: #fff;
            margin: 0;
            padding: 20px;
        }

        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }

        .print-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 5px;
        }

        .label-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 0.5%;
        }

        .label-card {
            width: 60mm;
            height: 40mm;
            border: 1px solid #000;
            margin-bottom: 12px;
            padding: 6px;
            box-sizing: border-box;
            font-size: 11px;
            page-break-inside: avoid;
        }

        .label-header {
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
            margin-bottom: 8px;
        }

        .label-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .label-category {
            font-size: 9px;
            color: #555;
            font-style: italic;
        }

        .label-content {
            display: flex;
        }

        .label-info {
            flex: 1;
        }

        .label-info-row {
            margin-bottom: 2px;
            line-height: 1.2;
            font-size: 10px;
        }

        .label-qrcode {
            width: 50px;
            height: 50px;
            border: 1px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 3px;
            font-size: 8px;
        }

        .label-footer {
            margin-top: 5px;
            border-top: 1px solid #000;
            padding-top: 3px;
            display: flex;
            justify-content: space-between;
            font-size: 9px;
        }

        .signature {
            display: flex;
        }

        .signature-label {
            margin-right: 3px;
        }

        .signature-line {
            width: 40px;
            border-bottom: 1px solid #000;
        }

        .no-print {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .no-print:hover {
            background: #0056b3;
        }

        .print-footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #000;
            text-align: center;
            font-size: 10px;
        }
    
        @d-flex print {
            @page {
                size: A4;
                margin: 10mm;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: SimSun, "Microsoft YaHei", sans-serif;
                font-size: 12px;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-after: always;
            }
        }

        body {
            font-family: SimSun, "Microsoft YaHei", sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #000;
            background: #fff;
            margin: 0;
            padding: 20px;
        }

        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }

        .print-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 5px;
        }

        .label-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 0.5%;
        }

        .label-card {
            width: 60mm;
            height: 40mm;
            border: 1px solid #000;
            margin-bottom: 12px;
            padding: 6px;
            box-sizing: border-box;
            font-size: 11px;
            page-break-inside: avoid;
        }

        .label-header {
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
            margin-bottom: 8px;
        }

        .label-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .label-category {
            font-size: 9px;
            color: #555;
            font-style: italic;
        }

        .label-content {
            display: flex;
        }

        .label-info {
            flex: 1;
        }

        .label-info-row {
            margin-bottom: 2px;
            line-height: 1.2;
            font-size: 10px;
        }

        .label-qrcode {
            width: 50px;
            height: 50px;
            border: 1px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 3px;
            font-size: 8px;
        }

        .label-footer {
            margin-top: 5px;
            border-top: 1px solid #000;
            padding-top: 3px;
            display: flex;
            justify-content: space-between;
            font-size: 9px;
        }

        .signature {
            display: flex;
        }

        .signature-label {
            margin-right: 3px;
        }

        .signature-line {
            width: 40px;
            border-bottom: 1px solid #000;
        }

        .no-print {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .no-print:hover {
            background: #0056b3;
        }

        .print-footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #000;
            text-align: center;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <button class="no-print" id="printBtn">打印标签</button>

    <div class="print-header">
        <h1 class="print-title">{{ project_name }} - 库存食材标签</h1>
    </div>

    <div class="label-grid">
        <div class="label-card">
            <div class="label-header">
                <div class="label-title">{{ inventory.ingredient.name }}</div>
                {% if inventory.ingredient.category %}
                <div class="label-category">{{ inventory.ingredient.category.name }}</div>
                {% endif %}
            </div>
            <div class="label-content">
                <div class="label-info">
                    <div class="label-info-row">
                        <strong>批次号：</strong>{{ inventory.batch_number }}
                    </div>
                    <div class="label-info-row">
                        <strong>数量：</strong>{{ inventory.quantity }}{{ inventory.unit }}
                    </div>
                    <div class="label-info-row">
                        <strong>仓库：</strong>{{ inventory.warehouse.name }}
                    </div>
                    <div class="label-info-row">
                        <strong>位置：</strong>{{ inventory.storage_location.name }}
                    </div>
                    <div class="label-info-row">
                        <strong>生产：</strong>{{ inventory.production_date.strftime('%Y-%m-%d') if inventory.production_date else '-' }}
                    </div>
                    <div class="label-info-row">
                        <strong>过期：</strong>{{ inventory.expiry_date.strftime('%Y-%m-%d') if inventory.expiry_date else '-' }}
                    </div>
                </div>
                <div class="label-qrcode">
                    {% if inventory.qr_code %}
                    <img src="data:image/png;base64,{{ inventory.qr_code }}" alt="溯源二维码" style="max-width: 100%; max-height: 100%;">
                    {% else %}
                    <div style="text-align: center; font-size: 8px; color: #666;">
                        溯源<br>二维码
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="label-footer">
                <div class="signature">
                    <div class="signature-label">管理员：</div>
                    <div class="signature-line"></div>
                </div>
                <div class="signature">
                    <div class="signature-label">日期：</div>
                    <div class="signature-line"></div>
                </div>
            </div>
        </div>

        <!-- 可以复制多个标签 -->
        <div class="label-card">
            <div class="label-header">
                <div class="label-title">{{ inventory.ingredient.name }}</div>
                {% if inventory.ingredient.category %}
                <div class="label-category">{{ inventory.ingredient.category.name }}</div>
                {% endif %}
            </div>
            <div class="label-content">
                <div class="label-info">
                    <div class="label-info-row">
                        <strong>批次号：</strong>{{ inventory.batch_number }}
                    </div>
                    <div class="label-info-row">
                        <strong>数量：</strong>{{ inventory.quantity }}{{ inventory.unit }}
                    </div>
                    <div class="label-info-row">
                        <strong>仓库：</strong>{{ inventory.warehouse.name }}
                    </div>
                    <div class="label-info-row">
                        <strong>位置：</strong>{{ inventory.storage_location.name }}
                    </div>
                    <div class="label-info-row">
                        <strong>生产：</strong>{{ inventory.production_date.strftime('%Y-%m-%d') if inventory.production_date else '-' }}
                    </div>
                    <div class="label-info-row">
                        <strong>过期：</strong>{{ inventory.expiry_date.strftime('%Y-%m-%d') if inventory.expiry_date else '-' }}
                    </div>
                </div>
                <div class="label-qrcode">
                    {% if inventory.qr_code %}
                    <img src="data:image/png;base64,{{ inventory.qr_code }}" alt="溯源二维码" style="max-width: 100%; max-height: 100%;">
                    {% else %}
                    <div style="text-align: center; font-size: 8px; color: #666;">
                        溯源<br>二维码
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="label-footer">
                <div class="signature">
                    <div class="signature-label">管理员：</div>
                    <div class="signature-line"></div>
                </div>
                <div class="signature">
                    <div class="signature-label">日期：</div>
                    <div class="signature-line"></div>
                </div>
            </div>
        </div>

        <!-- 第三个标签 -->
        <div class="label-card">
            <div class="label-header">
                <div class="label-title">{{ inventory.ingredient.name }}</div>
                {% if inventory.ingredient.category %}
                <div class="label-category">{{ inventory.ingredient.category.name }}</div>
                {% endif %}
            </div>
            <div class="label-content">
                <div class="label-info">
                    <div class="label-info-row">
                        <strong>批次号：</strong>{{ inventory.batch_number }}
                    </div>
                    <div class="label-info-row">
                        <strong>数量：</strong>{{ inventory.quantity }}{{ inventory.unit }}
                    </div>
                    <div class="label-info-row">
                        <strong>仓库：</strong>{{ inventory.warehouse.name }}
                    </div>
                    <div class="label-info-row">
                        <strong>位置：</strong>{{ inventory.storage_location.name }}
                    </div>
                    <div class="label-info-row">
                        <strong>生产：</strong>{{ inventory.production_date.strftime('%Y-%m-%d') if inventory.production_date else '-' }}
                    </div>
                    <div class="label-info-row">
                        <strong>过期：</strong>{{ inventory.expiry_date.strftime('%Y-%m-%d') if inventory.expiry_date else '-' }}
                    </div>
                </div>
                <div class="label-qrcode">
                    {% if inventory.qr_code %}
                    <img src="data:image/png;base64,{{ inventory.qr_code }}" alt="溯源二维码" style="max-width: 100%; max-height: 100%;">
                    {% else %}
                    <div style="text-align: center; font-size: 8px; color: #666;">
                        溯源<br>二维码
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="label-footer">
                <div class="signature">
                    <div class="signature-label">管理员：</div>
                    <div class="signature-line"></div>
                </div>
                <div class="signature">
                    <div class="signature-label">日期：</div>
                    <div class="signature-line"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="print-footer">
        <p>此标签由校园餐智慧食堂平台生成，确保库存食材安全可追溯</p>
    </div>

    <script nonce="{{ csp_nonce }}">
        window.onload = function() {
            // 自动聚焦
            window.focus();

            // 绑定打印按钮事件
            const printBtn = document.getElementById('printBtn');
            if (printBtn) {
                printBtn.addEventListener('click', function() {
                    window.print();
                });
            }
        };
    </script>
</body>
</html>
