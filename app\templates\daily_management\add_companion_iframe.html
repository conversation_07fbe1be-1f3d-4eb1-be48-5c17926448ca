<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>添加陪餐记录</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome-free/css/all.min.css') }}">
    <!-- Custom styles -->
    <style nonce="{{ csp_nonce }}">
        body {
            padding: 15px;
            background-color: #f8f9fc;
        }
        .photo-preview {
            max-width: 100%;
            max-height: 200px;
            margin-top: 10px;
        }
        .rating-container {
            display: flex;
            flex-direction: row;
        }
        .rating-star {
            font-size: 24px;
            color: #ccc;
            cursor: pointer;
            margin-right: 5px;
        }
        .rating-star.active {
            color: #f8ce0b;
        }
        .mb-3 {
            margin-bottom: 1rem;
        }
    
        body {
            padding: 15px;
            background-color: #f8f9fc;
        }
        .photo-preview {
            max-width: 100%;
            max-height: 200px;
            margin-top: 10px;
        }
        .rating-container {
            display: flex;
            flex-direction: row;
        }
        .rating-star {
            font-size: 24px;
            color: #ccc;
            cursor: pointer;
            margin-right: 5px;
        }
        .rating-star.active {
            color: #f8ce0b;
        }
        .mb-3 {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <form method="post" enctype="multipart/form-data" target="_top" novalidate novalidate>
            {{ form.csrf_token }}
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="companion_name">陪餐人姓名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="companion_name" name="companion_name" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="companion_role">陪餐人角色 <span class="text-danger">*</span></label>
                        <select class="form-control" id="companion_role" name="companion_role" required>
                            <option value="">请选择角色</option>
                            <option value="校长">校长</option>
                            <option value="副校长">副校长</option>
                            <option value="主任">主任</option>
                            <option value="教师">教师</option>
                            <option value="家长">家长</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="meal_type">餐次 <span class="text-danger">*</span></label>
                        <select class="form-control" id="meal_type" name="meal_type" required>
                            <option value="">请选择餐次</option>
                            <option value="breakfast">早餐</option>
                            <option value="lunch">午餐</option>
                            <option value="dinner">晚餐</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="dining_date">陪餐日期 <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="dining_date" name="dining_date" value="{{ log.log_date|format_datetime('%Y-%m-%d') }}" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="dining_time">陪餐时间 <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="dining_time" name="dining_time" value="12:00" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label>口味评分</label>
                        <div class="rating-container" id="taste-rating">
                            <span class="rating-star" data-value="1">★</span>
                            <span class="rating-star" data-value="2">★</span>
                            <span class="rating-star" data-value="3">★</span>
                            <span class="rating-star" data-value="4">★</span>
                            <span class="rating-star" data-value="5">★</span>
                        </div>
                        <input type="hidden" name="taste_rating" id="taste_rating_input" value="0">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label>卫生评分</label>
                        <div class="rating-container" id="hygiene-rating">
                            <span class="rating-star" data-value="1">★</span>
                            <span class="rating-star" data-value="2">★</span>
                            <span class="rating-star" data-value="3">★</span>
                            <span class="rating-star" data-value="4">★</span>
                            <span class="rating-star" data-value="5">★</span>
                        </div>
                        <input type="hidden" name="hygiene_rating" id="hygiene_rating_input" value="0">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label>服务评分</label>
                        <div class="rating-container" id="service-rating">
                            <span class="rating-star" data-value="1">★</span>
                            <span class="rating-star" data-value="2">★</span>
                            <span class="rating-star" data-value="3">★</span>
                            <span class="rating-star" data-value="4">★</span>
                            <span class="rating-star" data-value="5">★</span>
                        </div>
                        <input type="hidden" name="service_rating" id="service_rating_input" value="0">
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="comments">评价意见</label>
                <textarea class="form-control" id="comments" name="comments" rows="3"></textarea>
            </div>

            <div class="mb-3">
                <label for="suggestions">改进建议</label>
                <textarea class="form-control" id="suggestions" name="suggestions" rows="3"></textarea>
            </div>

            <div class="mb-3">
                <label for="photos">照片上传</label>
                <input type="file" class="form-control" id="photos" name="photos" multiple accept="image/*">
                <small class="form-text text-muted">可以选择多张照片上传，支持jpg、jpeg、png格式</small>
                <div id="photo-previews" class="mt-2 d-flex flex-wrap"></div>
            </div>

            <div class="mb-3 mt-4">
                <button type="submit" class="btn btn-primary">保存</button>
                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date) }}" class="btn btn-secondary" target="_top">取消</a>
            </div>
        </form>
    </div>

    <!-- jQuery -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- Bootstrap JS -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <script nonce="{{ csp_nonce }}">
        // 星级评分
        function setupRating(containerId, inputId) {
            const container = document.getElementById(containerId);
            const input = document.getElementById(inputId);
            const stars = container.querySelectorAll('.rating-star');

            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const value = parseInt(this.getAttribute('data-value'));
                    input.value = value;

                    // 更新星星显示
                    stars.forEach(s => {
                        if (parseInt(s.getAttribute('data-value')) <= value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });

                star.addEventListener('mouseover', function() {
                    const value = parseInt(this.getAttribute('data-value'));

                    // 临时更新星星显示
                    stars.forEach(s => {
                        if (parseInt(s.getAttribute('data-value')) <= value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });

                container.addEventListener('mouseout', function() {
                    const value = parseInt(input.value);

                    // 恢复星星显示
                    stars.forEach(s => {
                        if (parseInt(s.getAttribute('data-value')) <= value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
            });
        }

        // 照片预览
        document.getElementById('photos').addEventListener('change', function(e) {
            const previewsDiv = document.getElementById('photo-previews');
            previewsDiv.innerHTML = '';

            for (const file of this.files) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = document.createElement('img');
                    img.src = event.target.result;
                    img.className = 'photo-preview me-2 mb-2';
                    previewsDiv.appendChild(img);
                }
                reader.readAsDataURL(file);
            }
        });

        // 初始化星级评分
        setupRating('taste-rating', 'taste_rating_input');
        setupRating('hygiene-rating', 'hygiene_rating_input');
        setupRating('service-rating', 'service_rating_input');
    </script>
</body>
</html>
