<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bootstrap 5.3.6 兼容性测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .menu-input {
            min-height: 100px;
            cursor: pointer;
            border: 2px dashed #ddd;
            padding: 10px;
            margin: 5px;
            background-color: #f8f9fa;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .menu-input:hover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        .menu-input.readonly {
            background-color: #f0f0f0;
            cursor: not-allowed;
            opacity: 0.7;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">Bootstrap 5.3.6 周菜单兼容性测试</h1>
        
        <!-- 测试菜单输入框 -->
        <div class="test-section">
            <h3>菜单输入框测试</h3>
            <div class="row">
                <div class="col-md-4">
                    <h5>早餐</h5>
                    <div class="menu-input" data-date="2024-01-01" data-meal="早餐">
                        点击选择菜品
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>午餐</h5>
                    <div class="menu-input" data-date="2024-01-01" data-meal="午餐">
                        点击选择菜品
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>晚餐</h5>
                    <div class="menu-input" data-date="2024-01-01" data-meal="晚餐">
                        点击选择菜品
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试按钮 -->
        <div class="test-section">
            <h3>功能测试按钮</h3>
            <button type="button" class="btn btn-primary me-2" id="testModalBtn">
                测试模态框
            </button>
            <button type="button" class="btn btn-success me-2" id="testToastBtn">
                测试Toast通知
            </button>
            <button type="button" class="btn btn-info me-2" id="testConfirmBtn">
                测试确认对话框
            </button>
        </div>
        
        <!-- 日志输出 -->
        <div class="test-section">
            <h3>测试日志</h3>
            <div id="testLog" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>
    </div>

    <!-- 测试模态框 -->
    <div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="testModalLabel">测试模态框</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>这是一个Bootstrap 5.3.6兼容性测试模态框。</p>
                    <p>如果您能看到这个内容，说明模态框功能正常。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="testModalOkBtn">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
            logDiv.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 清空日志
        document.getElementById('testLog').innerHTML = '';
        log('测试开始...', 'info');

        // 测试菜单输入框点击
        document.querySelectorAll('.menu-input').forEach(input => {
            input.addEventListener('click', function() {
                const date = this.getAttribute('data-date');
                const meal = this.getAttribute('data-meal');
                log(`点击了菜单输入框: ${date} ${meal}`, 'success');
                
                // 测试模态框显示
                try {
                    const testModal = new bootstrap.Modal(document.getElementById('testModal'));
                    testModal.show();
                    log('模态框显示成功 (Bootstrap 5语法)', 'success');
                } catch (error) {
                    log('模态框显示失败: ' + error.message, 'error');
                }
            });
        });

        // 测试模态框按钮
        document.getElementById('testModalBtn').addEventListener('click', function() {
            log('测试模态框按钮被点击', 'info');
            try {
                const testModal = new bootstrap.Modal(document.getElementById('testModal'));
                testModal.show();
                log('模态框显示成功', 'success');
            } catch (error) {
                log('模态框显示失败: ' + error.message, 'error');
            }
        });

        // 测试Toast按钮
        document.getElementById('testToastBtn').addEventListener('click', function() {
            log('测试Toast按钮被点击', 'info');
            try {
                // 创建Toast元素
                const toastHtml = `
                    <div class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header">
                            <strong class="me-auto">测试通知</strong>
                            <small>刚刚</small>
                            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            这是一个Bootstrap 5.3.6兼容性测试Toast通知。
                        </div>
                    </div>
                `;
                
                // 创建Toast容器（如果不存在）
                let toastContainer = document.querySelector('.toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                    document.body.appendChild(toastContainer);
                }
                
                // 添加Toast
                toastContainer.innerHTML = toastHtml;
                const toastElement = toastContainer.querySelector('.toast');
                const toast = new bootstrap.Toast(toastElement);
                toast.show();
                
                log('Toast通知显示成功', 'success');
            } catch (error) {
                log('Toast通知显示失败: ' + error.message, 'error');
            }
        });

        // 测试确认对话框按钮
        document.getElementById('testConfirmBtn').addEventListener('click', function() {
            log('测试确认对话框按钮被点击', 'info');
            if (confirm('这是一个原生确认对话框，点击确定继续测试。')) {
                log('用户点击了确定', 'success');
            } else {
                log('用户点击了取消', 'info');
            }
        });

        // 测试模态框事件监听
        document.getElementById('testModal').addEventListener('shown.bs.modal', function() {
            log('模态框显示事件触发 (shown.bs.modal)', 'success');
        });

        document.getElementById('testModal').addEventListener('hidden.bs.modal', function() {
            log('模态框隐藏事件触发 (hidden.bs.modal)', 'success');
        });

        // 测试模态框确定按钮
        document.getElementById('testModalOkBtn').addEventListener('click', function() {
            log('模态框确定按钮被点击', 'info');
            const testModal = bootstrap.Modal.getInstance(document.getElementById('testModal'));
            if (testModal) {
                testModal.hide();
                log('模态框关闭成功', 'success');
            }
        });

        log('所有事件监听器已绑定', 'success');
        log('请点击上方的菜单输入框或按钮进行测试', 'info');
    </script>
</body>
</html>
