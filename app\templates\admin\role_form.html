{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system.roles') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回角色列表
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" novalidate novalidate novalidate>            {{ form.hidden_tag() }}

            <div class="mb-3">
                {{ form.name.label }}
                {{ form.name(class="form-control") }}
                {% for error in form.name.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>

            <div class="mb-3">
                {{ form.description.label }}
                {{ form.description(class="form-control") }}
                {% for error in form.description.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <label for="{{ form.permissions.id }}">{{ form.permissions.label.text }}</label>
                    <a href="{{ url_for('system.permission_help') }}" target="_blank" class="btn btn-sm btn-info">
                        <i class="fas fa-question-circle"></i> 权限配置帮助
                    </a>
                </div>
                {{ form.permissions(class="form-control", rows=10) }}
                {% for error in form.permissions.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
                <div class="card mt-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">权限配置说明</h6>
                    </div>
                    <div class="card-body">
                        <p>权限配置使用JSON格式，基本结构如下：</p>
                        <pre class="bg-light p-2 rounded">
{
  "模块名1": ["操作1", "操作2", ...],
  "模块名2": ["操作1", "操作2", ...],
  ...
}</pre>
                        <p><strong>特殊符号：</strong></p>
                        <ul>
                            <li><code>"*"</code> 作为模块名：表示所有模块</li>
                            <li><code>"*"</code> 作为操作：表示该模块的所有操作</li>
                        </ul>

                        <p><strong>常见示例：</strong></p>
                        <div class="accordion" id="permissionExamples">
                            <div class="card">
                                <div class="card-header p-0" id="headingOne">
                                    <button class="btn btn-link w-100 text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                        系统管理员（全部权限）
                                    </button>
                                </div>
                                <div id="collapseOne" class="collapse" aria-labelledby="headingOne" data-bs-parent="#permissionExamples">
                                    <div class="card-body">
                                        <pre class="bg-light p-2 rounded">{"*": ["*"]}</pre>
                                    </div>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-header p-0" id="headingTwo">
                                    <button class="btn btn-link w-100 text-start collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                        食堂管理员
                                    </button>
                                </div>
                                <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-bs-parent="#permissionExamples">
                                    <div class="card-body">
                                        <pre class="bg-light p-2 rounded">{
  "area": ["view"],
  "supplier": ["view"],
  "ingredient": ["view", "create", "edit"],
  "menu": ["view", "create", "edit", "delete"],
  "sample": ["view", "create", "edit", "delete"],
  "report": ["view", "print"]
}</pre>
                                    </div>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-header p-0" id="headingThree">
                                    <button class="btn btn-link w-100 text-start collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                        只读用户
                                    </button>
                                </div>
                                <div id="collapseThree" class="collapse" aria-labelledby="headingThree" data-bs-parent="#permissionExamples">
                                    <div class="card-body">
                                        <pre class="bg-light p-2 rounded">{
  "user": ["view"],
  "role": ["view"],
  "area": ["view"],
  "supplier": ["view"],
  "ingredient": ["view"],
  "menu": ["view"],
  "sample": ["view"],
  "report": ["view"]
}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <p class="mb-0">
                                <i class="fas fa-info-circle"></i>
                                <a href="{{ url_for('system.permission_help') }}" target="_blank">点击这里</a>
                                查看完整的权限配置帮助文档，包括所有模块和操作的详细说明。
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3 text-center">
                <a href="{{ url_for('system.roles') }}" class="btn btn-secondary">取消</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
