{% extends 'base.html' %}

{% block title %}选择学校 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-12">
      <p class="text-muted">请选择一个学校进行采购订单创建</p>
    </div>
  </div>

  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">可访问的学校</h5>
    </div>
    <div class="card-body">
      <div class="row">
        {% for area in areas %}
        {% if area.level == 3 %}  <!-- 只显示学校级别的区域 -->
        <div class="col-md-4 mb-3">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">{{ area.name }}</h5>
              <p class="card-text text-muted">{{ area.get_level_name() }}</p>
              <a href="{{ url_for('purchase_order.create_from_menu', area_id=area.id) }}" class="btn btn-primary">
                <i class="fas fa-shopping-cart"></i> 创建采购订单
              </a>
            </div>
          </div>
        </div>
        {% endif %}
        {% else %}
        <div class="col-md-12">
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> 您没有可访问的学校，请联系管理员分配权限。
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
