{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print-styles.css') }}">
<style nonce="{{ csp_nonce }}">
    body {
        font-size: 14pt;
    }
    
    .section-title {
        font-size: 16pt;
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 10px;
        border-bottom: 1px solid #4e73df;
        padding-bottom: 5px;
        color: #4e73df;
    }
    
    .info-row {
        margin-bottom: 10px;
    }
    
    .info-label {
        font-weight: bold;
    }
    
    .print-page {
        padding: 20mm;
        margin-bottom: 20mm;
        border: 1px solid #ddd;
        background: #fff;
    }
    
    .rating-stars {
        color: #f6c23e;
        font-size: 1.2rem;
    }
    
    @d-flex print {
        .no-print {
            display: none !important;
        }
        
        body {
            font-size: 12pt;
        }
        
        .section-title {
            font-size: 14pt;
        }
        
        .print-page {
            padding: 0;
            margin: 0;
            border: none;
        }
    }

    body {
        font-size: 14pt;
    }
    
    .section-title {
        font-size: 16pt;
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 10px;
        border-bottom: 1px solid #4e73df;
        padding-bottom: 5px;
        color: #4e73df;
    }
    
    .info-row {
        margin-bottom: 10px;
    }
    
    .info-label {
        font-weight: bold;
    }
    
    .print-page {
        padding: 20mm;
        margin-bottom: 20mm;
        border: 1px solid #ddd;
        background: #fff;
    }
    
    .rating-stars {
        color: #f6c23e;
        font-size: 1.2rem;
    }
    
    @d-flex print {
        .no-print {
            display: none !important;
        }
        
        body {
            font-size: 12pt;
        }
        
        .section-title {
            font-size: 14pt;
        }
        
        .print-page {
            padding: 0;
            margin: 0;
            border: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 打印控制按钮 -->
    <div class="no-print mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 fw-bold text-primary">打印预览 - 陪餐记录</h6>
                <div>
                    <a href="{{ url_for('daily_management.companions', log_id=log.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> 返回陪餐记录
                    </a>
                    <button class="print-button" class="btn btn-primary btn-sm">
                        <i class="fas fa-print me-1"></i> 打印
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-1"></i> 打印预览模式。点击"打印"按钮开始打印，或使用浏览器的打印功能（Ctrl+P）。
                </div>
            </div>
        </div>
    </div>

    <!-- 打印内容 -->
    <div class="print-preview">
        <!-- 第一页：陪餐记录列表 -->
        <div class="print-page avoid-break">
            <div class="print-page-indicator no-print">第1页</div>
            
            <!-- 页眉 -->
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂陪餐记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <!-- 陪餐记录列表 -->
            <div class="section-title">陪餐记录列表</div>
            {% if companions %}
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="5%">序号</th>
                        <th width="15%">陪餐人</th>
                        <th width="15%">角色</th>
                        <th width="15%">餐次</th>
                        <th width="15%">陪餐时间</th>
                        <th width="10%">口味评分</th>
                        <th width="10%">卫生评分</th>
                        <th width="10%">服务评分</th>
                    </tr>
                </thead>
                <tbody>
                    {% for companion in companions %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ companion.companion_name }}</td>
                        <td>{{ companion.companion_role }}</td>
                        <td>{{ companion.meal_type }}</td>
                        <td>{{ companion.dining_time|safe_datetime('%H:%M') }}</td>
                        <td>{{ companion.taste_rating or 0 }}</td>
                        <td>{{ companion.hygiene_rating or 0 }}</td>
                        <td>{{ companion.service_rating or 0 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p class="text-muted">暂无陪餐记录</p>
            {% endif %}
        </div>
        
        <!-- 陪餐记录详情页 -->
        {% for companion in companions %}
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">陪餐记录 {{ loop.index }}</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 陪餐记录详情</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">{{ companion.companion_name }} - {{ companion.meal_type }}</div>
            
            <div class="row info-row">
                <div class="col-md-4">
                    <span class="info-label">陪餐人：</span> {{ companion.companion_name }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">角色：</span> {{ companion.companion_role }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">陪餐时间：</span> {{ companion.dining_time|safe_datetime('%Y-%m-%d %H:%M') }}
                </div>
            </div>
            
            <div class="row info-row">
                <div class="col-md-4">
                    <span class="info-label">餐次：</span> {{ companion.meal_type }}
                </div>
                <div class="col-md-8">
                    <span class="info-label">评分：</span>
                    <div class="row mt-2">
                        <div class="col-md-4">
                            <span class="info-label">口味：</span> 
                            <span class="rating-stars">
                                {% for i in range(companion.taste_rating|int) %}★{% endfor %}
                                {% for i in range(5 - companion.taste_rating|int) %}☆{% endfor %}
                            </span>
                        </div>
                        <div class="col-md-4">
                            <span class="info-label">卫生：</span> 
                            <span class="rating-stars">
                                {% for i in range(companion.hygiene_rating|int) %}★{% endfor %}
                                {% for i in range(5 - companion.hygiene_rating|int) %}☆{% endfor %}
                            </span>
                        </div>
                        <div class="col-md-4">
                            <span class="info-label">服务：</span> 
                            <span class="rating-stars">
                                {% for i in range(companion.service_rating|int) %}★{% endfor %}
                                {% for i in range(5 - companion.service_rating|int) %}☆{% endfor %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section-title">评价意见</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ companion.comments or '无' }}</p>
                </div>
            </div>
            
            <div class="section-title">改进建议</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ companion.suggestions or '无' }}</p>
                </div>
            </div>
            
            {% if companion.photo_paths %}
            <div class="section-title">陪餐照片</div>
            <div class="row">
                {% for path in companion.photo_paths.split(';') %}
                <div class="col-md-4 mb-3">
                    <img src="{{ path }}" alt="陪餐照片" class="img-fluid img-thumbnail">
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        <!-- 签名区域 -->
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">签名页</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂陪餐记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">签名确认</div>
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="text-center">
                        <p>陪餐人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>食堂负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>学校负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <p>（本文档由系统自动生成，打印后有效）</p>
            </div>
        </div>
    </div>
</div>

<!-- 打印控制按钮（固定在右下角） -->
<div class="print-controls no-print">
    <button class="print-button"><i class="fas fa-print me-1"></i> 打印</button>
    <button data-action="safe-navigate" data-navigate-code="window.location.href=" style="cursor: pointer;"{{ url_for('daily_management.companions', log_id=log.id) }}'"><i class="fas fa-times me-1"></i> 取消</button>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 打印预览初始化
        $('.print-page').each(function(index) {
            $(this).find('.print-page-indicator').text('第' + (index + 1) + '页');
        });
    });
</script>
{% endblock %}
