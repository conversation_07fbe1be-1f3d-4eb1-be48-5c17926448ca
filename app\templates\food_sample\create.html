{% extends 'base.html' %}

{% block title %}创建留样记录{% endblock %}

{% block styles %}
{{ super() }}
<link href="{{ url_for('static', filename='vendor/select2/css/select2.min.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/select2/css/select2-bootstrap4.min.css') }}" rel="stylesheet" />
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/tempusdominus/css/tempusdominus-bootstrap-4.min.css') }}" />
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-tools">
                        <a href="{{ url_for('food_sample.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('food_sample.create') }}" enctype="multipart/form-data" novalidate novalidate><div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="area_id">区域 <span class="text-danger">*</span></label>
                                    <select name="area_id" id="area_id" class="form-control select2" required>
                                        <option value="">请选择区域</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}">{{ area.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="menu_plan_id">菜单计划</label>
                                    <select name="menu_plan_id" id="menu_plan_id" class="form-control select2">
                                        <option value="">请选择菜单计划</option>
                                        {% for menu_plan in menu_plans %}
                                        <option value="{{ menu_plan.id }}" data-area="{{ menu_plan.area_id }}" data-date="{{ menu_plan.plan_date }}" data-meal="{{ menu_plan.meal_type }}">
                                            {{ menu_plan.plan_date }} {{ menu_plan.meal_type }} - {{ menu_plan.area.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="meal_date">用餐日期 <span class="text-danger">*</span></label>
                                    <input type="date" name="meal_date" id="meal_date" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="meal_type">餐次 <span class="text-danger">*</span></label>
                                    <select name="meal_type" id="meal_type" class="form-control" required>
                                        <option value="">请选择餐次</option>
                                        <option value="早餐">早餐</option>
                                        <option value="午餐">午餐</option>
                                        <option value="晚餐">晚餐</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="recipe_id">食谱 <span class="text-danger">*</span></label>
                                    <select name="recipe_id" id="recipe_id" class="form-control select2" required>
                                        <option value="">请选择食谱</option>
                                        {% for recipe in recipes %}
                                        <option value="{{ recipe.id }}">{{ recipe.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sample_quantity">留样数量</label>
                                    <input type="number" name="sample_quantity" id="sample_quantity" class="form-control" min="0.1" step="0.1">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sample_unit">留样单位</label>
                                    <select name="sample_unit" id="sample_unit" class="form-control">
                                        <option value="">请选择单位</option>
                                        <option value="克">克</option>
                                        <option value="千克">千克</option>
                                        <option value="份">份</option>
                                        <option value="盒">盒</option>
                                        <option value="袋">袋</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="storage_temperature">存储温度</label>
                                    <input type="text" name="storage_temperature" id="storage_temperature" class="form-control" placeholder="例如：-18℃">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="storage_location">存储位置 <span class="text-danger">*</span></label>
                                    <input type="text" name="storage_location" id="storage_location" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_time">留样时间 <span class="text-danger">*</span></label>
                                    <div class="input-group date" id="startTimePicker" data-target-input="nearest">
                                        <input type="text" name="start_time" id="start_time" class="form-control datetimepicker-input" data-bs-target="#startTimePicker" required>
                                        <div  data-bs-target="#startTimePicker" data-bs-toggle="datetimepicker">
                                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="sample_image">留样图片</label>
                            <div class="input-group">
                                <div class="form-control">
                                    <input type="file" name="sample_image" id="sample_image" class="form-control-input" accept="image/*">
                                    <label class="form-control-label" for="sample_image">选择图片</label>
                                </div>
                            </div>
                        </div>
                        <div id="imagePreview" class="mt-2" style="display: none;">
                            <img src="" alt="预览图" style="max-height: 200px; max-width: 100%;">
                        </div>

                        <div class="mb-3 text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <a href="{{ url_for('food_sample.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/tempusdominus/js/tempusdominus-bootstrap-4.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bs-form-control-input/bs-form-control-input.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化Select2
        $('.select2').select2({
            theme: 'bootstrap4'
        });

        // 初始化日期时间选择器
        $('#startTimePicker').datetimepicker({
            format: 'YYYY-MM-DD HH:mm',
            defaultDate: moment(),
            icons: {
                time: 'far fa-clock'
            }
        });

        // 初始化文件输入
        bsCustomFileInput.init();

        // 图片预览
        $('#sample_image').change(function() {
            if (this.files && this.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview img').attr('src', e.target.result);
                    $('#imagePreview').show();
                }
                reader.readAsDataURL(this.files[0]);
            } else {
                $('#imagePreview').hide();
            }
        });

        // 菜单计划选择变化事件
        $('#menu_plan_id').change(function() {
            var selectedOption = $(this).find('option:selected');
            if (selectedOption.val()) {
                var areaId = selectedOption.data('area');
                var mealDate = selectedOption.data('date');
                var mealType = selectedOption.data('meal');

                $('#area_id').val(areaId).trigger('change');
                $('#meal_date').val(mealDate);
                $('#meal_type').val(mealType);
            }
        });

        // 如果URL中有menu_plan_id参数，自动选择对应的菜单计划
        var urlParams = new URLSearchParams(window.location.search);
        var menuPlanId = urlParams.get('menu_plan_id');
        if (menuPlanId) {
            $('#menu_plan_id').val(menuPlanId).trigger('change');
        }
    });
</script>
{% endblock %}
