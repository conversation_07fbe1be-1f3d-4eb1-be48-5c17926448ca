{% extends 'base.html' %}

{% block title %}分类食材选择组件示例{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/select2/select2.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/select2/select2-bootstrap4.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/categorized-ingredient-select.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ingredient_id">食材 <span class="text-danger">*</span></label>
                                <select id="ingredient_id" name="ingredient_id" class="categorized-ingredient-select" required>
                                    <option value="">-- 请选择食材 --</option>
                                </select>
                                <small class="form-text text-muted">食材按分类组织，便于查找</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> 使用说明</h5>
                                <p>1. 引入必要的CSS和JS文件：</p>
                                <pre><code>
&lt;link rel="stylesheet" href="{{ url_for('static', filename='vendor/select2/select2.min.css') }}"&gt;
&lt;link rel="stylesheet" href="{{ url_for('static', filename='vendor/select2/select2-bootstrap4.min.css') }}"&gt;
&lt;link rel="stylesheet" href="{{ url_for('static', filename='css/categorized-ingredient-select.css') }}"&gt;

&lt;script src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"&gt;&lt;/script&gt;
&lt;script src="{{ url_for('static', filename='js/categorized-ingredient-select.js') }}"&gt;&lt;/script&gt;
                                </code></pre>
                                
                                <p>2. 创建select元素并添加class：</p>
                                <pre><code>
&lt;select id="ingredient_id" name="ingredient_id" class="categorized-ingredient-select" required&gt;
    &lt;option value=""&gt;-- 请选择食材 --&lt;/option&gt;
&lt;/select&gt;
                                </code></pre>
                                
                                <p>3. 初始化组件：</p>
                                <pre><code>
$(document).ready(function() {
    $('.categorized-ingredient-select').categorizedIngredientSelect();
});
                                </code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/categorized-ingredient-select.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化分类食材选择组件
        $('.categorized-ingredient-select').categorizedIngredientSelect();
        
        // 监听选择变化
        $('#ingredient_id').on('change', function() {
            var selectedId = $(this).val();
            var selectedText = $(this).find('option:selected').text();
            
            if (selectedId) {
                console.log('已选择食材:', selectedId, selectedText);
            }
        });
    });
</script>
{% endblock %}
