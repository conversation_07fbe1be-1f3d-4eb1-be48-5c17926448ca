/* 步骤进度条样式 */

/* 基础进度条样式 */
.progress-steps {
    margin-bottom: 1.5rem;
}

.progress-steps .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-steps .progress-bar {
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* 步骤标签样式 */
.progress-steps .step-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.progress-steps .step-label {
    font-size: 0.75rem;
    line-height: 1.2;
    text-align: center;
    flex: 1;
    min-width: 0;
    padding: 0 0.25rem;
}

.progress-steps .step-label.active {
    font-weight: bold;
    color: #28a745 !important;
}

.progress-steps .step-label.completed {
    color: #28a745 !important;
}

.progress-steps .step-label.pending {
    color: #6c757d !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .progress-steps .step-labels {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .progress-steps .step-label {
        margin-bottom: 0.25rem;
        text-align: left;
        flex: none;
        width: auto;
    }
    
    /* 移动端使用网格布局 */
    .progress-steps .step-labels-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.25rem;
        margin-top: 0.5rem;
    }
    
    .progress-steps .step-labels-grid .step-label {
        margin-bottom: 0;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .progress-steps .step-labels-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .progress-steps .step-label {
        font-size: 0.7rem;
        padding: 0.1rem;
    }
}

/* 圆形步骤样式 */
.progress-steps-circular {
    margin-bottom: 2rem;
}

.progress-steps-circular .step-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.progress-steps-circular .step-circle.active {
    background-color: #28a745 !important;
    color: white !important;
    /* /* box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25); */ /* 移除阴影效果 */ */ /* 移除阴影效果 */
}

.progress-steps-circular .step-circle.completed {
    background-color: #28a745 !important;
    color: white !important;
}

.progress-steps-circular .step-circle.pending {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    border: 2px solid #dee2e6;
}

.progress-steps-circular .step-connector {
    height: 2px;
    background-color: #dee2e6;
    flex-grow: 1;
    margin: 0 0.5rem;
    transition: background-color 0.3s ease;
}

.progress-steps-circular .step-connector.completed {
    background-color: #28a745;
}

.progress-steps-circular .step-text {
    font-size: 0.7rem;
    text-align: center;
    max-width: 80px;
    line-height: 1.2;
    word-wrap: break-word;
}

/* 面包屑样式 */
.progress-steps-breadcrumb .breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 0.5rem;
}

.progress-steps-breadcrumb .breadcrumb-item {
    font-size: 0.8rem;
}

.progress-steps-breadcrumb .breadcrumb-item.active {
    font-weight: bold;
}

.progress-steps-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: bold;
}

/* 紧凑型样式 */
.progress-steps-compact {
    margin-bottom: 1rem;
}

.progress-steps-compact .progress {
    height: 6px;
}

.progress-steps-compact .step-info {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* 修复常见的显示问题 */
.progress-steps-container {
    width: 100%;
    overflow-x: auto;
    padding: 0.5rem 0;
}

/* 确保进度条在小屏幕上不会被截断 */
.progress-steps .progress {
    min-width: 200px;
    width: 100%;
}

/* 防止步骤标签重叠 */
.progress-steps .step-labels {
    min-height: 2rem;
}

/* 动画效果 */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width);
    }
}

.progress-steps .progress-bar.animated {
    animation: progressFill 1s ease-out;
}

/* 主题颜色变量 */
:root {
    --progress-success-color: #28a745;
    --progress-muted-color: #6c757d;
    --progress-bg-color: #e9ecef;
    --progress-text-color: #495057;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --progress-success-color: #20c997;
        --progress-muted-color: #adb5bd;
        --progress-bg-color: #495057;
        --progress-text-color: #f8f9fa;
    }
    
    .progress-steps .progress {
        background-color: var(--progress-bg-color);
    }
    
    .progress-steps .step-label {
        color: var(--progress-text-color);
    }
}

/* 打印样式 */
@media print {
    .progress-steps {
        margin-bottom: 1rem;
    }
    
    .progress-steps .progress {
        border: 1px solid #000;
        background-color: #fff !important;
    }
    
    .progress-steps .progress-bar {
        background-color: #000 !important;
    }
    
    .progress-steps .step-label {
        color: #000 !important;
    }
}
