{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            {{ form.name.label }}
                            {{ form.name(class="form-control") }}
                            {% if form.name.errors %}
                                <div class="text-danger">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.area_id.label }}
                            {{ form.area_id(class="form-control") }}
                            {% if form.area_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.area_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.location.label }}
                            {{ form.location(class="form-control") }}
                            {% if form.location.errors %}
                                <div class="text-danger">
                                    {% for error in form.location.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.manager_id.label }}
                            {{ form.manager_id(class="form-control") }}
                            {% if form.manager_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.manager_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.capacity.label }}
                            {{ form.capacity(class="form-control") }}
                            {% if form.capacity.errors %}
                                <div class="text-danger">
                                    {% for error in form.capacity.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.capacity_unit.label }}
                            {{ form.capacity_unit(class="form-control") }}
                            {% if form.capacity_unit.errors %}
                                <div class="text-danger">
                                    {% for error in form.capacity_unit.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.temperature_range.label }}
                            {{ form.temperature_range(class="form-control") }}
                            {% if form.temperature_range.errors %}
                                <div class="text-danger">
                                    {% for error in form.temperature_range.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.humidity_range.label }}
                            {{ form.humidity_range(class="form-control") }}
                            {% if form.humidity_range.errors %}
                                <div class="text-danger">
                                    {% for error in form.humidity_range.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.status.label }}
                            {{ form.status(class="form-control") }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.notes.label }}
                            {{ form.notes(class="form-control", rows=3) }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('warehouse.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 使用Select2增强下拉框
        $('.form-control').select2({
            theme: 'bootstrap4',
            width: '100%'
        });
    });
</script>
{% endblock %}
