{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <!-- 功能按钮 -->
    <div class="mb-4">
        <a href="{{ url_for('daily_management.edit_companion', companion_id=companion.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> 编辑记录
        </a>
        <a href="{{ url_for('daily_management.companions', log_id=companion.daily_log_id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
        <a href="{{ url_for('daily_management.print_companion_detail', companion_id=companion.id) }}" class="btn btn-info" target="_blank">
            <i class="fas fa-print"></i> 打印记录
        </a>
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
            <i class="fas fa-trash"></i> 删除记录
        </button>
    </div>

    <!-- 陪餐记录详情 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">基本信息</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>陪餐人：</strong> {{ companion.companion_name }}</p>
                            <p><strong>角色：</strong> {{ companion.companion_role }}</p>
                            <p><strong>餐次：</strong>
                                {% if companion.meal_type == 'breakfast' %}早餐
                                {% elif companion.meal_type == 'lunch' %}午餐
                                {% elif companion.meal_type == 'dinner' %}晚餐
                                {% endif %}
                            </p>
                            <p><strong>陪餐时间：</strong> {{ companion.dining_time }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>口味评分：</strong>
                                {% if companion.taste_rating %}
                                    {% for i in range(companion.taste_rating) %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(5 - companion.taste_rating) %}
                                        <i class="far fa-star text-warning"></i>
                                    {% endfor %}
                                {% else %}
                                    未评分
                                {% endif %}
                            </p>
                            <p><strong>卫生评分：</strong>
                                {% if companion.hygiene_rating %}
                                    {% for i in range(companion.hygiene_rating) %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(5 - companion.hygiene_rating) %}
                                        <i class="far fa-star text-warning"></i>
                                    {% endfor %}
                                {% else %}
                                    未评分
                                {% endif %}
                            </p>
                            <p><strong>服务评分：</strong>
                                {% if companion.service_rating %}
                                    {% for i in range(companion.service_rating) %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(5 - companion.service_rating) %}
                                        <i class="far fa-star text-warning"></i>
                                    {% endfor %}
                                {% else %}
                                    未评分
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <div class="mt-3">
                        <p><strong>评价意见：</strong></p>
                        <div class="p-3 bg-light rounded">
                            {{ companion.comments or '无' }}
                        </div>
                    </div>

                    <div class="mt-3">
                        <p><strong>改进建议：</strong></p>
                        <div class="p-3 bg-light rounded">
                            {{ companion.suggestions or '无' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">照片记录</h6>
                </div>
                <div class="card-body">
                    {% if photos %}
                        <div class="row">
                            {% for photo in photos %}
                                <div class="col-md-6 mb-3">
                                    <a href="{{ photo.file_path }}" target="_blank">
                                        <img src="{{ photo.file_path }}" class="img-fluid rounded" alt="陪餐照片">
                                    </a>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-center text-muted">暂无照片</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除 {{ companion.companion_name }} 的陪餐记录吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('daily_management.delete_companion', companion_id=companion.id) }}" method="post" novalidate novalidate>
                    {{ form.csrf_token }}
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
