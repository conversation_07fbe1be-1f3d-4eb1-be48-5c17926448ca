<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码诊断工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        
        .diagnosis-container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .qr-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .qr-version {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .qr-version.optimized {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .qr-version img {
            max-width: 200px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .url-box {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .diagnosis-result {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .issue-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .solution-list {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        @d-flex (max-width: 768px) {
            .qr-comparison {
                grid-template-columns: 1fr;
            }
        }
    
        body {
            background: #f8f9fa;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        
        .diagnosis-container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            /* box-shadow: 0 5px 15px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
        }
        
        .qr-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .qr-version {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .qr-version.optimized {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .qr-version img {
            max-width: 200px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .url-box {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .diagnosis-result {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .issue-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .solution-list {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        @d-flex (max-width: 768px) {
            .qr-comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="diagnosis-container">
            <div class="diagnosis-result">
                <h4><i class="fas fa-clipboard-check"></i> 诊断结果</h4>
                <p><strong>问题分析：</strong>食品留样标签的二维码扫不了</p>
                <p><strong>测试状态：</strong>
                    <span class="badge bg-success">URL可访问</span>
                    <span class="badge bg-info">编码已优化</span>
                    <span class="badge bg-warning">质量已提升</span>
                </p>
            </div>
            
            <div class="qr-comparison">
                <div class="qr-version">
                    <h5>原版本二维码</h5>
                    <p class="text-muted">低质量设置</p>
                    <div class="url-box">
                        容错级别: L (低)<br>
                        方块大小: 10<br>
                        边框: 4<br>
                        版本: 1
                    </div>
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        可能存在扫码困难
                    </div>
                </div>
                
                <div class="qr-version optimized">
                    <h5>优化版本二维码</h5>
                    <p class="text-success">高质量设置</p>
                    <div class="url-box">
                        容错级别: M (中等)<br>
                        方块大小: 12<br>
                        边框: 6<br>
                        版本: 2<br>
                        URL编码: 已优化
                    </div>
                    <div class="text-success">
                        <i class="fas fa-check-circle"></i>
                        扫码成功率提升
                    </div>
                </div>
            </div>
            
            <div class="issue-list">
                <h4><i class="fas fa-bug"></i> 发现的问题</h4>
                <ul>
                    <li><strong>URL编码问题：</strong>中文字符（如"午餐"）在URL中可能导致某些扫码软件无法正确识别</li>
                    <li><strong>二维码质量：</strong>原设置的容错级别过低，方块尺寸偏小</li>
                    <li><strong>打印质量：</strong>可能受打印机分辨率和纸张质量影响</li>
                    <li><strong>扫码软件兼容性：</strong>不同扫码软件对URL格式支持程度不同</li>
                </ul>
            </div>
            
            <div class="solution-list">
                <h4><i class="fas fa-tools"></i> 已实施的解决方案</h4>
                <ul>
                    <li><strong>✅ URL编码优化：</strong>对中文字符进行URL编码，确保兼容性</li>
                    <li><strong>✅ 二维码质量提升：</strong>
                        <ul>
                            <li>容错级别从L提升到M</li>
                            <li>方块大小从10增加到12</li>
                            <li>边框从4增加到6</li>
                            <li>版本从1升级到2</li>
                        </ul>
                    </li>
                    <li><strong>✅ 路由解码处理：</strong>在服务器端正确解码URL参数</li>
                    <li><strong>✅ 错误日志记录：</strong>增加详细的生成日志</li>
                </ul>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5><i class="fas fa-mobile-alt"></i> 扫码测试</h5>
                            <p class="text-muted">使用手机扫码软件测试</p>
                            <small>推荐：微信扫一扫、支付宝扫码</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5><i class="fas fa-print"></i> 打印测试</h5>
                            <p class="text-muted">测试打印质量</p>
                            <small>建议：300DPI以上分辨率</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5><i class="fas fa-link"></i> URL测试</h5>
                            <p class="text-muted">直接访问链接</p>
                            <a href="http://xiaoyuanst.com/food-trace/recipe/1/2025-01-15/%E5%8D%88%E9%A4%90/1" 
                               target="_blank" class="btn btn-sm btn-primary">
                                测试链接
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h5>使用建议</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>打印设置</h6>
                        <ul>
                            <li>使用300DPI或更高分辨率</li>
                            <li>选择高质量打印模式</li>
                            <li>使用白色背景纸张</li>
                            <li>确保墨盒充足</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>扫码技巧</h6>
                        <ul>
                            <li>保持适当扫码距离（10-20cm）</li>
                            <li>确保光线充足</li>
                            <li>保持手机稳定</li>
                            <li>尝试不同角度</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
