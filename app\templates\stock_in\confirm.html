{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>{{ title }}</h2>
      <p class="text-muted">确认入库单信息和质量状态</p>
    </div>
    <div class="col-md-4 text-end">
      <a href="{{ url_for('stock_in.view', id=stock_in.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回入库单
      </a>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 fw-bold text-primary">入库单信息</h6>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-4">
          <p><strong>入库单号：</strong> {{ stock_in.stock_in_number }}</p>
        </div>
        <div class="col-md-4">
          <p><strong>仓库：</strong> {{ stock_in.warehouse.name }}</p>
        </div>
        <div class="col-md-4">
          <p><strong>入库日期：</strong> {{ stock_in.stock_in_date.strftime('%Y-%m-%d') }}</p>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <p><strong>入库类型：</strong> {{ stock_in.stock_in_type }}</p>
        </div>
        <div class="col-md-4">
          <p><strong>供应商：</strong> {{ stock_in.supplier.name if stock_in.supplier else '自购' }}</p>
        </div>
        <div class="col-md-4">
          <p><strong>状态：</strong> {{ stock_in.status }}</p>
        </div>
      </div>
      {% if stock_in.purchase_order %}
      <div class="row">
        <div class="col-md-12">
          <p><strong>关联采购订单：</strong> 
            <a href="{{ url_for('purchase_order.view', id=stock_in.purchase_order.id) }}">
              {{ stock_in.purchase_order.order_number }}
            </a>
          </p>
        </div>
      </div>
      {% endif %}
      <div class="row">
        <div class="col-md-12">
          <p><strong>备注：</strong> {{ stock_in.notes }}</p>
        </div>
      </div>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 fw-bold text-primary">确认入库明细</h6>
    </div>
    <div class="card-body">
      <form method="post" action="{{ url_for('stock_in.confirm', id=stock_in.id) }}" novalidate novalidate>
        {{ csrf_token() }}
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>序号</th>
                <th>食材名称</th>
                <th>批次号</th>
                <th>数量</th>
                <th>单位</th>
                <th>生产日期</th>
                <th>过期日期</th>
                <th>质量状态</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              {% for item in stock_in_items %}
              <tr>
                <td>{{ loop.index }}</td>
                <td>{{ item.ingredient.name }}</td>
                <td>{{ item.batch_number }}</td>
                <td>
                  <input type="number" class="form-control" name="quantity_{{ item.id }}" value="{{ item.quantity }}" min="0" step="0.01" required>
                </td>
                <td>{{ item.unit }}</td>
                <td>{{ item.production_date.strftime('%Y-%m-%d') }}</td>
                <td>{{ item.expiry_date.strftime('%Y-%m-%d') }}</td>
                <td>
                  <select class="form-control" name="quality_{{ item.id }}">
                    <option value="良好" {% if item.quality_status == '良好' %}selected{% endif %}>良好</option>
                    <option value="一般" {% if item.quality_status == '一般' %}selected{% endif %}>一般</option>
                    <option value="较差" {% if item.quality_status == '较差' %}selected{% endif %}>较差</option>
                  </select>
                </td>
                <td>
                  <input type="text" class="form-control" name="notes_{{ item.id }}" value="{{ item.notes }}">
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <div class="alert alert-info">
          <i class="fas fa-info-circle"></i> 请确认每种食材的实际入库数量和质量状态。
        </div>
        <div class="text-center">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-check"></i> 确认入库
          </button>
          <a href="{{ url_for('stock_in.view', id=stock_in.id) }}" class="btn btn-secondary">
            <i class="fas fa-times"></i> 取消
          </a>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
