{% extends 'base.html' %}

{% block title %}检查记录小组件演示{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .demo-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        background-color: #fff;
    }
    
    .demo-section h2 {
        margin-bottom: 1rem;
        color: #4e73df;
        font-size: 1.5rem;
    }
    
    .demo-section .description {
        margin-bottom: 1.5rem;
        color: #5a5c69;
    }
    
    .code-block {
        background-color: #f8f9fc;
        padding: 1rem;
        border-radius: 0.35rem;
        border-start: 4px solid #4e73df;
        margin-bottom: 1.5rem;
        font-family: monospace;
        white-space: pre-wrap;
        overflow-x: auto;
    }
    
    .widget-container {
        border: 1px dashed #e3e6f0;
        padding: 1rem;
        border-radius: 0.35rem;
        background-color: #f8f9fc;
    }

    .demo-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        background-color: #fff;
    }
    
    .demo-section h2 {
        margin-bottom: 1rem;
        color: #4e73df;
        font-size: 1.5rem;
    }
    
    .demo-section .description {
        margin-bottom: 1.5rem;
        color: #5a5c69;
    }
    
    .code-block {
        background-color: #f8f9fc;
        padding: 1rem;
        border-radius: 0.35rem;
        border-start: 4px solid #4e73df;
        margin-bottom: 1.5rem;
        font-family: monospace;
        white-space: pre-wrap;
        overflow-x: auto;
    }
    
    .widget-container {
        border: 1px dashed #e3e6f0;
        padding: 1rem;
        border-radius: 0.35rem;
        background-color: #f8f9fc;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="demo-section">
                <div class="description">
                    <p>检查记录小组件可以嵌入到任何页面中，用于展示指定日期或指定日志ID的检查记录。</p>
                    <p>小组件会自动加载数据，并提供照片查看、评分展示等功能。</p>
                </div>
                
                <div class="code-block">
&lt;!-- 引入检查记录小组件 --&gt;
{% raw %}{% include 'daily_management/inspection_table_widget.html' with context %}{% endraw %}
                </div>
                
                <div class="widget-container">
                    <!-- 引入检查记录小组件 -->
                    {% include 'daily_management/inspection_table_widget.html' with context %}
                </div>
            </div>
            
            <div class="demo-section">
                <div class="description">
                    <p>可以通过指定 <code>log_id</code> 参数来展示特定日志的检查记录。</p>
                </div>
                
                <div class="code-block">
&lt;!-- 引入检查记录小组件，指定日志ID --&gt;
{% raw %}{% with widget_id='demo1', log_id=1 %}
    {% include 'daily_management/inspection_table_widget.html' with context %}
{% endwith %}{% endraw %}
                </div>
                
                <div class="widget-container">
                    <!-- 引入检查记录小组件，指定日志ID -->
                    {% with widget_id='demo1', log_id=1 %}
                        {% include 'daily_management/inspection_table_widget.html' with context %}
                    {% endwith %}
                </div>
            </div>
            
            <div class="demo-section">
                <div class="description">
                    <p>可以通过指定 <code>date_str</code> 参数来展示特定日期的检查记录。</p>
                </div>
                
                <div class="code-block">
&lt;!-- 引入检查记录小组件，指定日期 --&gt;
{% raw %}{% with widget_id='demo2', date_str='2023-01-01' %}
    {% include 'daily_management/inspection_table_widget.html' with context %}
{% endwith %}{% endraw %}
                </div>
                
                <div class="widget-container">
                    <!-- 引入检查记录小组件，指定日期 -->
                    {% with widget_id='demo2', date_str='2023-01-01' %}
                        {% include 'daily_management/inspection_table_widget.html' with context %}
                    {% endwith %}
                </div>
            </div>
            
            <div class="demo-section">
                <div class="description">
                    <p>可以在首页中使用检查记录小组件，展示今天的检查记录。</p>
                </div>
                
                <div class="code-block">
&lt;!-- 在首页中使用检查记录小组件 --&gt;
&lt;div class="card shadow mb-4"&gt;
    &lt;div class="card-header py-3 d-flex flex-row align-items-center justify-content-between"&gt;
        &lt;h6 class="m-0 fw-bold text-primary"&gt;今日检查记录&lt;/h6&gt;
        &lt;div class="dropdown no-arrow"&gt;
            &lt;a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"&gt;
                &lt;i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"&gt;&lt;/i&gt;
            &lt;/a&gt;
            &lt;div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink"&gt;
                &lt;a class="dropdown-item" href="{{ url_for('daily_management.inspections_by_date', date_str=today.strftime('%Y-%m-%d')) }}"&gt;查看详情&lt;/a&gt;
                &lt;a class="dropdown-item" href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='morning') }}"&gt;编辑晨检&lt;/a&gt;
                &lt;a class="dropdown-item" href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='noon') }}"&gt;编辑午检&lt;/a&gt;
                &lt;a class="dropdown-item" href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='evening') }}"&gt;编辑晚检&lt;/a&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="card-body"&gt;
        {% raw %}{% with widget_id='today' %}
            {% include 'daily_management/inspection_table_widget.html' with context %}
        {% endwith %}{% endraw %}
    &lt;/div&gt;
&lt;/div&gt;
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="d-flex justify-content-between mb-4">
        <div>
            <a href="{{ url_for('daily_management.index') }}" class="btn btn-secondary">
                <i class="fas fa-home me-1"></i> 返回首页
            </a>
        </div>
        <div>
            <a href="{{ url_for('daily_management.logs') }}" class="btn btn-primary">
                <i class="fas fa-list me-1"></i> 查看所有日志
            </a>
        </div>
    </div>
</div>
{% endblock %}
