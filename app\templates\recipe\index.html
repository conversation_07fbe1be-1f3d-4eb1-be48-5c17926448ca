{% extends 'base.html' %}

{% block title %}食谱管理{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .recipe-card {
        height: 100%;
        transition: transform 0.3s, box-shadow 0.3s;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }
    .recipe-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .recipe-image-container {
        height: 180px;
        overflow: hidden;
        position: relative;
    }
    .recipe-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
    }
    .recipe-card:hover .recipe-image {
        transform: scale(1.05);
    }
    .recipe-badges {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
    }
    .favorite-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
        background-color: rgba(255,255,255,0.7);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    .favorite-btn i {
        font-size: 18px;
        color: #ccc;
        transition: all 0.3s;
    }
    .favorite-btn.active i {
        color: #dc3545;
    }
    .favorite-btn:hover {
        background-color: rgba(255,255,255,0.9);
    }
    .favorite-btn:hover i {
        transform: scale(1.1);
    }
    .recipe-info {
        padding: 15px;
    }
    .recipe-title {
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 10px;
        height: 40px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .recipe-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        color: #6c757d;
        font-size: 0.9rem;
    }
    .recipe-tags {
        margin-bottom: 10px;
    }
    .recipe-footer {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
    }
    .filter-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .filter-title {
        font-weight: bold;
        margin-bottom: 10px;
    }

    .recipe-card {
        height: 100%;
        transition: transform 0.3s, box-shadow 0.3s;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }
    .recipe-card:hover {
        transform: translateY(-5px);
        /* box-shadow: 0 5px 15px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
    }
    .recipe-image-container {
        height: 180px;
        overflow: hidden;
        position: relative;
    }
    .recipe-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
    }
    .recipe-card:hover .recipe-image {
        transform: scale(1.05);
    }
    .recipe-badges {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
    }
    .favorite-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
        background-color: rgba(255,255,255,0.7);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    .favorite-btn i {
        font-size: 18px;
        color: #ccc;
        transition: all 0.3s;
    }
    .favorite-btn.active i {
        color: #dc3545;
    }
    .favorite-btn:hover {
        background-color: rgba(255,255,255,0.9);
    }
    .favorite-btn:hover i {
        transform: scale(1.1);
    }
    .recipe-info {
        padding: 15px;
    }
    .recipe-title {
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 10px;
        height: 40px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .recipe-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        color: #6c757d;
        font-size: 0.9rem;
    }
    .recipe-tags {
        margin-bottom: 10px;
    }
    .recipe-footer {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
    }
    .filter-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .filter-title {
        font-weight: bold;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <!-- 桌面端布局 -->
                    <div class="d-flex justify-content-between align-items-center desktop-only">
                        <div>
                            <div class="d-flex align-items-center">
                                <small class="text-muted me-3">
                                    <i class="fas fa-info-circle"></i>
                                    显示系统食谱和您学校的专用食谱，可复制系统食谱进行个性化改造
                                </small>
                                <span class="badge bg-light me-2">
                                    <i class="fas fa-list"></i> 共 {{ pagination.total }} 个食谱
                                </span>
                            </div>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('recipe_favorite.index') }}" class="btn btn-danger btn-sm me-2">
                                <i class="fas fa-heart"></i> 我的收藏
                            </a>
                            <a href="{{ url_for('recipe.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加食谱
                            </a>
                            <a href="{{ url_for('recipe_category.index') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </div>
                    </div>

                    <!-- 移动端布局 -->
                    <div class="mobile-only">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <small class="text-muted d-block mb-2">
                                        <i class="fas fa-info-circle"></i>
                                        显示系统食谱和您学校的专用食谱
                                    </small>
                                    <span class="badge bg-light">
                                        <i class="fas fa-list"></i> 共 {{ pagination.total }} 个食谱
                                    </span>
                                </div>
                                <div class="action-buttons">
                                    <a href="{{ url_for('recipe_favorite.index') }}" class="btn btn-danger">
                                        <i class="fas fa-heart"></i> 我的收藏
                                    </a>
                                    <a href="{{ url_for('recipe.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> 添加食谱
                                    </a>
                                    <a href="{{ url_for('recipe_category.index') }}" class="btn btn-info">
                                        <i class="fas fa-tags"></i> 分类管理
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <div class="filter-section">
                        <div class="filter-title">
                            <i class="fas fa-filter"></i> 筛选条件
                        </div>
                        <form method="GET" class="mb-2">
                            <div class="row">
                                <div class="col-lg-2 col-md-4 col-12 mobile-mb-2">
                                    <div class="mb-3">
                                        <label for="recipe_type">食谱来源</label>
                                        <select class="form-control" id="recipe_type" name="recipe_type">
                                            <option value="">-- 全部 --</option>
                                            <option value="system" {% if recipe_type == 'system' %}selected{% endif %}>
                                                系统食谱
                                            </option>
                                            <option value="school" {% if recipe_type == 'school' %}selected{% endif %}>
                                                {{ current_area.name if current_area else '学校' }}食谱
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4 col-12 mobile-mb-2">
                                    <div class="mb-3">
                                        <label for="category_id">食谱分类</label>
                                        <select class="form-control" id="category_id" name="category_id">
                                            <option value="">-- 所有分类 --</option>
                                            {% for category in categories %}
                                            <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>
                                                {{ category.name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4 col-12 mobile-mb-2">
                                    <div class="mb-3">
                                        <label for="meal_type">适用餐次</label>
                                        <select class="form-control" id="meal_type" name="meal_type">
                                            <option value="">-- 所有餐次 --</option>
                                            <option value="早餐">早餐</option>
                                            <option value="午餐">午餐</option>
                                            <option value="晚餐">晚餐</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 col-12 mobile-mb-2">
                                    <div class="mb-3">
                                        <label for="keyword">关键词</label>
                                        <input type="text" class="form-control" id="keyword" name="keyword" value="{{ keyword }}" placeholder="食谱名称/食材">
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="mb-3" style="margin-top: 32px;">
                                        <div class="btn-group w-100 d-md-none">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> 搜索
                                            </button>
                                            <a href="{{ url_for('recipe.index') }}" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> 重置
                                            </a>
                                        </div>
                                        <div class="d-none d-md-block">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> 搜索
                                            </button>
                                            <a href="{{ url_for('recipe.index') }}" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 食谱列表 -->
                    <div class="row">
                        {% for recipe in recipes %}
                        <div class="col-lg-3 col-md-4 col-sm-6 col-12 mobile-mb-2">
                            <div class="card recipe-card">
                                <div class="recipe-image-container">
                                    {% if recipe.main_image %}
                                    <img src="{{ url_for('static', filename=recipe.main_image) }}" alt="{{ recipe.name }}" class="recipe-image">
                                    {% else %}
                                    <img src="{{ url_for('static', filename='img/no-image.png') }}" alt="No Image" class="recipe-image">
                                    {% endif %}

                                    <div class="recipe-badges">
                                        {% if recipe.status == 1 %}
                                        <span class="badge bg-success">启用</span>
                                        {% else %}
                                        <span class="badge bg-danger">停用</span>
                                        {% endif %}

                                        {% if recipe.is_global %}
                                        <span class="badge bg-info" title="系统预设食谱，可复制为学校专用版本">
                                            <i class="fas fa-globe"></i> 系统
                                        </span>
                                        {% elif recipe.area_id %}
                                        <span class="badge bg-success" title="学校专用食谱，可自由编辑">
                                            <i class="fas fa-school"></i> {{ recipe.area.name if recipe.area else '专用' }}
                                        </span>
                                        {% endif %}

                                        {% if recipe.is_user_defined %}
                                        <span class="badge bg-warning">自定义</span>
                                        {% endif %}

                                        {% if recipe.meal_type %}
                                        <span class="badge bg-secondary">{{ recipe.meal_type }}</span>
                                        {% endif %}
                                    </div>

                                    <div class="favorite-btn" data-recipe-id="{{ recipe.id }}">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                </div>

                                <div class="recipe-info">
                                    <h5 class="recipe-title">{{ recipe.name }}</h5>

                                    <div class="recipe-meta">
                                        <span>
                                            <i class="fas fa-utensils"></i>
                                            {% if recipe.category_rel %}
                                                {{ recipe.category_rel.name }}
                                            {% else %}
                                                {{ recipe.category }}
                                            {% endif %}
                                        </span>

                                        {% if recipe.cooking_time %}
                                        <span><i class="far fa-clock"></i> {{ recipe.cooking_time }}分钟</span>
                                        {% endif %}
                                    </div>

                                    <div class="recipe-tags">
                                        <span class="badge bg-light"><i class="fas fa-carrot"></i> {{ recipe.ingredients.count() }}种食材</span>
                                        {% if recipe.calories %}
                                        <span class="badge bg-light"><i class="fas fa-fire"></i> {{ recipe.calories }}卡</span>
                                        {% endif %}
                                        {% if recipe.cooking_method %}
                                        <span class="badge bg-light">{{ recipe.cooking_method }}</span>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="recipe-footer">
                                    <div class="btn-group btn-group-sm w-100">
                                        <a href="{{ url_for('recipe.view', id=recipe.id) }}" class="btn btn-outline-info">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        {% if current_user.is_admin() %}
                                        <!-- 超级管理员：可以编辑删除所有食谱 -->
                                        <a href="{{ url_for('recipe.edit', id=recipe.id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <button class="btn btn-outline-warning delete-btn" data-id="{{ recipe.id }}" title="安全删除（可恢复）">
                                            <i class="fas fa-archive"></i> 删除
                                        </button>
                                        {% elif recipe.is_global %}
                                        <!-- 系统食谱：普通用户只能复制 -->
                                        <button class="btn btn-outline-success copy-btn" data-id="{{ recipe.id }}" title="复制为学校专用食谱">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                        {% elif recipe.area_id and recipe.area_id == current_area.id %}
                                        <!-- 本校食谱：可以编辑删除 -->
                                        <a href="{{ url_for('recipe.edit', id=recipe.id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <button class="btn btn-outline-warning delete-btn" data-id="{{ recipe.id }}" title="安全删除（可恢复）">
                                            <i class="fas fa-archive"></i> 删除
                                        </button>
                                        {% else %}
                                        <!-- 其他学校食谱或无归属食谱：只能复制 -->
                                        <button class="btn btn-outline-success copy-btn" data-id="{{ recipe.id }}" title="复制为学校专用食谱">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- 分页 -->
                    <div class="mt-4">
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if pagination.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('recipe.index', page=pagination.prev_num, per_page=pagination.per_page, category_id=category_id, keyword=keyword, recipe_type=recipe_type) }}">上一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">上一页</span>
                                </li>
                                {% endif %}

                                {% for page_num in pagination.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num == pagination.page %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('recipe.index', page=page_num, per_page=pagination.per_page, category_id=category_id, keyword=keyword, recipe_type=recipe_type) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('recipe.index', page=pagination.next_num, per_page=pagination.per_page, category_id=category_id, keyword=keyword, recipe_type=recipe_type) }}">下一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">下一页</span>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        <div class="text-center">
                            <span class="text-muted">
                                显示 {{ pagination.total }} 条记录中的第
                                {{ (pagination.page - 1) * pagination.per_page + 1 }}
                                到
                                {{ (pagination.page * pagination.per_page) if (pagination.page * pagination.per_page < pagination.total) else pagination.total }}
                                条
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-archive text-warning"></i> 删除食谱
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>安全删除说明：</strong>
                </div>
                <p class="mb-2">确定要删除这个食谱吗？</p>
                <ul class="text-muted small mb-0">
                    <li><i class="fas fa-shield-alt text-success"></i> 数据将被安全保留，不会影响留样记录和溯源功能</li>
                    <li><i class="fas fa-eye-slash text-info"></i> 食谱将从列表中隐藏，但历史数据完整保存</li>
                    <li><i class="fas fa-undo text-primary"></i> 如需要可以联系管理员恢复</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" class="btn btn-warning" id="confirmDelete">
                    <i class="fas fa-archive"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/auth-helper.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 检查所有食谱的收藏状态
        checkAllFavoriteStatus();

        // 收藏按钮点击事件
        $('.favorite-btn').click(function() {
            var recipeId = $(this).data('recipe-id');
            toggleFavorite(recipeId, $(this));
        });

        // 复制功能
        $('.copy-btn').click(function() {
            var recipeId = $(this).data('id');
            var btn = $(this);

            // 使用requireLogin函数检查登录状态
            requireLogin(function() {
                // 禁用按钮，防止重复点击
                btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 复制中...');

                $.ajax({
                    url: '{{ url_for("recipe.copy_recipe", id=0) }}'.replace('0', recipeId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            // 根据操作类型显示不同的提示
                            if (response.action === 'restored_and_updated') {
                                toastr.success(response.message, '食谱已恢复', {
                                    timeOut: 4000,
                                    extendedTimeOut: 1000
                                });
                                // 显示额外的恢复提示
                                setTimeout(function() {
                                    toastr.info('原已删除的食谱已恢复并更新为最新版本', '恢复成功', {
                                        timeOut: 3000
                                    });
                                }, 500);
                            } else {
                                toastr.success(response.message, '复制成功', {
                                    timeOut: 3000,
                                    extendedTimeOut: 1000
                                });
                            }

                            // 3秒后刷新页面，让用户看到新复制/恢复的食谱
                            setTimeout(function() {
                                window.location.reload();
                            }, 2500);
                        } else {
                            toastr.error(response.message || '复制失败，请稍后重试！');
                            // 恢复按钮状态
                            btn.prop('disabled', false).html('<i class="fas fa-copy"></i> 复制');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('复制失败:', xhr.responseText);

                        // 使用handleAuthError函数处理认证错误
                        if (!handleAuthError(xhr, '{{ request.path }}')) {
                            if (xhr.status === 403 ||
                                (xhr.responseText && xhr.responseText.indexOf('CSRF') !== -1)) {
                                toastr.error('CSRF验证失败，请刷新页面后重试');
                            } else {
                                toastr.error('复制失败，请稍后重试！');
                            }
                        }

                        // 恢复按钮状态
                        btn.prop('disabled', false).html('<i class="fas fa-copy"></i> 复制');
                    }
                });
            }, '请先登录后再复制食谱');
        });

        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal.show();
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                // 使用requireLogin函数检查登录状态
                requireLogin(function() {
                    $.ajax({
                        url: '{{ url_for("recipe.delete", id=0) }}'.replace('0', deleteId),
                        type: 'POST',
                        success: function(response) {
                            if (response.success === true || response.success === 1) {
                                // 显示软删除成功的详细信息
                                toastr.success(response.message, '删除成功', {
                                    timeOut: 3000,
                                    extendedTimeOut: 1000
                                });

                                // 如果有操作者信息，显示额外提示
                                if (response.details && response.details.deleted_by) {
                                    setTimeout(function() {
                                        toastr.info('操作者: ' + response.details.deleted_by, '操作记录', {
                                            timeOut: 2000
                                        });
                                    }, 500);
                                }

                                setTimeout(function() {
                                    window.location.reload();
                                }, 1500);
                            } else {
                                toastr.error(response.message || '删除失败，请稍后重试！');
                            }
                            $('#deleteModal').modal.hide();
                        },
                        error: function(xhr, status, error) {
                            console.error('删除失败:', xhr.responseText);

                            // 使用handleAuthError函数处理认证错误
                            if (!handleAuthError(xhr, '{{ request.path }}')) {
                                if (xhr.status === 403 ||
                                    (xhr.responseText && xhr.responseText.indexOf('CSRF') !== -1)) {
                                    toastr.error('CSRF验证失败，请刷新页面后重试');
                                } else {
                                    toastr.error('删除失败，请稍后重试！');
                                }
                            }

                            $('#deleteModal').modal.hide();
                        }
                    });
                }, '请先登录后再删除食谱');
            }
        });

        // 检查所有食谱的收藏状态
        function checkAllFavoriteStatus() {
            // 获取所有食谱ID
            var recipeIds = [];
            $('.favorite-btn').each(function() {
                recipeIds.push($(this).data('recipe-id'));
            });

            // 批量检查收藏状态
            recipeIds.forEach(function(recipeId) {
                checkFavoriteStatus(recipeId);
            });
        }

        // 检查单个食谱的收藏状态
        function checkFavoriteStatus(recipeId) {
            // 只有在用户已登录的情况下才检查收藏状态
            if (getLoginStatus()) {
                $.ajax({
                    url: '/api/check-favorite/' + recipeId,
                    type: 'GET',
                    success: function(response) {
                        if (response.is_favorited) {
                            $('.favorite-btn[data-recipe-id="' + recipeId + '"]').addClass('active');
                        }
                    },
                    error: function(xhr) {
                        // 使用handleAuthError函数处理认证错误
                        handleAuthError(xhr, '{{ request.path }}');
                    }
                });
            }
        }

        // 收藏/取消收藏
        function toggleFavorite(recipeId, btn) {
            // 使用requireLogin函数检查登录状态
            requireLogin(function() {
                $.ajax({
                    url: '/api/toggle-favorite/' + recipeId,
                    type: 'POST',
                    success: function(response) {
                        if (response.action === 'favorited') {
                            btn.addClass('active');
                            toastr.success('食谱已收藏');
                        } else {
                            btn.removeClass('active');
                            toastr.info('已取消收藏');
                        }
                    },
                    error: function(xhr) {
                        // 使用handleAuthError函数处理认证错误
                        if (!handleAuthError(xhr, '{{ request.path }}')) {
                            toastr.error('操作失败，请重试');
                        }
                    }
                });
            }, '请先登录后再收藏食谱');
        }
    });
</script>
{% endblock %}
