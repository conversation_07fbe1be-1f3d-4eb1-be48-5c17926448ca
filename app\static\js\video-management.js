/**
 * 视频管理模块 JavaScript
 * 版本: 2.0
 */

// 确保全局函数立即可用
window.uploadVideoForStep = function(stepName) {
    console.log('uploadVideoForStep called with:', stepName);
    $('#stepSelect').val(stepName);
    $('#uploadVideoModal').modal.show();
};

window.previewVideo = function(videoUrl, videoName) {
    console.log('previewVideo called with:', videoUrl, videoName);
    $('#previewVideoTitle').text(videoName);
    const video = $('#previewVideo')[0];

    video.src = videoUrl;
    video.style.display = 'block';
    $('#videoLoadError').hide();

    video.onerror = function() {
        video.style.display = 'none';
        $('#videoLoadError').show();
    };

    $('#videoPreviewModal').modal.show();
};

window.uploadVideo = function() {
    console.log('uploadVideo called');
    
    // 防止重复提交
    const uploadBtn = $('button[onclick="uploadVideo()"]');
    if (uploadBtn.prop('disabled')) {
        return;
    }

    // 检查文件大小
    const fileInput = $('#videoFile')[0];
    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (file.size > maxSize) {
            alert('视频文件大小不能超过50MB，请压缩后重试');
            return;
        }
    }

    uploadBtn.prop('disabled', true);

    const form = $('#uploadVideoForm')[0];
    const formData = new FormData(form);

    // 显示进度条
    $('#uploadProgress').show();

    $.ajax({
        url: '/admin/guide/videos/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener("progress", function(evt) {
                if (evt.lengthComputable) {
                    const percentComplete = evt.loaded / evt.total * 100;
                    $('.progress-bar').css('width', percentComplete + '%');
                }
            }, false);
            return xhr;
        },
        success: function(data) {
            if (data.success) {
                alert('视频上传成功！');
                $('#uploadVideoModal').modal.hide();
                location.reload();
            } else {
                alert('上传失败：' + data.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('上传失败:', error);
            alert('上传失败，请稍后重试');
        },
        complete: function() {
            $('#uploadProgress').hide();
            $('.progress-bar').css('width', '0%');
            // 重新启用上传按钮
            $('button[onclick="uploadVideo()"]').prop('disabled', false);
        }
    });
};

window.deleteVideoById = function(videoId, videoName) {
    console.log('deleteVideoById called with:', videoId, videoName);
    
    if (confirm(`确定要删除视频"${videoName}"吗？此操作不可恢复。`)) {
        $.ajax({
            url: `/admin/guide/videos/${videoId}/delete`,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    alert('视频删除成功！');
                    location.reload();
                } else {
                    alert('删除失败：' + data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('删除失败:', error);
                alert('删除失败，请稍后重试');
            }
        });
    }
};

// DOM加载完成后的初始化
$(document).ready(function() {
    console.log('视频管理模块已加载');
    console.log('uploadVideoForStep函数:', typeof window.uploadVideoForStep);
    console.log('uploadVideo函数:', typeof window.uploadVideo);
    console.log('previewVideo函数:', typeof window.previewVideo);
    console.log('deleteVideoById函数:', typeof window.deleteVideoById);
    
    // 测试函数是否可用
    if (typeof window.uploadVideoForStep !== 'function') {
        console.error('uploadVideoForStep函数未正确定义！');
    }
    if (typeof window.uploadVideo !== 'function') {
        console.error('uploadVideo函数未正确定义！');
    }
});
