{% extends "financial/base.html" %}

{% block title %}用友财务样式演示{% endblock %}

{% block financial_content %}
<!-- 用友财务专业工具栏 -->
<div class="uf-financial-toolbar">
    <div class="uf-financial-toolbar-left">
        <span class="uf-financial-toolbar-title">
            <i class="fas fa-palette uf-financial-icon"></i>
            用友财务样式演示
        </span>
    </div>
    <div class="uf-financial-toolbar-right">
        <button class="uf-financial-btn">
            <i class="fas fa-download uf-financial-icon"></i>导出样式
        </button>
    </div>
</div>

<!-- 财务汇总卡片演示 -->
<div class="uf-financial-summary-card">
    <div class="uf-financial-summary-title">
        <i class="fas fa-chart-bar uf-financial-icon"></i>财务数据汇总
    </div>
    <div class="uf-financial-summary-content">
        <div class="uf-financial-summary-item">
            <div class="uf-financial-summary-label">应付账款总额</div>
            <div class="uf-financial-summary-value">¥156,800.00</div>
        </div>
        <div class="uf-financial-summary-item">
            <div class="uf-financial-summary-label">已付金额</div>
            <div class="uf-financial-summary-value">¥88,800.00</div>
        </div>
        <div class="uf-financial-summary-item">
            <div class="uf-financial-summary-label">未付余额</div>
            <div class="uf-financial-summary-value">¥68,000.00</div>
        </div>
    </div>
</div>

<!-- 财务按钮演示 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-mouse-pointer uf-financial-icon"></i>财务专用按钮样式
    </div>
    <div class="uf-card-body">
        <div style="display: flex; gap: 12px; flex-wrap: wrap; margin-bottom: 16px;">
            <button class="uf-financial-btn">标准按钮</button>
            <button class="uf-financial-btn generate">生成按钮</button>
            <button class="uf-financial-btn approve">审核按钮</button>
            <button class="uf-financial-btn cancel">取消按钮</button>
        </div>
        <div style="display: flex; gap: 12px; flex-wrap: wrap;">
            <button class="uf-financial-btn">
                <i class="fas fa-plus uf-financial-icon"></i>带图标按钮
            </button>
            <button class="uf-financial-btn generate">
                <i class="fas fa-check uf-financial-icon"></i>生成凭证
            </button>
            <button class="uf-financial-btn approve">
                <i class="fas fa-stamp uf-financial-icon"></i>审核通过
            </button>
        </div>
    </div>
</div>

<!-- 财务表单演示 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-edit uf-financial-icon"></i>财务表单样式
    </div>
    <div class="uf-card-body">
        <div class="uf-financial-form">
            <div class="uf-financial-row g-3">
                <div class="uf-financial-mb-3">
                    <label class="uf-financial-form-label">凭证日期：</label>
                    <input type="date" class="uf-financial-form-control" value="2025-06-11">
                </div>
                <div class="uf-financial-mb-3">
                    <label class="uf-financial-form-label">凭证类型：</label>
                    <select class="uf-financial-form-control">
                        <option>入库凭证</option>
                        <option>付款凭证</option>
                        <option>收款凭证</option>
                    </select>
                </div>
            </div>
            <div class="uf-financial-row g-3">
                <div class="uf-financial-mb-3">
                    <label class="uf-financial-form-label">借方科目：</label>
                    <input type="text" class="uf-financial-form-control" placeholder="请选择科目">
                </div>
                <div class="uf-financial-mb-3">
                    <label class="uf-financial-form-label">金额：</label>
                    <input type="text" class="uf-amount-input" placeholder="0.00" value="68,000.00">
                </div>
            </div>
            <div class="uf-financial-row g-3">
                <div class="uf-financial-mb-3" style="flex: 1;">
                    <label class="uf-financial-form-label">摘要：</label>
                    <input type="text" class="uf-financial-form-control" placeholder="请输入摘要信息" value="采购食材原料">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 财务状态标签演示 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-tags uf-financial-icon"></i>财务状态标签
    </div>
    <div class="uf-card-body">
        <div style="display: flex; gap: 12px; flex-wrap: wrap; align-items: center;">
            <span class="uf-financial-status draft">草稿</span>
            <span class="uf-financial-status approved">已审核</span>
            <span class="uf-financial-status posted">已记账</span>
            <span class="uf-financial-status cancelled">已取消</span>
        </div>
    </div>
</div>

<!-- 财务数据显示演示 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-database uf-financial-icon"></i>财务数据显示
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
            <div>
                <h6>科目信息：</h6>
                <p>
                    <span class="uf-subject-code">1201</span> 
                    <span class="uf-subject-name">原材料</span>
                </p>
                <p>
                    <span class="uf-subject-code">2001</span> 
                    <span class="uf-subject-name">应付账款</span>
                </p>
            </div>
            <div>
                <h6>凭证号码：</h6>
                <p><span class="uf-voucher-number">PZ20250611001</span></p>
                <p><span class="uf-voucher-number">PZ20250611002</span></p>
            </div>
            <div>
                <h6>金额显示：</h6>
                <p><span class="uf-financial-amount positive">¥68,000.00</span></p>
                <p><span class="uf-financial-amount negative">¥-5,000.00</span></p>
                <p><span class="uf-financial-amount zero">¥0.00</span></p>
            </div>
            <div>
                <h6>日期时间：</h6>
                <p><span class="uf-financial-date">2025-06-11</span></p>
                <p><span class="uf-financial-date">2025-06-10 14:30</span></p>
            </div>
        </div>
    </div>
</div>

<!-- 财务表格演示 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-table uf-financial-icon"></i>财务专业表格
    </div>
    <div class="uf-card-body">
        <table class="uf-financial-table">
            <thead>
                <tr>
                    <th class="col-date">日期</th>
                    <th class="col-voucher">凭证号</th>
                    <th class="col-subject-code">科目代码</th>
                    <th class="col-subject-name">科目名称</th>
                    <th class="col-amount">借方金额</th>
                    <th class="col-amount">贷方金额</th>
                    <th class="col-summary">摘要</th>
                    <th class="col-status">状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="col-date">
                        <span class="uf-financial-date">2025-06-11</span>
                    </td>
                    <td class="col-voucher">
                        <span class="uf-voucher-number">PZ20250611001</span>
                    </td>
                    <td class="col-subject-code">
                        <span class="uf-subject-code">1201</span>
                    </td>
                    <td class="col-subject-name">
                        <span class="uf-subject-name">原材料</span>
                    </td>
                    <td class="col-amount">
                        <span class="uf-financial-amount">¥68,000.00</span>
                    </td>
                    <td class="col-amount">
                        <span class="uf-financial-amount zero">¥0.00</span>
                    </td>
                    <td class="col-summary">
                        <span class="uf-financial-summary">采购食材原料</span>
                    </td>
                    <td class="col-status">
                        <span class="uf-financial-status approved">已审核</span>
                    </td>
                </tr>
                <tr>
                    <td class="col-date">
                        <span class="uf-financial-date">2025-06-11</span>
                    </td>
                    <td class="col-voucher">
                        <span class="uf-voucher-number">PZ20250611001</span>
                    </td>
                    <td class="col-subject-code">
                        <span class="uf-subject-code">2001</span>
                    </td>
                    <td class="col-subject-name">
                        <span class="uf-subject-name">应付账款</span>
                    </td>
                    <td class="col-amount">
                        <span class="uf-financial-amount zero">¥0.00</span>
                    </td>
                    <td class="col-amount">
                        <span class="uf-financial-amount">¥68,000.00</span>
                    </td>
                    <td class="col-summary">
                        <span class="uf-financial-summary">应付供应商货款</span>
                    </td>
                    <td class="col-status">
                        <span class="uf-financial-status approved">已审核</span>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr style="background: #f8f9fa; font-weight: 600;">
                    <td colspan="4" style="text-align: right;">合计：</td>
                    <td class="col-amount">
                        <span class="uf-financial-amount" style="font-weight: 600;">¥68,000.00</span>
                    </td>
                    <td class="col-amount">
                        <span class="uf-financial-amount" style="font-weight: 600;">¥68,000.00</span>
                    </td>
                    <td colspan="2"></td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

<!-- 财务分页演示 -->
<div class="uf-financial-pagination">
    <button class="uf-financial-btn">上一页</button>
    <span class="uf-financial-page-info">第 1 页，共 5 页，总计 48 条记录</span>
    <button class="uf-financial-btn">下一页</button>
</div>

{% endblock %}

{% block financial_css %}
<style>
/* 演示页面特定样式 */
.uf-card {
    margin-bottom: 20px;
}

h6 {
    font-size: 12px;
    font-weight: 600;
    color: var(--uf-primary);
    margin-bottom: 8px;
}

p {
    margin-bottom: 6px;
    font-size: 12px;
}

/* 演示页面特定样式 */
.uf-card {
    margin-bottom: 20px;
}

h6 {
    font-size: 12px;
    font-weight: 600;
    color: var(--uf-primary);
    margin-bottom: 8px;
}

p {
    margin-bottom: 6px;
    font-size: 12px;
}
</style>
{% endblock %}
