{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">陪餐记录信息</h6>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.csrf_token }}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="companion_name">陪餐人姓名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="companion_name" name="companion_name" value="{{ companion.companion_name }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="companion_role">陪餐人角色 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="companion_role" name="companion_role" value="{{ companion.companion_role }}" placeholder="如：校长、主任、教师等" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="meal_type">餐次 <span class="text-danger">*</span></label>
                            <select class="form-control" id="meal_type" name="meal_type" required>
                                <option value="breakfast" {% if companion.meal_type == 'breakfast' %}selected{% endif %}>早餐</option>
                                <option value="lunch" {% if companion.meal_type == 'lunch' %}selected{% endif %}>午餐</option>
                                <option value="dinner" {% if companion.meal_type == 'dinner' %}selected{% endif %}>晚餐</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="dining_date">陪餐日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="dining_date" name="dining_date" value="{{ companion.dining_time.strftime('%Y-%m-%d') if companion.dining_time else '' }}" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="dining_time">陪餐时间 <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="dining_time" name="dining_time" value="{{ companion.dining_time.strftime('%H:%M') if companion.dining_time else '12:00' }}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="taste_rating">口味评分 (1-5星)</label>
                            <select class="form-control" id="taste_rating" name="taste_rating">
                                <option value="">请选择</option>
                                <option value="1" {% if companion.taste_rating == 1 %}selected{% endif %}>1星</option>
                                <option value="2" {% if companion.taste_rating == 2 %}selected{% endif %}>2星</option>
                                <option value="3" {% if companion.taste_rating == 3 %}selected{% endif %}>3星</option>
                                <option value="4" {% if companion.taste_rating == 4 %}selected{% endif %}>4星</option>
                                <option value="5" {% if companion.taste_rating == 5 %}selected{% endif %}>5星</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="hygiene_rating">卫生评分 (1-5星)</label>
                            <select class="form-control" id="hygiene_rating" name="hygiene_rating">
                                <option value="">请选择</option>
                                <option value="1" {% if companion.hygiene_rating == 1 %}selected{% endif %}>1星</option>
                                <option value="2" {% if companion.hygiene_rating == 2 %}selected{% endif %}>2星</option>
                                <option value="3" {% if companion.hygiene_rating == 3 %}selected{% endif %}>3星</option>
                                <option value="4" {% if companion.hygiene_rating == 4 %}selected{% endif %}>4星</option>
                                <option value="5" {% if companion.hygiene_rating == 5 %}selected{% endif %}>5星</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="service_rating">服务评分 (1-5星)</label>
                            <select class="form-control" id="service_rating" name="service_rating">
                                <option value="">请选择</option>
                                <option value="1" {% if companion.service_rating == 1 %}selected{% endif %}>1星</option>
                                <option value="2" {% if companion.service_rating == 2 %}selected{% endif %}>2星</option>
                                <option value="3" {% if companion.service_rating == 3 %}selected{% endif %}>3星</option>
                                <option value="4" {% if companion.service_rating == 4 %}selected{% endif %}>4星</option>
                                <option value="5" {% if companion.service_rating == 5 %}selected{% endif %}>5星</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="comments">评价意见</label>
                    <textarea class="form-control" id="comments" name="comments" rows="3">{{ companion.comments or '' }}</textarea>
                </div>

                <div class="mb-3">
                    <label for="suggestions">改进建议</label>
                    <textarea class="form-control" id="suggestions" name="suggestions" rows="3">{{ companion.suggestions or '' }}</textarea>
                </div>

                <div class="mb-3">
                    <label for="photos">添加更多照片 (可多选)</label>
                    <div id="photos"
                         data-companion-id="{{ companion.id }}"
                         data-api-base-url="/daily-management/api/v2">
                    </div>
                    <small class="form-text text-muted">可以上传多张照片，支持jpg、png、gif等格式</small>
                </div>

                {% if photos %}
                <div class="mb-3">
                    <label>已上传照片</label>
                    <div class="row">
                        {% for photo in photos %}
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <img src="{{ photo.file_path }}" class="card-img-top" alt="陪餐照片">
                                    <div class="card-body p-2 text-center">
                                        <button type="button" class="btn btn-sm btn-danger delete-photo" data-photo-id="{{ photo.id }}">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <div class="mb-3 text-center mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存更改
                    </button>
                    <a href="{{ url_for('daily_management.view_companion', companion_id=companion.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回详情
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/companion-uploader.js') }}?v=1.0.0"></script>
{% endblock %}
