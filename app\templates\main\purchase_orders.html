{% extends 'base.html' %}

{% block title %}采购订单 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        </div>
    <div class="col-md-4 text-end">
        <a href="#" class="btn btn-primary">
            <i class="fas fa-plus"></i> 创建订单
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>供应商</th>
                        <th>总金额</th>
                        <th>订单日期</th>
                        <th>预计送达</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders.items %}
                    <tr>
                        <td>{{ order.id }}</td>
                        <td>{{ order.supplier.name }}</td>
                        <td>¥{{ order.total_amount }}</td>
                        <td>{{  order.order_date|format_datetime('%Y-%m-%d')  }}</td>
                        <td>{{  order.delivery_date|format_datetime('%Y-%m-%d') if order.delivery_date else '未设置'  }}</td>
                        <td>
                            {% if order.status == '待审核' %}
                            <span class="badge bg-warning">{{ order.status }}</span>
                            {% elif order.status == '已发货' %}
                            <span class="badge bg-info">{{ order.status }}</span>
                            {% elif order.status == '已完成' %}
                            <span class="badge bg-success">{{ order.status }}</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ order.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="#" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">暂无订单数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% if orders.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                <li class="page-item {% if not orders.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.purchase_orders', page=orders.prev_num) if orders.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in orders.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == orders.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('main.purchase_orders', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not orders.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.purchase_orders', page=orders.next_num) if orders.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}
