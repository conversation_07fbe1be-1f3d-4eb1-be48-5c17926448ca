{% extends 'base.html' %}

{% block title %}供应商管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <!-- 桌面端布局 -->
                    <div class="d-flex justify-content-between align-items-center desktop-only">
                        <div>
                            {% if current_area %}
                            <small class="text-muted">
                                当前区域:
                                {% for area in area_path %}
                                <span class="badge bg-info">{{ area.get_level_name() }} - {{ area.name }}</span>
                                {% if not loop.last %} <i class="fas fa-chevron-right"></i> {% endif %}
                                {% endfor %}
                            </small>
                            {% endif %}
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加供应商
                            </a>
                            <a href="{{ url_for('supplier_category.index') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                            <a href="{{ url_for('supplier_school.index') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-school"></i> 学校绑定管理
                            </a>
                        </div>
                    </div>

                    <!-- 移动端布局 -->
                    <div class="mobile-only">
                        <div class="row">
                            <div class="col-12">
                                {% if current_area %}
                                <div class="mb-3">
                                    <small class="text-muted d-block mb-2">当前区域:</small>
                                    {% for area in area_path %}
                                    <span class="badge bg-info">{{ area.get_level_name() }} - {{ area.name }}</span>
                                    {% if not loop.last %} <i class="fas fa-chevron-right"></i> {% endif %}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="action-buttons">
                                    <a href="{{ url_for('supplier.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> 添加供应商
                                    </a>
                                    <a href="{{ url_for('supplier_category.index') }}" class="btn btn-info">
                                        <i class="fas fa-tags"></i> 分类管理
                                    </a>
                                    <a href="{{ url_for('supplier_school.index') }}" class="btn btn-success">
                                        <i class="fas fa-school"></i> 学校绑定管理
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-lg-4 col-md-6 col-12 mobile-mb-2">
                                <div class="mb-3">
                                    <label for="category_id">供应商分类</label>
                                    <select class="form-control" id="category_id" name="category_id">
                                        <option value="">-- 所有分类 --</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-12 mobile-mb-2">
                                <div class="mb-3">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" value="{{ keyword }}" placeholder="供应商名称">
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-12 col-12">
                                <div class="mb-3">
                                    <label class="d-none d-md-block">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 桌面端供应商列表 -->
                    <div class="table-responsive desktop-only">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>供应商名称</th>
                                    <th>分类</th>
                                    <th>联系人</th>
                                    <th>联系电话</th>
                                    <th>合作学校</th>
                                    <th>评级</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in suppliers %}
                                <tr>
                                    <td>{{ supplier.id }}</td>
                                    <td>{{ supplier.name }}</td>
                                    <td>{{ supplier.category.name if supplier.category else '-' }}</td>
                                    <td>{{ supplier.contact_person }}</td>
                                    <td>{{ supplier.phone }}</td>
                                    <td>
                                        {% if supplier.accessible_relations %}
                                            {% for relation in supplier.accessible_relations %}
                                                {% if relation.status == 1 %}
                                                <span class="badge bg-success mb-1">{{ relation.area.name }}</span><br>
                                                {% endif %}
                                            {% endfor %}
                                        {% else %}
                                        <span class="text-muted">暂无合作学校</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if supplier.rating %}
                                        <div class="text-warning">
                                            {% for i in range(supplier.rating|int) %}
                                            <i class="fas fa-star"></i>
                                            {% endfor %}
                                            {% if supplier.rating % 1 > 0 %}
                                            <i class="fas fa-star-half-alt"></i>
                                            {% endif %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">暂无评级</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if supplier.status == 1 %}
                                        <span class="badge bg-success">合作中</span>
                                        {% else %}
                                        <span class="badge bg-secondary">已停用</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('supplier.view', id=supplier.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('supplier.edit', id=supplier.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ supplier.id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">暂无供应商数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 移动端供应商卡片 -->
                    <div class="mobile-only">
                        {% for supplier in suppliers %}
                        <div class="card mb-3 border-start-{% if supplier.status == 1 %}success{% else %}secondary{% endif %}">
                            <div class="card-body py-2">
                                <div class="row">
                                    <div class="col-8">
                                        <h6 class="mb-1">{{ supplier.name }}</h6>
                                        <small class="text-muted">ID: {{ supplier.id }} | {{ supplier.category.name if supplier.category else '未分类' }}</small>
                                    </div>
                                    <div class="col-4 text-end">
                                        {% if supplier.status == 1 %}
                                        <span class="badge bg-success">合作中</span>
                                        {% else %}
                                        <span class="badge bg-secondary">已停用</span>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">联系人</small>
                                        <div class="small">{{ supplier.contact_person or '-' }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">联系电话</small>
                                        <div class="small">{{ supplier.phone or '-' }}</div>
                                    </div>
                                </div>

                                {% if supplier.rating %}
                                <div class="row mt-1">
                                    <div class="col-12">
                                        <small class="text-muted">评级</small>
                                        <div class="text-warning">
                                            {% for i in range(supplier.rating|int) %}
                                            <i class="fas fa-star"></i>
                                            {% endfor %}
                                            {% if supplier.rating % 1 > 0 %}
                                            <i class="fas fa-star-half-alt"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                {% if supplier.accessible_relations %}
                                <div class="row mt-1">
                                    <div class="col-12">
                                        <small class="text-muted">合作学校</small>
                                        <div>
                                            {% for relation in supplier.accessible_relations %}
                                                {% if relation.status == 1 %}
                                                <span class="badge bg-success mb-1">{{ relation.area.name }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="btn-group btn-group-sm w-100" role="group">
                                            <a href="{{ url_for('supplier.view', id=supplier.id) }}" class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('supplier.edit', id=supplier.id) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-btn" data-id="{{ supplier.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5>暂无供应商数据</h5>
                            <p class="text-muted">您可以添加新的供应商或调整筛选条件</p>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier.index', page=pagination.prev_num, category_id=category_id, keyword=keyword) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('supplier.index', page=page, category_id=category_id, keyword=keyword) }}">{{ page }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier.index', page=pagination.next_num, category_id=category_id, keyword=keyword) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个供应商吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal.show();
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("supplier.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal.hide();
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal.hide();
                    }
                });
            }
        });
    });
</script>
{% endblock %}
