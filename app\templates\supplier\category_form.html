{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST">                        {{ form.hidden_tag() }}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.name.label }}
                                    {{ form.name(class="form-control") }}
                                    {% for error in form.name.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="mb-3">
                                    {{ form.description.label }}
                                    {{ form.description(class="form-control", rows=4) }}
                                    {% for error in form.description.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="mb-3">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('supplier_category.index') }}" class="btn btn-secondary">取消</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
