{% macro data_visualization_cards() %}
<!-- 数据可视化卡片 -->
<div class="row">
    <!-- 就餐人数统计卡片 -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <!-- 卡片头部 -->
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 fw-bold text-primary">就餐人数统计</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                        <div class="dropdown-header">图表操作:</div>
                        <a class="dropdown-item" href="#" id="refreshDinersChart">
                            <i class="fas fa-sync-alt fa-sm fa-fw me-2 text-gray-400"></i>刷新数据
                        </a>
                        <a class="dropdown-item" href="#" id="exportDinersData">
                            <i class="fas fa-download fa-sm fa-fw me-2 text-gray-400"></i>导出数据
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#" id="dinersChartOptions">
                            <i class="fas fa-cog fa-sm fa-fw me-2 text-gray-400"></i>图表设置
                        </a>
                    </div>
                </div>
            </div>
            <!-- 卡片内容 -->
            <div class="card-body">
                <div class="chart-filters mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dinersDateRange">日期范围</label>
                                <select class="form-control" id="dinersDateRange">
                                    <option value="7">最近7天</option>
                                    <option value="30" selected>最近30天</option>
                                    <option value="90">最近3个月</option>
                                    <option value="180">最近6个月</option>
                                    <option value="custom">自定义...</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dinersChartType">图表类型</label>
                                <select class="form-control" id="dinersChartType">
                                    <option value="bar" selected>柱状图</option>
                                    <option value="line">折线图</option>
                                    <option value="stacked">堆叠图</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="dinersChart"></canvas>
                </div>
                <div class="mt-3 text-center small">
                    <span class="me-2">
                        <i class="fas fa-circle text-primary"></i> 学生
                    </span>
                    <span class="me-2">
                        <i class="fas fa-circle text-success"></i> 教师
                    </span>
                    <span class="me-2">
                        <i class="fas fa-circle text-info"></i> 其他
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 检查记录统计卡片 -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <!-- 卡片头部 -->
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 fw-bold text-primary">检查记录统计</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                        <div class="dropdown-header">图表操作:</div>
                        <a class="dropdown-item" href="#" id="refreshInspectionChart">
                            <i class="fas fa-sync-alt fa-sm fa-fw me-2 text-gray-400"></i>刷新数据
                        </a>
                        <a class="dropdown-item" href="#" id="exportInspectionData">
                            <i class="fas fa-download fa-sm fa-fw me-2 text-gray-400"></i>导出数据
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#" id="inspectionChartOptions">
                            <i class="fas fa-cog fa-sm fa-fw me-2 text-gray-400"></i>图表设置
                        </a>
                    </div>
                </div>
            </div>
            <!-- 卡片内容 -->
            <div class="card-body">
                <div class="chart-filters mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectionDateRange">日期范围</label>
                                <select class="form-control" id="inspectionDateRange">
                                    <option value="7">最近7天</option>
                                    <option value="30" selected>最近30天</option>
                                    <option value="90">最近3个月</option>
                                    <option value="180">最近6个月</option>
                                    <option value="custom">自定义...</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectionChartType">图表类型</label>
                                <select class="form-control" id="inspectionChartType">
                                    <option value="bar" selected>柱状图</option>
                                    <option value="pie">饼图</option>
                                    <option value="doughnut">环形图</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="inspectionChart"></canvas>
                </div>
                <div class="mt-3 text-center small">
                    <span class="me-2">
                        <i class="fas fa-circle text-success"></i> 正常
                    </span>
                    <span class="me-2">
                        <i class="fas fa-circle text-danger"></i> 异常
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 自定义日期范围模态框 -->
<div class="modal fade" id="customDateRangeModal" tabindex="-1" role="dialog" aria-labelledby="customDateRangeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customDateRangeModalLabel">自定义日期范围</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="startDate">开始日期</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="mb-3">
                    <label for="endDate">结束日期</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="applyCustomDateRange">应用</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载Chart.js和自定义图表脚本 -->
<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否已加载Chart.js
        if (typeof Chart === 'undefined') {
            // 加载Chart.js
            const chartScript = document.createElement('script');
            chartScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";
            chartScript.onload = loadCustomCharts;
            document.head.appendChild(chartScript);
        } else {
            loadCustomCharts();
        }
        
        function loadCustomCharts() {
            // 加载自定义图表脚本
            const customChartScript = document.createElement('script');
            customChartScript.src = "{{ url_for('static', filename='js/daily-management-charts.js') }}";
            customChartScript.onload = initCharts;
            document.head.appendChild(customChartScript);
        }
        
        function initCharts() {
            // 初始化图表
            const charts = new DailyManagementCharts();
            charts.initAllCharts();
            
            // 绑定事件
            bindChartEvents(charts);
        }
        
        function bindChartEvents(charts) {
            // 刷新就餐人数图表
            document.getElementById('refreshDinersChart')?.addEventListener('click', function() {
                charts.initDinersChart();
            });
            
            // 刷新检查记录图表
            document.getElementById('refreshInspectionChart')?.addEventListener('click', function() {
                charts.initInspectionChart();
            });
            
            // 就餐人数日期范围变化
            document.getElementById('dinersDateRange')?.addEventListener('change', function() {
                if (this.value === 'custom') {
                    $('#customDateRangeModal').modal.show();
                    document.getElementById('applyCustomDateRange').dataset.target = 'diners';
                } else {
                    const days = parseInt(this.value);
                    charts.initDinersChart('dinersChart', {
                        start_date: charts.getDateString(-days),
                        end_date: charts.getDateString(0)
                    });
                }
            });
            
            // 检查记录日期范围变化
            document.getElementById('inspectionDateRange')?.addEventListener('change', function() {
                if (this.value === 'custom') {
                    $('#customDateRangeModal').modal.show();
                    document.getElementById('applyCustomDateRange').dataset.target = 'inspection';
                } else {
                    const days = parseInt(this.value);
                    charts.initInspectionChart('inspectionChart', {
                        start_date: charts.getDateString(-days),
                        end_date: charts.getDateString(0)
                    });
                }
            });
            
            // 应用自定义日期范围
            document.getElementById('applyCustomDateRange')?.addEventListener('click', function() {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                
                if (!startDate || !endDate) {
                    alert('请选择开始和结束日期');
                    return;
                }
                
                const target = this.dataset.target;
                if (target === 'diners') {
                    charts.initDinersChart('dinersChart', {
                        start_date: startDate,
                        end_date: endDate
                    });
                } else if (target === 'inspection') {
                    charts.initInspectionChart('inspectionChart', {
                        start_date: startDate,
                        end_date: endDate
                    });
                }
                
                $('#customDateRangeModal').modal.hide();
            });
        }
    });
</script>
{% endmacro %}
