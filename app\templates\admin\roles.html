{% extends 'base.html' %}

{% block title %}角色管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        </div>
    <div class="col-md-4 text-end">
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-plus"></i> 添加角色
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="{{ url_for('system.role_templates') }}">
                    <i class="fas fa-copy"></i> 使用角色模板
                </a>
                <a class="dropdown-item" href="{{ url_for('system.add_role') }}">
                    <i class="fas fa-file"></i> 创建空白角色
                </a>
            </div>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-key"></i> 权限工具
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="{{ url_for('permission_migration.index') }}">
                    <i class="fas fa-sync-alt"></i> 权限迁移工具
                </a>
                <a class="dropdown-item" href="{{ url_for('permission_audit.index') }}">
                    <i class="fas fa-search"></i> 权限审计工具
                </a>
                <a class="dropdown-item" href="{{ url_for('role_permissions_fix.fix_all_role_permissions') }}">
                    <i class="fas fa-tools"></i> 修复所有角色权限
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{{ url_for('system.permission_help') }}" target="_blank">
                    <i class="fas fa-question-circle"></i> 权限配置帮助
                </a>
            </div>
        </div>
        <a href="{{ url_for('system.users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回用户管理
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-body">
        <form id="searchForm" class="form-inline mb-3">
            <div class="mb-3 me-2">
                <input type="text" id="searchInput" class="form-control" placeholder="搜索角色名称...">
            </div>
            <button type="button" id="searchBtn" class="btn btn-primary">
                <i class="fas fa-search"></i> 搜索
            </button>
            <button type="button" id="resetBtn" class="btn btn-secondary ms-2">
                <i class="fas fa-sync"></i> 重置
            </button>
        </form>
    </div>
</div>

<div class="accordion" id="roleAccordion">
    <!-- 系统管理角色 -->
    <div class="card">
        <div class="card-header bg-primary text-white" id="headingSystem">
            <h5 class="mb-0">
                <button class="btn btn-link text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSystem" aria-expanded="true" aria-controls="collapseSystem">
                    <i class="fas fa-cogs"></i> 系统管理角色
                </button>
                <span class="badge bg-light float-end">
                    {{  roles|selectattr('name', 'in', ['系统管理员', '超级管理员', '管理员'])|list|length  }} 个角色
                </span>
            </h5>
        </div>

        <div id="collapseSystem" class="collapse show" aria-labelledby="headingSystem" data-bs-parent="#roleAccordion">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>角色名称</th>
                                <th>描述</th>
                                <th>用户数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles if role.name in ['系统管理员', '超级管理员', '管理员'] %}
                            <tr>
                                <td>{{ role.id }}</td>
                                <td>{{ role.name }}</td>
                                <td>{{ role.description or '-' }}</td>
                                <td>{{ role.users.count() }}</td>
                                <td>
                                    <a href="{{ url_for('system.view_role', id=role.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                    <a href="{{ url_for('system.edit_role', id=role.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-key"></i> 权限
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无系统管理角色</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 教育行政角色 -->
    <div class="card">
        <div class="card-header bg-success text-white" id="headingEducation">
            <h5 class="mb-0">
                <button class="btn btn-link text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseEducation" aria-expanded="false" aria-controls="collapseEducation">
                    <i class="fas fa-university"></i> 教育行政角色
                </button>
                <span class="badge bg-light float-end">
                    {{  roles|selectattr('name', 'in', ['教育局局长', '教育局副局长', '教育局督导室主任', '教育局财务科长', '教育局安全科长', '教育局数据分析员', '教育局督学'])|list|length  }} 个角色
                </span>
            </h5>
        </div>
        <div id="collapseEducation" class="collapse" aria-labelledby="headingEducation" data-bs-parent="#roleAccordion">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>角色名称</th>
                                <th>描述</th>
                                <th>用户数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles if role.name in ['教育局局长', '教育局副局长', '教育局督导室主任', '教育局财务科长', '教育局安全科长', '教育局数据分析员', '教育局督学'] %}
                            <tr>
                                <td>{{ role.id }}</td>
                                <td>{{ role.name }}</td>
                                <td>{{ role.description or '-' }}</td>
                                <td>{{ role.users.count() }}</td>
                                <td>
                                    <a href="{{ url_for('system.view_role', id=role.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                    <a href="{{ url_for('system.edit_role', id=role.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-key"></i> 权限
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteRoleModal{{ role.id }}">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>

                                    <!-- 删除角色确认模态框 -->
                                    <div class="modal fade" id="deleteRoleModal{{ role.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteRoleModalLabel{{ role.id }}" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header bg-danger text-white">
                                                    <h5 class="modal-title" id="deleteRoleModalLabel{{ role.id }}">确认删除角色</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>您确定要删除角色 <strong>{{ role.name }}</strong> 吗？</p>
                                                    <div class="alert alert-warning">
                                                        <i class="fas fa-exclamation-triangle"></i> 警告：
                                                        <ul>
                                                            <li>此操作不可逆，删除后无法恢复</li>
                                                            <li>如果角色已关联用户，将无法删除</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                    <form action="{{ url_for('system.delete_role', id=role.id) }}" method="POST">
                                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"><button type="submit" class="btn btn-danger">确认删除</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无教育行政角色</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 学校管理角色 -->
    <div class="card">
        <div class="card-header bg-info text-white" id="headingSchool">
            <h5 class="mb-0">
                <button class="btn btn-link text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSchool" aria-expanded="false" aria-controls="collapseSchool">
                    <i class="fas fa-school"></i> 学校管理角色
                </button>
                <span class="badge bg-light float-end">
                    {{  roles|selectattr('name', 'in', ['学校管理员'])|list|length  }} 个角色
                </span>
            </h5>
        </div>
        <div id="collapseSchool" class="collapse" aria-labelledby="headingSchool" data-bs-parent="#roleAccordion">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>角色名称</th>
                                <th>描述</th>
                                <th>用户数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles if role.name in ['学校管理员'] %}
                            <tr>
                                <td>{{ role.id }}</td>
                                <td>{{ role.name }}</td>
                                <td>{{ role.description or '-' }}</td>
                                <td>{{ role.users.count() }}</td>
                                <td>
                                    <a href="{{ url_for('system.view_role', id=role.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                    <a href="{{ url_for('system.edit_role', id=role.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-key"></i> 权限
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteRoleModal{{ role.id }}">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>

                                    <!-- 删除角色确认模态框 -->
                                    <div class="modal fade" id="deleteRoleModal{{ role.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteRoleModalLabel{{ role.id }}" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header bg-danger text-white">
                                                    <h5 class="modal-title" id="deleteRoleModalLabel{{ role.id }}">确认删除角色</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>您确定要删除角色 <strong>{{ role.name }}</strong> 吗？</p>
                                                    <div class="alert alert-warning">
                                                        <i class="fas fa-exclamation-triangle"></i> 警告：
                                                        <ul>
                                                            <li>此操作不可逆，删除后无法恢复</li>
                                                            <li>如果角色已关联用户，将无法删除</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                    <form action="{{ url_for('system.delete_role', id=role.id) }}" method="POST"><button type="submit" class="btn btn-danger">确认删除</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无学校管理角色</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 食堂管理角色 -->
    <div class="card">
        <div class="card-header bg-warning text-dark" id="headingCanteen">
            <h5 class="mb-0">
                <button class="btn btn-link text-dark" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCanteen" aria-expanded="false" aria-controls="collapseCanteen">
                    <i class="fas fa-utensils"></i> 食堂管理角色
                </button>
                <span class="badge bg-dark float-end">
                    {{  roles|selectattr('name', 'in', ['食堂主管', '食堂管理员', '采购员', '库存管理员', '菜单管理员', '食品安全员', '财务管理员'])|list|length  }} 个角色
                </span>
            </h5>
        </div>
        <div id="collapseCanteen" class="collapse" aria-labelledby="headingCanteen" data-bs-parent="#roleAccordion">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>角色名称</th>
                                <th>描述</th>
                                <th>用户数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles if role.name in ['食堂主管', '食堂管理员', '采购员', '库存管理员', '菜单管理员', '食品安全员', '财务管理员'] %}
                            <tr>
                                <td>{{ role.id }}</td>
                                <td>{{ role.name }}</td>
                                <td>{{ role.description or '-' }}</td>
                                <td>{{ role.users.count() }}</td>
                                <td>
                                    <a href="{{ url_for('system.view_role', id=role.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                    <a href="{{ url_for('system.edit_role', id=role.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-key"></i> 权限
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteRoleModal{{ role.id }}">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>

                                    <!-- 删除角色确认模态框 -->
                                    <div class="modal fade" id="deleteRoleModal{{ role.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteRoleModalLabel{{ role.id }}" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header bg-danger text-white">
                                                    <h5 class="modal-title" id="deleteRoleModalLabel{{ role.id }}">确认删除角色</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>您确定要删除角色 <strong>{{ role.name }}</strong> 吗？</p>
                                                    <div class="alert alert-warning">
                                                        <i class="fas fa-exclamation-triangle"></i> 警告：
                                                        <ul>
                                                            <li>此操作不可逆，删除后无法恢复</li>
                                                            <li>如果角色已关联用户，将无法删除</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                    <form action="{{ url_for('system.delete_role', id=role.id) }}" method="POST"><button type="submit" class="btn btn-danger">确认删除</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无食堂管理角色</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 其他角色 -->
    <div class="card">
        <div class="card-header bg-secondary text-white" id="headingOther">
            <h5 class="mb-0">
                <button class="btn btn-link text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOther" aria-expanded="false" aria-controls="collapseOther">
                    <i class="fas fa-users"></i> 其他角色
                </button>
                <span class="badge bg-light float-end">
                    {{  roles|rejectattr('name', 'in', ['系统管理员', '超级管理员', '管理员', '教育局局长', '教育局副局长', '教育局督导室主任', '教育局财务科长', '教育局安全科长', '教育局数据分析员', '教育局督学', '学校管理员', '食堂主管', '食堂管理员', '采购员', '库存管理员', '菜单管理员', '食品安全员', '财务管理员'])|list|length  }} 个角色
                </span>
            </h5>
        </div>
        <div id="collapseOther" class="collapse" aria-labelledby="headingOther" data-bs-parent="#roleAccordion">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>角色名称</th>
                                <th>描述</th>
                                <th>用户数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles if role.name not in ['系统管理员', '超级管理员', '管理员', '教育局局长', '教育局副局长', '教育局督导室主任', '教育局财务科长', '教育局安全科长', '教育局数据分析员', '教育局督学', '学校管理员', '食堂主管', '食堂管理员', '采购员', '库存管理员', '菜单管理员', '食品安全员', '财务管理员'] %}
                            <tr>
                                <td>{{ role.id }}</td>
                                <td>{{ role.name }}</td>
                                <td>{{ role.description or '-' }}</td>
                                <td>{{ role.users.count() }}</td>
                                <td>
                                    <a href="{{ url_for('system.view_role', id=role.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                    <a href="{{ url_for('system.edit_role', id=role.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-key"></i> 权限
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteRoleModal{{ role.id }}">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>

                                    <!-- 删除角色确认模态框 -->
                                    <div class="modal fade" id="deleteRoleModal{{ role.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteRoleModalLabel{{ role.id }}" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header bg-danger text-white">
                                                    <h5 class="modal-title" id="deleteRoleModalLabel{{ role.id }}">确认删除角色</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>您确定要删除角色 <strong>{{ role.name }}</strong> 吗？</p>
                                                    <div class="alert alert-warning">
                                                        <i class="fas fa-exclamation-triangle"></i> 警告：
                                                        <ul>
                                                            <li>此操作不可逆，删除后无法恢复</li>
                                                            <li>如果角色已关联用户，将无法删除</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                    <form action="{{ url_for('system.delete_role', id=role.id) }}" method="POST"><button type="submit" class="btn btn-danger">确认删除</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无其他角色</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 搜索功能
    $('#searchBtn').click(function() {
        filterRoles();
    });

    $('#searchInput').keyup(function(e) {
        if (e.keyCode === 13) {
            filterRoles();
        }
    });

    $('#resetBtn').click(function() {
        $('#searchInput').val('');
        $('table tbody tr').show();
    });

    function filterRoles() {
        var searchText = $('#searchInput').val().toLowerCase();
        $('table tbody tr').each(function() {
            var roleName = $(this).find('td:nth-child(2)').text().toLowerCase();
            var roleDesc = $(this).find('td:nth-child(3)').text().toLowerCase();

            if (roleName.includes(searchText) || roleDesc.includes(searchText)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    // 排序功能
    $('.sort-link').click(function(e) {
        e.preventDefault();
        var sortBy = $(this).data('sort');
        var tbody = $('table tbody');
        var rows = tbody.find('tr').toArray();

        rows.sort(function(a, b) {
            var aValue, bValue;

            if (sortBy === 'id') {
                aValue = parseInt($(a).find('td:nth-child(1)').text());
                bValue = parseInt($(b).find('td:nth-child(1)').text());
            } else if (sortBy === 'name') {
                aValue = $(a).find('td:nth-child(2)').text();
                bValue = $(b).find('td:nth-child(2)').text();
            } else if (sortBy === 'users') {
                aValue = parseInt($(a).find('td:nth-child(4)').text());
                bValue = parseInt($(b).find('td:nth-child(4)').text());
            }

            // 切换排序方向
            if ($(this).hasClass('asc')) {
                $(this).removeClass('asc').addClass('desc');
                return bValue > aValue ? 1 : -1;
            } else {
                $(this).removeClass('desc').addClass('asc');
                return aValue > bValue ? 1 : -1;
            }
        });

        // 更新表格
        $.each(rows, function(index, row) {
            tbody.append(row);
        });

        // 更新排序图标
        $('.sort-link').find('i').attr('class', 'fas fa-sort');
        if ($(this).hasClass('asc')) {
            $(this).find('i').attr('class', 'fas fa-sort-up');
        } else {
            $(this).find('i').attr('class', 'fas fa-sort-down');
        }
    });
});
</script>
{% endblock %}