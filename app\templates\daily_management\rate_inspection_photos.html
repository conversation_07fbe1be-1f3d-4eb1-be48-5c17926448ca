{% extends 'base.html' %}

{% block title %}评价检查照片{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .photo-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transition: transform 0.2s;
    }
    
    .photo-card:hover {
        transform: translateY(-5px);
    }
    
    .photo-img {
        width: 100%;
        height: 180px;
        object-fit: cover;
    }
    
    .photo-info {
        padding: 15px;
    }
    
    .photo-time {
        font-size: 0.8rem;
        color: #858796;
        margin-bottom: 10px;
    }
    
    .rating-container {
        margin-top: 10px;
    }
    
    .rating-stars {
        display: flex;
        gap: 5px;
    }
    
    .star {
        font-size: 24px;
        cursor: pointer;
        color: #ddd;
    }
    
    .star.active {
        color: #f6c23e;
    }
    
    .rating-label {
        margin-top: 5px;
        font-size: 0.9rem;
        color: #5a5c69;
    }
    
    .error-message {
        color: #e74a3b;
        font-size: 0.8rem;
        margin-top: 5px;
        display: none;
    }
    
    .success-message {
        color: #1cc88a;
        font-size: 0.8rem;
        margin-top: 5px;
        display: none;
    }
    
    .no-photos {
        grid-column: 1 / -1;
        text-align: center;
        padding: 50px;
        background-color: #f8f9fc;
        border-radius: 0.35rem;
    }
    
    .meal-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.35rem;
        margin-right: 5px;
    }
    
    .meal-morning {
        background-color: #4e73df;
        color: white;
    }
    
    .meal-noon {
        background-color: #1cc88a;
        color: white;
    }
    
    .meal-evening {
        background-color: #f6c23e;
        color: white;
    }

    .photo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .photo-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        overflow: hidden;
        /* box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15); */ /* 移除阴影效果 */
        transition: transform 0.2s;
    }
    
    .photo-card:hover {
        transform: translateY(-5px);
    }
    
    .photo-img {
        width: 100%;
        height: 180px;
        object-fit: cover;
    }
    
    .photo-info {
        padding: 15px;
    }
    
    .photo-time {
        font-size: 0.8rem;
        color: #858796;
        margin-bottom: 10px;
    }
    
    .rating-container {
        margin-top: 10px;
    }
    
    .rating-stars {
        display: flex;
        gap: 5px;
    }
    
    .star {
        font-size: 24px;
        cursor: pointer;
        color: #ddd;
    }
    
    .star.active {
        color: #f6c23e;
    }
    
    .rating-label {
        margin-top: 5px;
        font-size: 0.9rem;
        color: #5a5c69;
    }
    
    .error-message {
        color: #e74a3b;
        font-size: 0.8rem;
        margin-top: 5px;
        display: none;
    }
    
    .success-message {
        color: #1cc88a;
        font-size: 0.8rem;
        margin-top: 5px;
        display: none;
    }
    
    .no-photos {
        grid-column: 1 / -1;
        text-align: center;
        padding: 50px;
        background-color: #f8f9fc;
        border-radius: 0.35rem;
    }
    
    .meal-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.35rem;
        margin-right: 5px;
    }
    
    .meal-morning {
        background-color: #4e73df;
        color: white;
    }
    
    .meal-noon {
        background-color: #1cc88a;
        color: white;
    }
    
    .meal-evening {
        background-color: #f6c23e;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">
        {{ school.name }} - 评价检查照片
    </h1>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold text-primary">{{ log.log_date }} 检查照片评价</h6>
            <div>
                <a href="{{ url_for('daily_management.simplified_inspection', log_id=log.id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-arrow-left me-1"></i> 返回检查记录
                </a>
            </div>
        </div>
        <div class="card-body">
            <ul class="nav nav-tabs mb-4" id="photoTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="all-tab" data-bs-toggle="tab" href="#all" role="tab" aria-controls="all" aria-selected="true">
                        <i class="fas fa-images me-1"></i> 全部照片
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="morning-tab" data-bs-toggle="tab" href="#morning" role="tab" aria-controls="morning" aria-selected="false">
                        <i class="fas fa-sun me-1"></i> 早晨检查
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="noon-tab" data-bs-toggle="tab" href="#noon" role="tab" aria-controls="noon" aria-selected="false">
                        <i class="fas fa-cloud-sun me-1"></i> 中午检查
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="evening-tab" data-bs-toggle="tab" href="#evening" role="tab" aria-controls="evening" aria-selected="false">
                        <i class="fas fa-moon me-1"></i> 晚上检查
                    </a>
                </li>
            </ul>
            
            <div class="tab-content" id="photoTabsContent">
                <!-- 全部照片 -->
                <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                    {% if photos %}
                    <div class="photo-grid">
                        {% for photo in photos %}
                        <div class="photo-card">
                            <img src="{{ photo.file_path }}" alt="检查照片" class="photo-img">
                            <div class="photo-info">
                                <div class="photo-time">
                                    <span class="meal-badge meal-{{ photo.reference_type }}">
                                        {% if photo.reference_type == 'morning' %}早晨
                                        {% elif photo.reference_type == 'noon' %}中午
                                        {% elif photo.reference_type == 'evening' %}晚上
                                        {% endif %}
                                    </span>
                                    {{ photo.upload_time|format_datetime }}
                                </div>
                                <div class="rating-container" data-photo-id="{{ photo.id }}">
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                        <span class="star {% if photo.rating >= i %}active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                    <div class="rating-label" id="rating-label-{{ photo.id }}">
                                        {% if photo.rating %}当前评分: {{ photo.rating }} 星{% else %}未评分{% endif %}
                                    </div>
                                    <div class="error-message" id="error-message-{{ photo.id }}"></div>
                                    <div class="success-message" id="success-message-{{ photo.id }}"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="no-photos">
                        <i class="fas fa-image fa-3x mb-3 text-gray-300"></i>
                        <p class="text-gray-500">暂无检查照片</p>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 早晨检查 -->
                <div class="tab-pane fade" id="morning" role="tabpanel" aria-labelledby="morning-tab">
                    {% if morning_photos %}
                    <div class="photo-grid">
                        {% for photo in morning_photos %}
                        <div class="photo-card">
                            <img src="{{ photo.file_path }}" alt="早晨检查照片" class="photo-img">
                            <div class="photo-info">
                                <div class="photo-time">{{ photo.upload_time|format_datetime }}</div>
                                <div class="rating-container" data-photo-id="{{ photo.id }}">
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                        <span class="star {% if photo.rating >= i %}active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                    <div class="rating-label" id="rating-label-{{ photo.id }}">
                                        {% if photo.rating %}当前评分: {{ photo.rating }} 星{% else %}未评分{% endif %}
                                    </div>
                                    <div class="error-message" id="error-message-{{ photo.id }}"></div>
                                    <div class="success-message" id="success-message-{{ photo.id }}"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="no-photos">
                        <i class="fas fa-sun fa-3x mb-3 text-gray-300"></i>
                        <p class="text-gray-500">暂无早晨检查照片</p>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 中午检查 -->
                <div class="tab-pane fade" id="noon" role="tabpanel" aria-labelledby="noon-tab">
                    {% if noon_photos %}
                    <div class="photo-grid">
                        {% for photo in noon_photos %}
                        <div class="photo-card">
                            <img src="{{ photo.file_path }}" alt="中午检查照片" class="photo-img">
                            <div class="photo-info">
                                <div class="photo-time">{{ photo.upload_time|format_datetime }}</div>
                                <div class="rating-container" data-photo-id="{{ photo.id }}">
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                        <span class="star {% if photo.rating >= i %}active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                    <div class="rating-label" id="rating-label-{{ photo.id }}">
                                        {% if photo.rating %}当前评分: {{ photo.rating }} 星{% else %}未评分{% endif %}
                                    </div>
                                    <div class="error-message" id="error-message-{{ photo.id }}"></div>
                                    <div class="success-message" id="success-message-{{ photo.id }}"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="no-photos">
                        <i class="fas fa-cloud-sun fa-3x mb-3 text-gray-300"></i>
                        <p class="text-gray-500">暂无中午检查照片</p>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 晚上检查 -->
                <div class="tab-pane fade" id="evening" role="tabpanel" aria-labelledby="evening-tab">
                    {% if evening_photos %}
                    <div class="photo-grid">
                        {% for photo in evening_photos %}
                        <div class="photo-card">
                            <img src="{{ photo.file_path }}" alt="晚上检查照片" class="photo-img">
                            <div class="photo-info">
                                <div class="photo-time">{{ photo.upload_time|format_datetime }}</div>
                                <div class="rating-container" data-photo-id="{{ photo.id }}">
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                        <span class="star {% if photo.rating >= i %}active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                    <div class="rating-label" id="rating-label-{{ photo.id }}">
                                        {% if photo.rating %}当前评分: {{ photo.rating }} 星{% else %}未评分{% endif %}
                                    </div>
                                    <div class="error-message" id="error-message-{{ photo.id }}"></div>
                                    <div class="success-message" id="success-message-{{ photo.id }}"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="no-photos">
                        <i class="fas fa-moon fa-3x mb-3 text-gray-300"></i>
                        <p class="text-gray-500">暂无晚上检查照片</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化星级评分
        const ratingContainers = document.querySelectorAll('.rating-container');
        
        ratingContainers.forEach(container => {
            const photoId = container.dataset.photoId;
            const stars = container.querySelectorAll('.star');
            const ratingLabel = document.getElementById(`rating-label-${photoId}`);
            const errorMessage = document.getElementById(`error-message-${photoId}`);
            const successMessage = document.getElementById(`success-message-${photoId}`);
            
            // 鼠标悬停效果
            stars.forEach((star, index) => {
                star.addEventListener('mouseover', function() {
                    const value = parseInt(this.dataset.value);
                    
                    stars.forEach(s => {
                        if (parseInt(s.dataset.value) <= value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
                
                // 点击评分
                star.addEventListener('click', function() {
                    const value = parseInt(this.dataset.value);
                    
                    // 隐藏消息
                    errorMessage.style.display = 'none';
                    successMessage.style.display = 'none';
                    
                    // 发送评分请求
                    fetch('/api/v2/photos/' + photoId + '/rating', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ rating: value })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            errorMessage.textContent = data.error;
                            errorMessage.style.display = 'block';
                        } else {
                            // 更新评分标签
                            ratingLabel.textContent = `当前评分: ${value} 星`;
                            
                            // 显示成功消息
                            successMessage.textContent = '评分已更新';
                            successMessage.style.display = 'block';
                            
                            // 3秒后隐藏成功消息
                            setTimeout(() => {
                                successMessage.style.display = 'none';
                            }, 3000);
                        }
                    })
                    .catch(error => {
                        errorMessage.textContent = '评分失败，请重试';
                        errorMessage.style.display = 'block';
                    });
                });
            });
            
            // 鼠标离开时恢复当前评分显示
            container.addEventListener('mouseleave', function() {
                const currentRating = parseInt(ratingLabel.textContent.match(/\d+/) || 0);
                
                stars.forEach(s => {
                    if (parseInt(s.dataset.value) <= currentRating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
        });
    });
</script>
{% endblock %}
