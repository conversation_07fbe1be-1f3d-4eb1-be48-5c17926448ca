{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">{{ title }}</h3>
            <a href="{{ url_for('warehouse.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('warehouse.edit', id=warehouse.id) if warehouse else url_for('warehouse.create') }}" novalidate novalidate><div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name">仓库名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ warehouse.name if warehouse else '' }}" required>
                                </div>
                                
                                {% if areas %}
                                <div class="mb-3">
                                    <label for="area_id">所属区域 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="area_id" name="area_id" required>
                                        <option value="">-- 请选择区域 --</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if warehouse and warehouse.area_id == area.id %}selected{% endif %}>{{ area.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                {% endif %}

                                <div class="mb-3">
                                    <label for="location">位置 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="location" name="location" value="{{ warehouse.location if warehouse else '' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="manager_id">管理员 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="manager_id" name="manager_id" required>
                                        <option value="">-- 请选择管理员 --</option>
                                        {% for manager in managers %}
                                        <option value="{{ manager.id }}" {% if warehouse and warehouse.manager_id == manager.id %}selected{% endif %}>{{ manager.real_name or manager.username }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="capacity">容量</label>
                                    <input type="number" class="form-control" id="capacity" name="capacity" value="{{ warehouse.capacity if warehouse else '' }}" step="0.01">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="capacity_unit">容量单位</label>
                                    <input type="text" class="form-control" id="capacity_unit" name="capacity_unit" value="{{ warehouse.capacity_unit if warehouse else '平方米' }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="temperature_range">温度范围</label>
                                    <input type="text" class="form-control" id="temperature_range" name="temperature_range" value="{{ warehouse.temperature_range if warehouse else '' }}" placeholder="例如：15-25°C">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="humidity_range">湿度范围</label>
                                    <input type="text" class="form-control" id="humidity_range" name="humidity_range" value="{{ warehouse.humidity_range if warehouse else '' }}" placeholder="例如：40%-60%">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="status">状态 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="正常" {% if warehouse and warehouse.status == '正常' %}selected{% endif %}>正常</option>
                                        <option value="维护中" {% if warehouse and warehouse.status == '维护中' %}selected{% endif %}>维护中</option>
                                        <option value="已关闭" {% if warehouse and warehouse.status == '已关闭' %}selected{% endif %}>已关闭</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="notes">备注</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ warehouse.notes if warehouse else '' }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> 保存
                                </button>
                                <a href="{{ url_for('warehouse.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> 取消
                                </a>
                            </div>
                        </div>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
{% endblock %}
