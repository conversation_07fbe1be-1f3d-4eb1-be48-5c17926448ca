{% extends 'base.html' %}

{% block title %}日常健康检查 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>日常健康检查 ({{  today|format_datetime('%Y-%m-%d')  }})</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('employee.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回员工列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">已检查员工 ({{  checked_employees|length  }}人)</h5>
            </div>
            <div class="card-body">
                {% if checked_employees %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>员工姓名</th>
                                <th>部门</th>
                                <th>体温</th>
                                <th>健康状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in checked_employees %}
                            <tr>
                                <td>{{ item.employee.name }}</td>
                                <td>{{ item.employee.department }}</td>
                                <td>{{ item.check.temperature }}°C</td>
                                <td>
                                    {% if item.check.health_status == '正常' %}
                                    <span class="badge bg-success">{{ item.check.health_status }}</span>
                                    {% else %}
                                    <span class="badge bg-danger">{{ item.check.health_status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('employee.view_employee', id=item.employee.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 今日暂无已检查员工
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">待检查员工 ({{  unchecked_employees|length  }}人)</h5>
            </div>
            <div class="card-body">
                {% if unchecked_employees %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>员工姓名</th>
                                <th>部门</th>
                                <th>职位</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in unchecked_employees %}
                            <tr>
                                <td>{{ employee.name }}</td>
                                <td>{{ employee.department }}</td>
                                <td>{{ employee.position }}</td>
                                <td>
                                    <a href="{{ url_for('employee.add_health_check', employee_id=employee.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus"></i> 添加检查
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> 所有在职员工今日已完成健康检查
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">健康检查统计</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="text-primary">{{  checked_employees|length + unchecked_employees|length  }}</h3>
                        <p class="mb-0">在职员工总数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="text-success">{{  checked_employees|length  }}</h3>
                        <p class="mb-0">已检查员工数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="text-warning">{{  unchecked_employees|length  }}</h3>
                        <p class="mb-0">待检查员工数</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="text-success">{{  checked_employees|selectattr('check.health_status', 'eq', '正常')|list|length  }}</h3>
                        <p class="mb-0">健康状态正常</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="text-danger">{{  checked_employees|selectattr('check.health_status', 'eq', '异常')|list|length  }}</h3>
                        <p class="mb-0">健康状态异常</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
