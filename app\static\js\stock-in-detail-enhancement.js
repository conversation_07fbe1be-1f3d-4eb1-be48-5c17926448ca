/**
 * 入库单详情页面增强脚本
 * 提供更好的用户交互体验和视觉效果
 */

$(document).ready(function() {
    // 初始化页面增强功能
    initStockInDetailEnhancements();
});

function initStockInDetailEnhancements() {
    // 1. 表格行悬停效果增强
    enhanceTableHoverEffects();

    // 2. 文档操作按钮增强
    enhanceDocumentButtons();

    // 3. 批次号快速选择增强
    enhanceBatchNumberSelection();

    // 4. 文件上传区域增强
    enhanceFileUploadArea();

    // 5. 模态框动画增强
    enhanceModalAnimations();

    // 6. 状态提示增强
    enhanceStatusAlerts();

    // 7. 操作按钮状态管理
    enhanceActionButtons();
}

// 表格行悬停效果增强 - 简化版
function enhanceTableHoverEffects() {
    // 移除过度的悬停动画，保持简洁专业
    // 基础的CSS悬停效果已足够
}

// 文档操作按钮增强
function enhanceDocumentButtons() {
    // 查看按钮点击效果
    $('.document-table .btn-primary').on('click', function() {
        $(this).addClass('btn-clicked');
        setTimeout(() => {
            $(this).removeClass('btn-clicked');
        }, 200);
    });

    // 下载按钮点击效果
    $('.document-table .btn-success').on('click', function() {
        $(this).addClass('btn-clicked');
        setTimeout(() => {
            $(this).removeClass('btn-clicked');
        }, 200);

        // 显示下载提示
        showToast('success', '文档下载已开始');
    });

    // 删除按钮确认增强
    $('.document-table .btn-danger').on('click', function(e) {
        e.preventDefault();
        const button = $(this);

        Swal.fire({
            title: '确认删除',
            text: '确定要删除这个文档吗？此操作不可撤销。',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '确定删除',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                // 执行删除操作
                const docId = button.attr('onclick').match(/\d+/)[0];
                deleteDocument(docId);
            }
        });
    });
}

// 批次号快速选择增强
function enhanceBatchNumberSelection() {
    // 注意：批次号点击事件已通过HTML onclick属性处理，这里只添加视觉效果
    $('.badge.me-1.mb-1').on('click', function() {
        // 视觉反馈
        $(this).addClass('badge-selected');
        setTimeout(() => {
            $(this).removeClass('badge-selected');
        }, 1000);
    });
}

// 文件上传区域增强
function enhanceFileUploadArea() {
    const fileInput = $('#document');
    const fileLabel = $('.form-control-label');

    // 文件选择事件
    fileInput.on('change', function() {
        const fileName = $(this)[0].files[0]?.name || '选择文件...';
        fileLabel.text(fileName);

        // 验证文件类型
        if ($(this)[0].files[0]) {
            const file = $(this)[0].files[0];
            const allowedTypes = ['pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx', 'xls', 'xlsx'];
            const fileExt = file.name.split('.').pop().toLowerCase();

            if (!allowedTypes.includes(fileExt)) {
                showToast('error', '不支持的文件格式');
                $(this).val('');
                fileLabel.text('选择文件...');
                return;
            }

            // 验证文件大小 (10MB)
            if (file.size > 10 * 1024 * 1024) {
                showToast('error', '文件大小不能超过10MB');
                $(this).val('');
                fileLabel.text('选择文件...');
                return;
            }

            showToast('success', '文件选择成功');
        }
    });

    // 拖拽上传支持
    const uploadArea = $('.form-control');

    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('drag-over');
    });

    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('drag-over');
    });

    uploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('drag-over');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            fileInput[0].files = files;
            fileInput.trigger('change');
        }
    });
}

// 模态框动画增强
function enhanceModalAnimations() {
    $('.modal').on('show.bs.modal', function() {
        $(this).find('.modal-dialog').addClass('modal-show-animation');
    });

    $('.modal').on('hide.bs.modal', function() {
        $(this).find('.modal-dialog').removeClass('modal-show-animation');
    });
}

// 状态提示增强
function enhanceStatusAlerts() {
    // 为状态提示添加图标动画
    $('.alert .fas').addClass('alert-icon-animated');

    // 自动隐藏某些提示
    $('.alert-info').each(function() {
        const alert = $(this);
        if (alert.text().includes('操作提示')) {
            setTimeout(() => {
                alert.fadeOut(500);
            }, 10000); // 10秒后自动隐藏
        }
    });
}

// 操作按钮状态管理
function enhanceActionButtons() {
    // 确认入库按钮增强
    $('#stockInBtn').on('click', function() {
        const button = $(this);

        Swal.fire({
            title: '确认入库',
            text: '确定要执行入库操作吗？此操作将更新库存数据。',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '确认入库',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                // 显示加载状态
                button.prop('disabled', true);
                button.html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

                // 执行入库操作
                performStockIn();
            }
        });
    });

    // 撤销入库按钮增强
    $('#cancelStockInBtn').on('click', function() {
        const button = $(this);

        Swal.fire({
            title: '撤销入库',
            text: '确定要撤销入库操作吗？这将恢复库存数据到入库前状态。',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ffc107',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '确认撤销',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                // 显示加载状态
                button.prop('disabled', true);
                button.html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

                // 执行撤销操作
                cancelStockIn();
            }
        });
    });
}

// 显示提示消息
function showToast(type, message) {
    toastr[type](message);
}

// 执行入库操作
function performStockIn() {
    // 这里应该调用实际的入库API
    setTimeout(() => {
        showToast('success', '入库操作成功');
        location.reload();
    }, 2000);
}

// 执行撤销操作
function cancelStockIn() {
    // 这里应该调用实际的撤销API
    setTimeout(() => {
        showToast('success', '撤销操作成功');
        location.reload();
    }, 2000);
}

// CSS 动画类
const additionalStyles = `
<style>
/* 移除过度的表格动画效果 */

.btn-clicked {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

.badge-selected {
    background-color: #28a745 !important;
    transform: scale(1.1);
}

.drag-over {
    border-color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1) !important;
}

.modal-show-animation {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-icon-animated {
    animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}
</style>
`;

// 添加样式到页面
$('head').append(additionalStyles);
