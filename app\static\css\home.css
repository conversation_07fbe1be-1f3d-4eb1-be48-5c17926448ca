/* 首页样式 */
.hero-section {
    background: linear-gradient(120deg, #6a82fb 0%, #fc5c7d 100%);
    color: white;
    padding: 100px 0 60px 0;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.hero-section .wave-bg {
    position: absolute;
    left: 0; right: 0; bottom: -1px;
    width: 100%;
    z-index: 2;
    pointer-events: none;
}

.feature-highlight, .feature-item, .stat-item {
    background: #fff;
    border-radius: 16px;
    /* box-shadow: 0 2px 16px rgba(80,80,120,0.08); */ /* 移除阴影效果 */
    transition: box-shadow 0.3s, transform 0.3s;
    opacity: 0;
    transform: translateY(40px);
}

.feature-highlight:hover, .feature-item:hover, .stat-item:hover {
    /* box-shadow: 0 8px 32px rgba(80,80,120,0.18); */ /* 移除阴影效果 */
    transform: translateY(-8px) scale(1.04);
}

.feature-icon {
    font-size: 3.2rem;
    margin-bottom: 18px;
    color: #6a82fb;
    transition: color 0.3s;
}

.feature-highlight:hover .feature-icon {
    color: #fc5c7d;
}

.text-purple {
    color: #a259f7 !important;
}

.stats-section {
    background: linear-gradient(90deg, #f8f9fa 60%, #fceabb 100%);
    padding: 60px 0;
    margin: 0 0 50px 0;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: url('../img/stats-pattern.svg') center/cover;
    opacity: 0.04;
    z-index: 0;
}

.stat-item {
    text-align: center;
    padding: 20px;
    transition: transform 0.3s ease;
}

.stat-number {
    font-size: 2.8rem;
    font-weight: bold;
    color: #6a82fb;
    margin-bottom: 10px;
    transition: color 0.3s;
}

.stat-item:hover .stat-number {
    color: #fc5c7d;
}

.stat-label {
    color: #6c757d;
    font-size: 1.1rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .hero-section {
        padding: 50px 0 30px 0;
    }
    .feature-highlight, .feature-item, .stat-item {
        margin-bottom: 18px;
    }
    .stat-number {
        font-size: 2rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

/* 按钮样式优化 */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    z-index: 1;
}

.btn:active::after {
    content: '';
    position: absolute;
    left: 50%; top: 50%;
    width: 0; height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ripple 0.5s linear;
    z-index: 2;
}

@keyframes ripple {
    to {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

/* 加载状态 */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 滚动淡入动画 */
.aos-animate {
    opacity: 1 !important;
    transform: none !important;
    transition: opacity 0.7s cubic-bezier(.4,0,.2,1), transform 0.7s cubic-bezier(.4,0,.2,1);
} 