{% extends "base.html" %}

{% block title %}日常管理{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .overview-card {
        border-radius: 10px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transition: transform 0.3s;
    }

    .overview-card:hover {
        transform: translateY(-5px);
    }

    .overview-card .title {
        font-size: 0.9rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.8);
    }

    .overview-card .count {
        font-size: 1.8rem;
        font-weight: 700;
        margin-top: 5px;
    }

    .overview-card .icon {
        font-size: 2rem;
        opacity: 0.8;
    }

    .feature-card {
        transition: transform 0.3s;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .icon-box {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .status-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        border-radius: 10px;
        font-weight: 600;
    }

    .status-bg-success {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-bg-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .status-bg-info {
        background-color: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }

    .status-bg-danger {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .issue-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .issue-item {
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
        border-start: 4px solid #6c757d;
        background-color: #f8f9fa;
    }

    .issue-item.priority-high {
        border-start-color: #dc3545;
    }

    .issue-item.priority-medium {
        border-start-color: #ffc107;
    }

    .issue-item.priority-low {
        border-start-color: #28a745;
    }

    .issue-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .issue-meta {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .chart-container {
        position: relative;
        margin: auto;
    }

    .overview-card {
        border-radius: 10px;
        /* box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15); */ /* 移除阴影效果 */
        transition: transform 0.3s;
    }

    .overview-card:hover {
        transform: translateY(-5px);
    }

    .overview-card .title {
        font-size: 0.9rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.8);
    }

    .overview-card .count {
        font-size: 1.8rem;
        font-weight: 700;
        margin-top: 5px;
    }

    .overview-card .icon {
        font-size: 2rem;
        opacity: 0.8;
    }

    .feature-card {
        transition: transform 0.3s;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .icon-box {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .status-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        border-radius: 10px;
        font-weight: 600;
    }

    .status-bg-success {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-bg-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .status-bg-info {
        background-color: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }

    .status-bg-danger {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .issue-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .issue-item {
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
        border-start: 4px solid #6c757d;
        background-color: #f8f9fa;
    }

    .issue-item.priority-high {
        border-start-color: #dc3545;
    }

    .issue-item.priority-medium {
        border-start-color: #ffc107;
    }

    .issue-item.priority-low {
        border-start-color: #28a745;
    }

    .issue-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .issue-meta {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .chart-container {
        position: relative;
        margin: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <a href="{{ url_for('daily_management.print_daily_summary', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
               class="btn btn-primary print-btn {{ 'disabled' if not today_log else '' }}"
               {{ 'disabled' if not today_log else '' }}>
                <i class="fas fa-print"></i> 打印今日汇总
            </a>
        </div>
    </div>

    <!-- 日常管理中心卡片 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">
                <i class="fas fa-calendar-alt me-1"></i> 日常管理中心
            </h6>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-calendar-day me-1"></i> 进入今日日志
                </a>
                <p class="text-muted mt-2">管理日志、检查记录、陪餐记录等所有日常工作</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-star text-warning me-1"></i> 推荐使用</h5>
                            <p class="card-text">日常管理中心提供了更直观的日期导航体验，所有内容集中在一个页面，减少了页面跳转，让您可以看到整体情况。</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-magic text-info me-1"></i> 自动创建日志</h5>
                            <p class="card-text">无需手动创建日志，系统会自动为您处理，让您专注于记录重要内容。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化提示工具
        new bootstrap.Tooltip(document.querySelector('[data-bs-toggle="tooltip"]'));
    });
</script>
{% endblock %}
