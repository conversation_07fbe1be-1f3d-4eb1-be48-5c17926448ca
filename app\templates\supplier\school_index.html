{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if current_area %}
                            <small class="text-muted">
                                当前区域:
                                {% for area in area_path %}
                                <span class="badge bg-info">{{ area.get_level_name() }} - {{ area.name }}</span>
                                {% if not loop.last %} <i class="fas fa-chevron-right"></i> {% endif %}
                                {% endfor %}
                            </small>
                            {% endif %}
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier_school.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加关联
                            </a>
                            <a href="{{ url_for('supplier.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> 返回供应商列表
                            </a>
                            <a href="{{ url_for('supplier_category.index') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="supplier_id">供应商</label>
                                    <select class="form-control" id="supplier_id" name="supplier_id">
                                        <option value="">-- 所有供应商 --</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="area_id">学校</label>
                                    <select class="form-control" id="area_id" name="area_id">
                                        <option value="">-- 所有学校 --</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>
                                            {{ area.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="status">状态</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">-- 所有状态 --</option>
                                        <option value="1" {% if status == 1 %}selected{% endif %}>有效</option>
                                        <option value="0" {% if status == 0 %}selected{% endif %}>已终止</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" value="{{ keyword }}" placeholder="合同编号">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="mb-3">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 关联列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>供应商</th>
                                    <th>学校</th>
                                    <th>合同编号</th>
                                    <th>合作开始日期</th>
                                    <th>合作结束日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for relation in relations %}
                                <tr>
                                    <td>{{ relation.id }}</td>
                                    <td>{{ relation.supplier.name }}</td>
                                    <td>{{ relation.area.name }}</td>
                                    <td>{{ relation.contract_number or '-' }}</td>
                                    <td>{{  relation.start_date|format_datetime('%Y-%m-%d')  }}</td>
                                    <td>{{  relation.end_date|format_datetime('%Y-%m-%d') if relation.end_date else '长期'  }}</td>
                                    <td>
                                        {% if relation.status == 1 %}
                                        <span class="badge bg-success">有效</span>
                                        {% else %}
                                        <span class="badge bg-danger">已终止</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('supplier_school.edit', id=relation.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if relation.status == 1 %}
                                            <button type="button" class="btn btn-sm btn-warning terminate-btn" data-id="{{ relation.id }}">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                            {% else %}
                                            <button type="button" class="btn btn-sm btn-success activate-btn" data-id="{{ relation.id }}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ relation.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">暂无供应商-学校关联数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier_school.index', page=pagination.prev_num, supplier_id=supplier_id, area_id=area_id, status=status, keyword=keyword) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('supplier_school.index', page=page, supplier_id=supplier_id, area_id=area_id, status=status, keyword=keyword) }}">{{ page }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier_school.index', page=pagination.next_num, supplier_id=supplier_id, area_id=area_id, status=status, keyword=keyword) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个供应商-学校关联吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 终止确认模态框 -->
<div class="modal fade" id="terminateModal" tabindex="-1" role="dialog" aria-labelledby="terminateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="terminateModalLabel">确认终止</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要终止这个供应商-学校关联吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" id="confirmTerminate">确认终止</button>
            </div>
        </div>
    </div>
</div>

<!-- 激活确认模态框 -->
<div class="modal fade" id="activateModal" tabindex="-1" role="dialog" aria-labelledby="activateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="activateModalLabel">确认激活</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要重新激活这个供应商-学校关联吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmActivate">确认激活</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 获取CSRF令牌
        var csrfToken = $('meta[name=csrf-token]').attr('content');

        // 设置AJAX默认头部包含CSRF令牌
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrfToken);
                }
            }
        });

        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal.show();
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("supplier_school.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal.hide();
                    },
                    error: function(xhr) {
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            toastr.error(xhr.responseJSON.message);
                        } else {
                            toastr.error('删除失败，请稍后重试！');
                        }
                        $('#deleteModal').modal.hide();
                    }
                });
            }
        });

        // 终止功能
        var terminateId = null;

        $('.terminate-btn').click(function() {
            terminateId = $(this).data('id');
            $('#terminateModal').modal.show();
        });

        $('#confirmTerminate').click(function() {
            if (terminateId) {
                $.ajax({
                    url: '{{ url_for("supplier_school.terminate", id=0) }}'.replace('0', terminateId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#terminateModal').modal.hide();
                    },
                    error: function(xhr) {
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            toastr.error(xhr.responseJSON.message);
                        } else {
                            toastr.error('操作失败，请稍后重试！');
                        }
                        $('#terminateModal').modal.hide();
                    }
                });
            }
        });

        // 激活功能
        var activateId = null;

        $('.activate-btn').click(function() {
            activateId = $(this).data('id');
            $('#activateModal').modal.show();
        });

        $('#confirmActivate').click(function() {
            if (activateId) {
                $.ajax({
                    url: '{{ url_for("supplier_school.activate", id=0) }}'.replace('0', activateId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#activateModal').modal.hide();
                    },
                    error: function(xhr) {
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            toastr.error(xhr.responseJSON.message);
                        } else {
                            toastr.error('操作失败，请稍后重试！');
                        }
                        $('#activateModal').modal.hide();
                    }
                });
            }
        });
    });
</script>
{% endblock %}
