{% extends 'base.html' %}

{% block title %}仓库管理{% endblock %}

{% block page_title %}仓库管理{% endblock %}

{% block content %}
<div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">管理学校仓库信息，包括仓库基本信息、容量和状态</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('warehouse.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 创建仓库
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('warehouse.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label>区域</label>
                                    <select name="area_id" class="form-select">
                                        <option value="">全部</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>{{ area.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label>状态</label>
                                    <select name="status" class="form-select">
                                        <option value="">全部</option>
                                        <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                                        <option value="维护中" {% if status == '维护中' %}selected{% endif %}>维护中</option>
                                        <option value="已关闭" {% if status == '已关闭' %}selected{% endif %}>已关闭</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 仓库列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>区域</th>
                                    <th>位置</th>
                                    <th>管理员</th>
                                    <th>容量</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for warehouse in warehouses %}
                                <tr>
                                    <td>{{ warehouse.name }}</td>
                                    <td>{{ warehouse.area.name }}</td>
                                    <td>{{ warehouse.location }}</td>
                                    <td>{{ warehouse.manager.real_name or warehouse.manager.username }}</td>
                                    <td>{{ warehouse.capacity }} {{ warehouse.capacity_unit }}</td>
                                    <td>
                                        {% if warehouse.status == '正常' %}
                                        <span class="badge bg-success">正常</span>
                                        {% elif warehouse.status == '维护中' %}
                                        <span class="badge bg-warning">维护中</span>
                                        {% elif warehouse.status == '已关闭' %}
                                        <span class="badge bg-danger">已关闭</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('warehouse.view', id=warehouse.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        <a href="{{ url_for('warehouse.edit', id=warehouse.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无仓库数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('warehouse.index', page=pagination.prev_num, area_id=area_id, status=status) if pagination.has_prev else '#' }}">上一页</a>
                            </li>

                            {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == pagination.page %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('warehouse.index', page=page_num, area_id=area_id, status=status) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('warehouse.index', page=pagination.next_num, area_id=area_id, status=status) if pagination.has_next else '#' }}">下一页</a>
                            </li>
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
{% endblock %}
