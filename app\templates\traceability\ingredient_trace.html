{% extends 'base.html' %}

{% block title %}食材溯源详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-tools">
                        <a href="{{ url_for('traceability.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回溯源查询
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 食材基本信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary">
                                    <h4 class="card-title text-white">食材基本信息</h4>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 text-center">
                                            {% if ingredient.base_image %}
                                            <img src="{{ url_for('static', filename=ingredient.base_image) }}" alt="{{ ingredient.name }}" class="img-fluid img-thumbnail" style="max-height: 150px;">
                                            {% else %}
                                            <img src="{{ url_for('static', filename='img/no-image.png') }}" alt="无图片" class="img-fluid img-thumbnail" style="max-height: 150px;">
                                            {% endif %}
                                        </div>
                                        <div class="col-md-8">
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th class="w-30">名称</th>
                                                    <td>{{ ingredient.name }}</td>
                                                </tr>
                                                <tr>
                                                    <th>分类</th>
                                                    <td>{{ ingredient.category_rel.name if ingredient.category_rel else ingredient.category }}</td>
                                                </tr>
                                                <tr>
                                                    <th>单位</th>
                                                    <td>{{ ingredient.unit }}</td>
                                                </tr>
                                                <tr>
                                                    <th>规格</th>
                                                    <td>{{ ingredient.specification or '无' }}</td>
                                                </tr>
                                                <tr>
                                                    <th>保质期</th>
                                                    <td>{{ ingredient.shelf_life or '无' }} 天</td>
                                                </tr>
                                                <tr>
                                                    <th>存储条件</th>
                                                    <td>{{ ingredient.storage_condition or '无' }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success">
                                    <h4 class="card-title text-white">批次统计</h4>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-box bg-info">
                                                <span class="info-box-icon"><i class="fas fa-box"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">批次总数</span>
                                                    <span class="info-box-number">{{  batches|length  }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-box bg-success">
                                                <span class="info-box-icon"><i class="fas fa-warehouse"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">当前库存</span>
                                                    <span class="info-box-number">
                                                        {% set total_stock = namespace(value=0) %}
                                                        {% for batch in batches %}
                                                            {% set total_stock.value = total_stock.value + batch.current_quantity %}
                                                        {% endfor %}
                                                        {{ total_stock.value }} {{ ingredient.unit }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="info-box bg-warning">
                                                <span class="info-box-icon"><i class="fas fa-exclamation-triangle"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">预警批次</span>
                                                    <span class="info-box-number">
                                                        {% set warning_count = namespace(value=0) %}
                                                        {% for batch in batches %}
                                                            {% if batch.status == '预警' %}
                                                                {% set warning_count.value = warning_count.value + 1 %}
                                                            {% endif %}
                                                        {% endfor %}
                                                        {{ warning_count.value }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-box bg-danger">
                                                <span class="info-box-icon"><i class="fas fa-calendar-times"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">过期批次</span>
                                                    <span class="info-box-number">
                                                        {% set expired_count = namespace(value=0) %}
                                                        {% for batch in batches %}
                                                            {% if batch.status == '过期' %}
                                                                {% set expired_count.value = expired_count.value + 1 %}
                                                            {% endif %}
                                                        {% endfor %}
                                                        {{ expired_count.value }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批次列表 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-primary">
                                    <h4 class="card-title text-white">批次列表</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>批次号</th>
                                                    <th>供应商</th>
                                                    <th>生产日期</th>
                                                    <th>过期日期</th>
                                                    <th>当前库存</th>
                                                    <th>单位</th>
                                                    <th>状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for batch in batches %}
                                                <tr>
                                                    <td>{{ batch.batch_number }}</td>
                                                    <td>{{ batch.supplier.name }}</td>
                                                    <td>{{ batch.production_date }}</td>
                                                    <td>{{ batch.expiry_date }}</td>
                                                    <td>{{ batch.current_quantity }}</td>
                                                    <td>{{ batch.unit }}</td>
                                                    <td>
                                                        {% if batch.status == '正常' %}
                                                        <span class="badge bg-success">正常</span>
                                                        {% elif batch.status == '预警' %}
                                                        <span class="badge bg-warning">预警</span>
                                                        {% elif batch.status == '过期' %}
                                                        <span class="badge bg-danger">过期</span>
                                                        {% elif batch.status == '已用完' %}
                                                        <span class="badge bg-secondary">已用完</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <a href="{{ url_for('traceability.trace_batch', batch_number=batch.batch_number) }}" class="btn btn-info btn-sm">
                                                            <i class="fas fa-search"></i> 溯源
                                                        </a>
                                                        <a href="{{ url_for('material_batch.view', id=batch.id) }}" class="btn btn-primary btn-sm">
                                                            <i class="fas fa-eye"></i> 详情
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="8" class="text-center">暂无批次数据</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
