<!-- 检查项展示小组件，用于首页或其他页面嵌入 -->
<div class="inspection-widget" id="inspection-widget-{{ widget_id|default('default') }}">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold text-primary">
                <i class="fas fa-clipboard-check"></i> {{ title|default('食堂检查记录') }}
            </h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">操作:</div>
                    <a class="dropdown-item" href="{{ url_for('daily_management.daily_logs') }}">
                        <i class="fas fa-list fa-sm fa-fw me-2 text-gray-400"></i>
                        查看所有记录
                    </a>
                    <a class="dropdown-item refresh-widget" href="javascript:void(0);">
                        <i class="fas fa-sync fa-sm fa-fw me-2 text-gray-400"></i>
                        刷新数据
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="inspection-content">
                <!-- 数据加载中占位符 -->
                <div class="text-center py-4 loading-placeholder">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载检查记录...</p>
                </div>
                
                <!-- 数据将通过 AJAX 加载到这里 -->
                <div class="inspection-data" style="display: none;"></div>
                
                <!-- 错误提示 -->
                <div class="alert alert-danger error-message" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i> <span class="error-text"></span>
                </div>
                
                <!-- 无数据提示 -->
                <div class="alert alert-info no-data-message" style="display: none;">
                    <i class="fas fa-info-circle"></i> 暂无检查记录
                </div>
            </div>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化检查项展示小组件
        initInspectionWidget('{{ widget_id|default("default") }}', {{ inspection_id|default('null') }}, '{{ inspection_type|default("") }}', '{{ inspection_item|default("") }}', {{ days|default(1) }});
    });
    
    // 初始化检查项展示小组件
    function initInspectionWidget(widgetId, inspectionId, inspectionType, inspectionItem, days) {
        const widget = document.getElementById(`inspection-widget-${widgetId}`);
        if (!widget) return;
        
        const contentEl = widget.querySelector('.inspection-content');
        const loadingEl = widget.querySelector('.loading-placeholder');
        const dataEl = widget.querySelector('.inspection-data');
        const errorEl = widget.querySelector('.error-message');
        const errorTextEl = widget.querySelector('.error-text');
        const noDataEl = widget.querySelector('.no-data-message');
        
        // 刷新按钮点击事件
        widget.querySelector('.refresh-widget').addEventListener('click', function() {
            loadInspectionData();
        });
        
        // 加载检查项数据
        function loadInspectionData() {
            // 显示加载中
            loadingEl.style.display = 'block';
            dataEl.style.display = 'none';
            errorEl.style.display = 'none';
            noDataEl.style.display = 'none';
            
            let url = '';
            
            // 根据参数构建URL
            if (inspectionId) {
                // 如果指定了检查项ID，直接获取该检查项
                url = `/daily-management/inspections/display-html/${inspectionId}`;
            } else {
                // 否则，根据类型和项目名称获取最近的检查项
                url = `/daily-management/inspections/latest-html?days=${days}`;
                if (inspectionType) {
                    url += `&type=${inspectionType}`;
                }
                if (inspectionItem) {
                    url += `&item=${encodeURIComponent(inspectionItem)}`;
                }
            }
            
            // 发送请求
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    // 隐藏加载中
                    loadingEl.style.display = 'none';
                    
                    // 检查是否有数据
                    if (html.trim() === '' || html.includes('暂无检查记录')) {
                        noDataEl.style.display = 'block';
                    } else {
                        // 显示数据
                        dataEl.innerHTML = html;
                        dataEl.style.display = 'block';
                    }
                })
                .catch(error => {
                    // 显示错误信息
                    loadingEl.style.display = 'none';
                    errorTextEl.textContent = `加载失败: ${error.message}`;
                    errorEl.style.display = 'block';
                    console.error('加载检查项数据失败:', error);
                });
        }
        
        // 初始加载数据
        loadInspectionData();
    }
</script>
